package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.UserPackage;
import org.apache.ibatis.annotations.Param;

/**
 * 用户购买的套餐和加油包相关信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface UserPackageMapper 
{
    /**
     * 查询用户购买的套餐和加油包相关信息
     * 
     * @param userPackageId 用户购买的套餐和加油包相关信息主键
     * @return 用户购买的套餐和加油包相关信息
     */
    public UserPackage selectUserPackageByUserPackageId(String userPackageId);

    /**
     * 查询用户购买的套餐和加油包相关信息列表
     * 
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 用户购买的套餐和加油包相关信息集合
     */
    public List<UserPackage> selectUserPackageList(UserPackage userPackage);

    /**
     * 新增用户购买的套餐和加油包相关信息
     * 
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 结果
     */
    public int insertUserPackage(UserPackage userPackage);

    /**
     * 扣减积分
     * @param packageScore 扣减的积分数
     * @param userPackageId 套餐ID
     * @return
     */
    int updatePackageScore(@Param("packageScore") long packageScore, @Param("userPackageId") String userPackageId);

    /**
     * 修改用户购买的套餐和加油包相关信息
     * 
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 结果
     */
    public int updateUserPackage(UserPackage userPackage);

    /**
     * 删除用户购买的套餐和加油包相关信息
     * 
     * @param userPackageId 用户购买的套餐和加油包相关信息主键
     * @return 结果
     */
    public int deleteUserPackageByUserPackageId(String userPackageId);

    /**
     * 批量删除用户购买的套餐和加油包相关信息
     * 
     * @param userPackageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserPackageByUserPackageIds(String[] userPackageIds);

    /**
     * 修改用户购买的套餐和加油包相关信息
     *
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 结果
     */
    public int updateUserPackageByUserId(UserPackage userPackage);
}
