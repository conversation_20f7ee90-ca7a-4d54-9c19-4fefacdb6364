package com.dataxai.web.service;

import java.util.List;

import com.dataxai.common.core.Dto.RefuelingBagDto;
import com.dataxai.web.domain.UserRefueligBag;

/**
 * 用户购买的加油包相关信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-23
 */
public interface IUserRefueligBagService 
{
    /**
     * 查询用户购买的加油包相关信息
     * 
     * @param userRefuelingBagId 用户购买的加油包相关信息主键
     * @return 用户购买的加油包相关信息
     */
    public UserRefueligBag selectUserRefueligBagByUserRefuelingBagId(String userRefuelingBagId);

    /**
     * 查询用户购买的加油包相关信息列表
     * 
     * @param userRefueligBag 用户购买的加油包相关信息
     * @return 用户购买的加油包相关信息集合
     */
    public List<UserRefueligBag> selectUserRefueligBagList(UserRefueligBag userRefueligBag);

    /**
     * 新增用户购买的加油包相关信息
     * 
     * @param userRefueligBag 用户购买的加油包相关信息
     * @return 结果
     */
    public int insertUserRefueligBag(UserRefueligBag userRefueligBag);

    /**
     * 修改用户购买的加油包相关信息
     * 
     * @param userRefueligBag 用户购买的加油包相关信息
     * @return 结果
     */
    public int updateUserRefueligBag(UserRefueligBag userRefueligBag);

    /**
     * 批量删除用户购买的加油包相关信息
     * 
     * @param userRefuelingBagIds 需要删除的用户购买的加油包相关信息主键集合
     * @return 结果
     */
    public int deleteUserRefueligBagByUserRefuelingBagIds(String[] userRefuelingBagIds);

    /**
     * 删除用户购买的加油包相关信息信息
     * 
     * @param userRefuelingBagId 用户购买的加油包相关信息主键
     * @return 结果
     */
    public int deleteUserRefueligBagByUserRefuelingBagId(String userRefuelingBagId);

    Integer addByUserId(RefuelingBagDto dto);

}
