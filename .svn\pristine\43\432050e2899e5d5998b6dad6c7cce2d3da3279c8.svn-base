package com.dataxai.web.controller.admincontroller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.TBaseinfoManage;
import com.dataxai.web.service.ITBaseinfoManageService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 公司基础信息管理Controller
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@RestController
@RequestMapping("/baseinfo/manage")
public class TBaseinfoManageController extends BaseController
{
    @Autowired
    private ITBaseinfoManageService tBaseinfoManageService;

    /**
     * 查询公司基础信息管理列表
     */
    @PreAuthorize("@ss.hasPermi('baseinfo:manage:list')")
    @GetMapping("/list")
    public TableDataInfo list(TBaseinfoManage tBaseinfoManage)
    {
        startPage();
        List<TBaseinfoManage> list = tBaseinfoManageService.selectTBaseinfoManageList(tBaseinfoManage);
        return getDataTable(list);
    }

    /**
     * 导出公司基础信息管理列表
     */
    @PreAuthorize("@ss.hasPermi('baseinfo:manage:export')")
    @Log(title = "公司基础信息管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TBaseinfoManage tBaseinfoManage)
    {
        List<TBaseinfoManage> list = tBaseinfoManageService.selectTBaseinfoManageList(tBaseinfoManage);
        ExcelUtil<TBaseinfoManage> util = new ExcelUtil<TBaseinfoManage>(TBaseinfoManage.class);
        util.exportExcel(response, list, "公司基础信息管理数据");
    }

    /**
     * 获取公司基础信息管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('baseinfo:manage:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tBaseinfoManageService.selectTBaseinfoManageById(id));
    }

    /**
     * 新增公司基础信息管理
     */
    @PreAuthorize("@ss.hasPermi('baseinfo:manage:add')")
    @Log(title = "公司基础信息管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TBaseinfoManage tBaseinfoManage)
    {
        return toAjax(tBaseinfoManageService.insertTBaseinfoManage(tBaseinfoManage));
    }

    /**
     * 修改公司基础信息管理
     */
    @PreAuthorize("@ss.hasPermi('baseinfo:manage:edit')")
    @Log(title = "公司基础信息管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TBaseinfoManage tBaseinfoManage)
    {
        return toAjax(tBaseinfoManageService.updateTBaseinfoManage(tBaseinfoManage));
    }

    /**
     * 删除公司基础信息管理
     */
    @PreAuthorize("@ss.hasPermi('baseinfo:manage:remove')")
    @Log(title = "公司基础信息管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tBaseinfoManageService.deleteTBaseinfoManageByIds(ids));
    }
}
