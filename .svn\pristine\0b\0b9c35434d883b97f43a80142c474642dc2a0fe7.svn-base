<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.mapper.ProductInfoMapper">
    
    <resultMap type="ProductInfo" id="ProductInfoResult">
        <result property="id"    column="id"    />
        <result property="productTitle"    column="product_title"    />
        <result property="productPrice"    column="product_price"    />
        <result property="productImageUrl"    column="product_image_url"    />
        <result property="originalImageUrl"    column="original_image_url"    />
        <result property="sourcePlatform"    column="source_platform"    />
        <result property="ownerId"    column="owner_id"    />
        <result property="ownerName"    column="owner_name"    />
        <result property="commentNum"    column="comment_num"    />
        <result property="starLevel"    column="star_level"    />
        <result property="riskLevel"    column="risk_level"    />
        <result property="elements"    column="elements"    />
        <result property="suggestion"    column="suggestion"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectProductInfoVo">
        select id, product_title, product_price, product_image_url, original_image_url, source_platform, owner_id, owner_name, comment_num, star_level, risk_level, elements, suggestion, create_time, update_time, status, remark from product_info
    </sql>

    <select id="selectProductInfoList" parameterType="ProductInfo" resultMap="ProductInfoResult">
        <include refid="selectProductInfoVo"/>
        <where>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            <if test="productTitle != null  and productTitle != ''"> and product_title like concat('%', #{productTitle}, '%')</if>
            <if test="sourcePlatform != null  and sourcePlatform != ''"> and source_platform = #{sourcePlatform}</if>
            <if test="ownerId != null "> and owner_id = #{ownerId}</if>
            <if test="starLevel != null "> and star_level = #{starLevel}</if>
            <if test="riskLevel != null  and riskLevel != ''"> and risk_level = #{riskLevel}</if>
        </where>
    </select>
    
    <select id="selectProductInfoById" parameterType="Long" resultMap="ProductInfoResult">
        <include refid="selectProductInfoVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertProductInfo" parameterType="ProductInfo" useGeneratedKeys="true" keyProperty="id">
        insert into product_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productTitle != null and productTitle != ''">product_title,</if>
            <if test="productPrice != null">product_price,</if>
            <if test="productImageUrl != null and productImageUrl != ''">product_image_url,</if>
            <if test="originalImageUrl != null and originalImageUrl != ''">original_image_url,</if>
            <if test="sourcePlatform != null and sourcePlatform != ''">source_platform,</if>
            <if test="ownerId != null">owner_id,</if>
            <if test="ownerName != null and ownerName != ''">owner_name,</if>
            <if test="commentNum != null">comment_num,</if>
            <if test="starLevel != null">star_level,</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level,</if>
            <if test="elements != null">elements,</if>
            <if test="suggestion != null">suggestion,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="status != null">status,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productTitle != null and productTitle != ''">#{productTitle},</if>
            <if test="productPrice != null">#{productPrice},</if>
            <if test="productImageUrl != null and productImageUrl != ''">#{productImageUrl},</if>
            <if test="originalImageUrl != null and originalImageUrl != ''">#{originalImageUrl},</if>
            <if test="sourcePlatform != null and sourcePlatform != ''">#{sourcePlatform},</if>
            <if test="ownerId != null">#{ownerId},</if>
            <if test="ownerName != null and ownerName != ''">#{ownerName},</if>
            <if test="commentNum != null">#{commentNum},</if>
            <if test="starLevel != null">#{starLevel},</if>
            <if test="riskLevel != null and riskLevel != ''">#{riskLevel},</if>
            <if test="elements != null">#{elements},</if>
            <if test="suggestion != null">#{suggestion},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateProductInfo" parameterType="ProductInfo">
        update product_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="productTitle != null and productTitle != ''">product_title = #{productTitle},</if>
            <if test="productPrice != null">product_price = #{productPrice},</if>
            <if test="productImageUrl != null and productImageUrl != ''">product_image_url = #{productImageUrl},</if>
            <if test="originalImageUrl != null and originalImageUrl != ''">original_image_url = #{originalImageUrl},</if>
            <if test="sourcePlatform != null and sourcePlatform != ''">source_platform = #{sourcePlatform},</if>
            <if test="ownerId != null">owner_id = #{ownerId},</if>
            <if test="ownerName != null and ownerName != ''">owner_name = #{ownerName},</if>
            <if test="commentNum != null">comment_num = #{commentNum},</if>
            <if test="starLevel != null">star_level = #{starLevel},</if>
            <if test="riskLevel != null and riskLevel != ''">risk_level = #{riskLevel},</if>
            <if test="elements != null">elements = #{elements},</if>
            <if test="suggestion != null">suggestion = #{suggestion},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteProductInfoById" parameterType="Long">
        delete from product_info where id = #{id}
    </delete>

    <delete id="deleteProductInfoByIds" parameterType="String">
        delete from product_info where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>