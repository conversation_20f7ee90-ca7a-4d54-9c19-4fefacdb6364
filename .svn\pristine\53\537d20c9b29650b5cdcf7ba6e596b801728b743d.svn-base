package com.dataxai.web.mapper;

import com.dataxai.web.entity.PushMessage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 */
public interface PushMessageMapper {



    void insert(PushMessage pushMessage);

    void updateById(PushMessage updatePushMessage);


    @Select("select * from push_message where user_id = #{userId} and push_state = 0")
    List<PushMessage> findByUserIdAndPushWait(@Param("userId") Long userId);


    void updatePushSuccessByUserId(@Param("userId") Long userId);
}
