package com.dataxai.web.service.impl;

import java.util.List;
import com.dataxai.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.TTariffPackageSubMapper;
import com.dataxai.web.domain.TTariffPackageSub;
import com.dataxai.web.service.ITTariffPackageSubService;

/**
 * 子套餐Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
public class TTariffPackageSubServiceImpl implements ITTariffPackageSubService 
{
    @Autowired
    private TTariffPackageSubMapper tTariffPackageSubMapper;

    /**
     * 查询子套餐
     * 
     * @param tariffPackageSubId 子套餐主键
     * @return 子套餐
     */
    @Override
    public TTariffPackageSub selectTTariffPackageSubByTariffPackageSubId(String tariffPackageSubId)
    {
        return tTariffPackageSubMapper.selectTTariffPackageSubByTariffPackageSubId(tariffPackageSubId);
    }

    /**
     * 查询子套餐列表
     * 
     * @param tTariffPackageSub 子套餐
     * @return 子套餐
     */
    @Override
    public List<TTariffPackageSub> selectTTariffPackageSubList(TTariffPackageSub tTariffPackageSub)
    {
        return tTariffPackageSubMapper.selectTTariffPackageSubList(tTariffPackageSub);
    }

    /**
     * 新增子套餐
     * 
     * @param tTariffPackageSub 子套餐
     * @return 结果
     */
    @Override
    public int insertTTariffPackageSub(TTariffPackageSub tTariffPackageSub)
    {
        tTariffPackageSub.setCreateTime(DateUtils.getNowDate());
        return tTariffPackageSubMapper.insertTTariffPackageSub(tTariffPackageSub);
    }

    /**
     * 修改子套餐
     * 
     * @param tTariffPackageSub 子套餐
     * @return 结果
     */
    @Override
    public int updateTTariffPackageSub(TTariffPackageSub tTariffPackageSub)
    {
        tTariffPackageSub.setUpdateTime(DateUtils.getNowDate());
        return tTariffPackageSubMapper.updateTTariffPackageSub(tTariffPackageSub);
    }

    /**
     * 批量删除子套餐
     * 
     * @param tariffPackageSubIds 需要删除的子套餐主键
     * @return 结果
     */
    @Override
    public int deleteTTariffPackageSubByTariffPackageSubIds(String[] tariffPackageSubIds)
    {
        return tTariffPackageSubMapper.deleteTTariffPackageSubByTariffPackageSubIds(tariffPackageSubIds);
    }

    /**
     * 删除子套餐信息
     * 
     * @param tariffPackageSubId 子套餐主键
     * @return 结果
     */
    @Override
    public int deleteTTariffPackageSubByTariffPackageSubId(String tariffPackageSubId)
    {
        return tTariffPackageSubMapper.deleteTTariffPackageSubByTariffPackageSubId(tariffPackageSubId);
    }
}
