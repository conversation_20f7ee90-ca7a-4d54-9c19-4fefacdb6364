package com.dataxai.web.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 4K图生成信息对象 Web_4k_process
 * 
 * <AUTHOR>
 * @date 2024-02-02
 */
@ApiModel(value = "Web4kProcess", description = "4K图生成信息对象 Web_4k_process")
public class Web4kProcess extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 执行得次数 */
    @Excel(name = "执行得次数")
    @ApiModelProperty(value = "执行得次数")
    private Long ordinal;

    /** 需要转成4K图的连接 */
    @Excel(name = "需要转成4K图的连接")
    @ApiModelProperty(value = "需要转成4K图的连接")
    private String originImgUrl;

    /** 任务状态 0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中 */
    @Excel(name = "任务状态 0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中")
    @ApiModelProperty(value = "任务状态 0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中")
    private Long status;

    /** 所属的任务id */
    @Excel(name = "所属的任务id")
    @ApiModelProperty(value = "所属的任务id")
    private String taskId;

    /** 唯一确定ID */
    @Excel(name = "唯一确定ID")
    @ApiModelProperty(value = "唯一确定ID")
    private String taskOrdinalId;

    /** 用户id */
    @Excel(name = "用户id")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /** 是否启动4k图生成 */
    @Excel(name = "是否启动4k图生成")
    @ApiModelProperty(value = "是否启动4k图生成")
    private Integer isEnhance;

    /** 4k图完成后的地址 */
    @Excel(name = "4k图完成后的地址")
    @ApiModelProperty(value = "4k图完成后的地址")
    private String ordinalImgResultList;

    public void setOrdinal(Long ordinal) 
    {
        this.ordinal = ordinal;
    }

    public Long getOrdinal() 
    {
        return ordinal;
    }
    public void setOriginImgUrl(String originImgUrl) 
    {
        this.originImgUrl = originImgUrl;
    }

    public String getOriginImgUrl() 
    {
        return originImgUrl;
    }
    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }
    public void setTaskId(String taskId) 
    {
        this.taskId = taskId;
    }

    public String getTaskId() 
    {
        return taskId;
    }
    public void setTaskOrdinalId(String taskOrdinalId) 
    {
        this.taskOrdinalId = taskOrdinalId;
    }

    public String getTaskOrdinalId() 
    {
        return taskOrdinalId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setIsEnhance(Integer isEnhance) 
    {
        this.isEnhance = isEnhance;
    }

    public Integer getIsEnhance() 
    {
        return isEnhance;
    }
    public void setOrdinalImgResultList(String ordinalImgResultList) 
    {
        this.ordinalImgResultList = ordinalImgResultList;
    }

    public String getOrdinalImgResultList() 
    {
        return ordinalImgResultList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ordinal", getOrdinal())
            .append("originImgUrl", getOriginImgUrl())
            .append("status", getStatus())
            .append("taskId", getTaskId())
            .append("taskOrdinalId", getTaskOrdinalId())
            .append("userId", getUserId())
            .append("isEnhance", getIsEnhance())
            .append("ordinalImgResultList", getOrdinalImgResultList())
            .toString();
    }
}
