<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.TUserCommentMapper">
    <resultMap type="TUserComment" id="TUserCommentResult">
        <result property="commentId" column="comment_id"/>
        <result property="userId" column="user_id"/>
        <result property="taskId" column="task_id"/>
        <result property="commentContent" column="comment_content"/>
        <result property="commentTime" column="comment_time"/>
    </resultMap>

    <sql id="selectTUserCommentVo">
        select comment_id, user_id, task_id, comment_content, comment_time
        from t_user_comment
    </sql>

    <select id="selectTUserCommentList" parameterType="TUserComment" resultMap="TUserCommentResult">
        <include refid="selectTUserCommentVo"/>
        <where>
            <if test="userId != null  and userId != ''">and user_id = #{userId}</if>
            <if test="taskId != null  and taskId != ''">and task_id = #{taskId}</if>
            <if test="commentContent != null  and commentContent != ''">and comment_content = #{commentContent}</if>
            <if test="commentTime != null ">and comment_time = #{commentTime}</if>
        </where>
    </select>

    <select id="selectTUserCommentByCommentId" parameterType="String" resultMap="TUserCommentResult">
        <include refid="selectTUserCommentVo"/>
        where comment_id = #{commentId}
    </select>

    <insert id="insertTUserComment" parameterType="TUserComment">
        insert into t_user_comment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="commentId != null">comment_id,</if>
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="commentContent != null">comment_content,</if>
            <if test="commentTime != null">comment_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="commentId != null">#{commentId},</if>
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="commentContent != null">#{commentContent},</if>
            <if test="commentTime != null">#{commentTime},</if>
        </trim>
    </insert>

    <update id="updateTUserComment" parameterType="TUserComment">
        update t_user_comment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="commentContent != null">comment_content = #{commentContent},</if>
            <if test="commentTime != null">comment_time = #{commentTime},</if>
        </trim>
        where comment_id = #{commentId}
    </update>

    <delete id="deleteTUserCommentByCommentId" parameterType="String">
        delete
        from t_user_comment
        where comment_id = #{commentId}
    </delete>

    <delete id="deleteTUserCommentByCommentIds" parameterType="String">
        delete from t_user_comment where comment_id in
        <foreach item="commentId" collection="array" open="(" separator="," close=")">
            #{commentId}
        </foreach>
    </delete>
</mapper>