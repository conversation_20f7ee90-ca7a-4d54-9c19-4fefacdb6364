<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item
				label="名称"
				prop="disclaimersName"
			>
				<el-input
					v-model="queryParams.disclaimersName"
					placeholder="请输入名称"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item>
				<el-button
					type="primary"
					icon="el-icon-search"
					size="mini"
					@click="handleQuery"
					>搜索</el-button
				>
				<el-button
					icon="el-icon-refresh"
					size="mini"
					@click="resetQuery"
					>重置</el-button
				>
			</el-form-item>
		</el-form>

		<!-- <el-row
			:gutter="10"
			class="mb8"
		>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['baseinfo:disclaimers:add']"
					type="primary"
					plain
					icon="el-icon-plus"
					size="mini"
					@click="handleAdd"
					>新增</el-button
				>
			</el-col>

			<right-toolbar
				:show-search.sync="showSearch"
				@queryTable="getList"
			/>
		</el-row> -->

		<el-table
			v-loading="loading"
			:data="disclaimersList"
			@selection-change="handleSelectionChange"
		>
			<el-table-column
				type="selection"
				width="55"
				align="center"
			/>
			<el-table-column
				label="id"
				align="center"
				prop="id"
			/>
			<el-table-column
				label="名称"
				align="center"
				prop="disclaimersName"
			/>
			<!-- <el-table-column
				label="内容"
				align="center"
				prop="disclaimersContent"
			/> -->
			<el-table-column
				label="操作"
				align="center"
				class-name="small-padding fixed-width"
				width="100"
			>
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['baseinfo:disclaimers:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
						>修改</el-button
					>
					<!-- <el-button
						v-hasPermi="['baseinfo:disclaimers:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					> -->
				</template>
			</el-table-column>
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改免责声明对话框 -->
		<el-dialog
			:title="title"
			:visible.sync="open"
			width="900px"
			append-to-body
		>
			<el-form
				ref="form"
				:model="form"
				:rules="rules"
				label-width="80px"
			>
				<el-form-item
					label="名称"
					prop="disclaimersName"
				>
					<el-input
						v-model="form.disclaimersName"
						placeholder="请输入名称"
					/>
				</el-form-item>
				<el-form-item
					label="内容"
					prop="disclaimersContent"
				>
					<editor
						v-model="form.disclaimersContent"
						:min-height="192"
					/>
				</el-form-item>
			</el-form>
			<div
				slot="footer"
				class="dialog-footer"
			>
				<el-button
					type="primary"
					@click="submitForm"
					>确 定</el-button
				>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	listDisclaimers,
	getDisclaimers,
	delDisclaimers,
	addDisclaimers,
	updateDisclaimers
} from '@/api/baseinfo/disclaimers'

export default {
	name: 'Disclaimers',
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 免责声明表格数据
			disclaimersList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				disclaimersName: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {}
		}
	},
	created() {
		this.getList()
	},
	methods: {
		/** 查询免责声明列表 */
		getList() {
			this.loading = true
			listDisclaimers(this.queryParams).then((response) => {
				this.disclaimersList = response.data
				this.total = response.total
				this.loading = false
			})
		},
		// 取消按钮
		cancel() {
			this.open = false
			this.reset()
		},
		// 表单重置
		reset() {
			this.form = {
				id: null,
				disclaimersName: null,
				disclaimersContent: null
			}
			this.resetForm('form')
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm('queryForm')
			this.handleQuery()
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.id)
			this.single = selection.length !== 1
			this.multiple = !selection.length
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset()
			this.open = true
			this.title = '添加免责声明'
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const id = row.id || this.ids
			getDisclaimers(id).then((response) => {
				this.form = response.data
				this.open = true
				this.title = '修改免责声明'
			})
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.form.id != null) {
						updateDisclaimers(this.form).then((response) => {
							this.$modal.msgSuccess('修改成功')
							this.open = false
							this.getList()
						})
					} else {
						addDisclaimers(this.form).then((response) => {
							this.$modal.msgSuccess('新增成功')
							this.open = false
							this.getList()
						})
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const ids = row.id || this.ids
			this.$modal
				.confirm('是否确认删除免责声明编号为"' + ids + '"的数据项？')
				.then(function () {
					return delDisclaimers(ids)
				})
				.then(() => {
					this.getList()
					this.$modal.msgSuccess('删除成功')
				})
				.catch(() => {})
		},
		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'baseinfo/disclaimers/export',
				{
					...this.queryParams
				},
				`disclaimers_${new Date().getTime()}.xlsx`
			)
		}
	}
}
</script>
