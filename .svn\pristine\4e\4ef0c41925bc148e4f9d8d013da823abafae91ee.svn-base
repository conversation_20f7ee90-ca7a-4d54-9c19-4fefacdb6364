package com.dataxai.web.service.impl;

import java.util.Date;
import java.util.List;
import com.dataxai.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.FavoriteMapper;
import com.dataxai.web.domain.Favorite;
import com.dataxai.web.service.IFavoriteService;

/**
 * 图片收藏Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-13
 */
@Service
public class FavoriteServiceImpl implements IFavoriteService 
{
    @Autowired
    private FavoriteMapper favoriteMapper;

    /**
     * 查询图片收藏
     * 
     * @param favoriteId 图片收藏主键
     * @return 图片收藏
     */
    @Override
    public Favorite selectFavoriteByFavoriteId(String favoriteId)
    {
        return favoriteMapper.selectFavoriteByFavoriteId(favoriteId);
    }

    @Override
    public List<Favorite> selectFavoriteListByUserId(Long userId) {
        return favoriteMapper.selectFavoriteListByUserId(userId);
    }

    /**
     * 查询图片收藏列表
     * 
     * @param favorite 图片收藏
     * @return 图片收藏
     */
    @Override
    public List<Favorite> selectFavoriteList(Favorite favorite)
    {
        return favoriteMapper.selectFavoriteList(favorite);
    }

    /**
     * 新增图片收藏
     * 
     * @param favorite 图片收藏
     * @return 结果
     */
    @Override
    public int insertFavorite(Favorite favorite)
    {
        Date nowDate = DateUtils.getNowDate();
        favorite.setCreateTime(nowDate);
        favorite.setUpdateTime(nowDate);
        favorite.setDelFlag(0L);
        return favoriteMapper.insertFavorite(favorite);
    }

    /**
     * 修改图片收藏
     * 
     * @param favorite 图片收藏
     * @return 结果
     */
    @Override
    public int updateFavorite(Favorite favorite)
    {
        favorite.setUpdateTime(DateUtils.getNowDate());
        return favoriteMapper.updateFavorite(favorite);
    }

    /**
     * 批量删除图片收藏
     * 
     * @param favoriteIds 需要删除的图片收藏主键
     * @return 结果
     */
    @Override
    public int deleteFavoriteByFavoriteIds(Long[] favoriteIds)
    {
        return favoriteMapper.deleteFavoriteByFavoriteIds(favoriteIds);
    }

    /**
     * 删除图片收藏信息
     * 
     * @param favoriteId 图片收藏主键
     * @return 结果
     */
    @Override
    public int deleteFavoriteByFavoriteId(Long favoriteId)
    {
        return favoriteMapper.deleteFavoriteByFavoriteId(favoriteId);
    }
}
