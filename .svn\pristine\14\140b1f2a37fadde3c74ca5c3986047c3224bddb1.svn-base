package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.TBaseinfoManage;

/**
 * 公司基础信息管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface TBaseinfoManageMapper 
{
    /**
     * 查询公司基础信息管理
     * 
     * @param id 公司基础信息管理主键
     * @return 公司基础信息管理
     */
    public TBaseinfoManage selectTBaseinfoManageById(Long id);

    /**
     * 查询公司基础信息管理列表
     * 
     * @param tBaseinfoManage 公司基础信息管理
     * @return 公司基础信息管理集合
     */
    public List<TBaseinfoManage> selectTBaseinfoManageList(TBaseinfoManage tBaseinfoManage);

    /**
     * 新增公司基础信息管理
     * 
     * @param tBaseinfoManage 公司基础信息管理
     * @return 结果
     */
    public int insertTBaseinfoManage(TBaseinfoManage tBaseinfoManage);

    /**
     * 修改公司基础信息管理
     * 
     * @param tBaseinfoManage 公司基础信息管理
     * @return 结果
     */
    public int updateTBaseinfoManage(TBaseinfoManage tBaseinfoManage);

    /**
     * 删除公司基础信息管理
     * 
     * @param id 公司基础信息管理主键
     * @return 结果
     */
    public int deleteTBaseinfoManageById(Long id);

    /**
     * 批量删除公司基础信息管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTBaseinfoManageByIds(Long[] ids);
}
