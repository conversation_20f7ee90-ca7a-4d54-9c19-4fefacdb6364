<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.DefindedCharacterMapper">
    
    <resultMap type="DefindedCharacter" id="DefindedCharacterResult">
        <result property="characterId"    column="character_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="userId"    column="user_id"    />
        <result property="imgUrl"    column="img_url"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="taskOrdinalId"    column="task_ordinal_id"    />
    </resultMap>

    <sql id="selectDefindedCharacterVo">
        select character_id, task_id, user_id, img_url, create_time, create_by, update_time, update_by,task_ordinal_id from t_definded_character
    </sql>

    <select id="selectDefindedCharacterList" parameterType="DefindedCharacter" resultMap="DefindedCharacterResult">
        <include refid="selectDefindedCharacterVo"/>
        <where>  
            <if test="taskId != null  and taskId != ''"> and task_id = #{taskId}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="imgUrl != null  and imgUrl != ''"> and img_url = #{imgUrl}</if>
            <if test="taskOrdinalId != null  and taskOrdinalId != ''"> and task_ordinal_id = #{taskOrdinalId}</if>
        </where>
    </select>
    
    <select id="selectDefindedCharacterByTaskId" parameterType="String" resultMap="DefindedCharacterResult">
        <include refid="selectDefindedCharacterVo"/>
        where task_id = #{taskId}
    </select>

    <select id="selectDefindedCharacterByTaskOrdinalId" parameterType="String" resultMap="DefindedCharacterResult">
        <include refid="selectDefindedCharacterVo"/>
        where task_ordinal_id = #{taskOrdinalId}
    </select>

    <select id="selectDefindedCharacterByCharacterId" parameterType="String" resultMap="DefindedCharacterResult">
        <include refid="selectDefindedCharacterVo"/>
        where character_id = #{characterId}
    </select>
        
    <insert id="insertDefindedCharacter" parameterType="DefindedCharacter">
        insert into t_definded_character
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="characterId != null">character_id,</if>
            <if test="taskId != null and taskId != ''">task_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="imgUrl != null">img_url,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="taskOrdinalId != null and taskOrdinalId != ''">task_ordinal_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="characterId != null">#{characterId},</if>
            <if test="taskId != null and taskId != ''">#{taskId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="imgUrl != null">#{imgUrl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="taskOrdinalId != null and taskOrdinalId != ''">#{taskOrdinalId},</if>
         </trim>
    </insert>

    <update id="updateDefindedCharacter" parameterType="DefindedCharacter">
        update t_definded_character
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null and taskId != ''">task_id = #{taskId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="imgUrl != null">img_url = #{imgUrl},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="taskOrdinalId != null and taskOrdinalId != ''">task_ordinal_id = #{taskOrdinalId},</if>
        </trim>
        where character_id = #{characterId}
    </update>

    <delete id="deleteDefindedCharacterByCharacterId" parameterType="String">
        delete from t_definded_character where character_id = #{characterId}
    </delete>

    <delete id="deleteDefindedCharacterByCharacterIds" parameterType="String">
        delete from t_definded_character where character_id in 
        <foreach item="characterId" collection="array" open="(" separator="," close=")">
            #{characterId}
        </foreach>
    </delete>

    <delete id="deleteDefindedCharacterByTaskIds" parameterType="String">
        delete from t_definded_character where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper>