<template>
	<el-row class="row-bg">
		<el-col :span="24">
			<div class="grid-content">{{ title }}</div>
		</el-col>
		<el-col
			v-if="typeof total === 'number'"
			:span="24"
		>
			<div class="grid-content">
				<count-to
					:start-val="0"
					:end-val="total"
					:duration="3200"
				/>
			</div>
		</el-col>
		<el-col :span="24">
			<slot v-bind="slotProps" />
		</el-col>
	</el-row>
</template>
<script>
import CountTo from 'vue-count-to'
export default {
	name: 'Datawrap',
	components: {
		CountTo
	},
	props: {
		title: {
			type: String,
			default: ''
		},
		total: {
			type: [String, Number],
			default: ''
		}
	},
	computed: {
		slotProps() {
			const { title, total } = this
			return { title, total }
		}
	}
}
</script>
<style lang="scss" scoped>
.row-bg {
	width: 100%;
	background-color: #ffffff;
}

.grid-content {
	font-size: 14px;
	font-weight: 700;
	color: #000;
	text-align: left;
	height: 20px;
	line-height: 20px;
}
</style>
