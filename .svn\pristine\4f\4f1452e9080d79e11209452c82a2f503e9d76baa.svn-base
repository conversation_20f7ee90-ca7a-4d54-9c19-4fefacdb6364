package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.AdminScoreHistory;

/**
 * 基本变更历史记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface AdminScoreHistoryMapper 
{
    /**
     * 查询基本变更历史记录
     * 
     * @param id 基本变更历史记录主键
     * @return 基本变更历史记录
     */
    public AdminScoreHistory selectAdminScoreHistoryById(Long id);

    /**
     * 查询基本变更历史记录列表
     * 
     * @param adminScoreHistory 基本变更历史记录
     * @return 基本变更历史记录集合
     */
    public List<AdminScoreHistory> selectAdminScoreHistoryList(AdminScoreHistory adminScoreHistory);

    /**
     * 新增基本变更历史记录
     * 
     * @param adminScoreHistory 基本变更历史记录
     * @return 结果
     */
    public int insertAdminScoreHistory(AdminScoreHistory adminScoreHistory);

    /**
     * 修改基本变更历史记录
     * 
     * @param adminScoreHistory 基本变更历史记录
     * @return 结果
     */
    public int updateAdminScoreHistory(AdminScoreHistory adminScoreHistory);

    /**
     * 删除基本变更历史记录
     * 
     * @param id 基本变更历史记录主键
     * @return 结果
     */
    public int deleteAdminScoreHistoryById(Long id);

    /**
     * 批量删除基本变更历史记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAdminScoreHistoryByIds(Long[] ids);
}
