package com.dataxai.web.service.impl;

import cn.hutool.http.HttpUtil;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.domain.ProductImageUploadTask;
import com.dataxai.domain.ProductInfo;
import com.dataxai.mapper.ProductInfoMapper;
import com.dataxai.service.IProductImageUploadService;
import com.dataxai.web.service.AliYunFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;

/**
 * 产品图片上传服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class ProductImageUploadServiceImpl implements IProductImageUploadService
{
    @Autowired
    @Qualifier("productImageUploadRedisTemplate")
    private RedisTemplate<String, ProductImageUploadTask> redisTemplate;
    
    @Autowired
    private AliYunFileService aliYunFileService;
    
    @Autowired
    private ProductInfoMapper productInfoMapper;
    
    private static final String PRODUCT_IMAGE_UPLOAD_QUEUE = "product:image:upload:queue";

    /**
     * 创建产品图片上传任务
     */
    @Override
    public void createImageUploadTask(Long productId, String originalImageUrl, Long userId) {
        try {
            ProductImageUploadTask task = new ProductImageUploadTask(productId, originalImageUrl, userId);
            
            // 将任务添加到Redis队列
            Long result = redisTemplate.opsForList().leftPush(PRODUCT_IMAGE_UPLOAD_QUEUE, task);
            if (result != null && result > 0) {
                log.info("产品图片上传任务添加成功！产品ID：{}，当前队列长度：{}", productId, result);
            } else {
                log.error("产品图片上传任务添加失败！产品ID：{}", productId);
            }
        } catch (Exception e) {
            log.error("创建产品图片上传任务失败，产品ID：{}，错误：{}", productId, e.getMessage(), e);
        }
    }

    /**
     * 处理产品图片上传任务
     */
    @Override
    public void processImageUploadTask(ProductImageUploadTask task) {
        log.info("开始处理产品图片上传任务：{}", task);
        
        try {
            // 更新任务状态为处理中
            task.setStatus(1);
            task.setUpdateTime(DateUtils.getNowDate());
            
            // 从URL下载图片并上传到阿里云
            String aliYunUrl = downloadAndUploadToAliYun(task.getOriginalImageUrl());
            
            if (StringUtils.isNotEmpty(aliYunUrl)) {
                // 更新产品信息中的图片URL和状态
                updateProductInfo(task.getProductId(), aliYunUrl);
                
                // 更新任务状态为成功
                task.setStatus(2);
                log.info("产品图片上传成功，产品ID：{}，阿里云URL：{}", task.getProductId(), aliYunUrl);
            } else {
                // 更新任务状态为失败
                task.setStatus(3);
                task.setErrorMessage("上传到阿里云失败");
                log.error("产品图片上传到阿里云失败，产品ID：{}", task.getProductId());
            }
            
        } catch (Exception e) {
            // 更新任务状态为失败
            task.setStatus(3);
            task.setErrorMessage(e.getMessage());
            log.error("处理产品图片上传任务失败，产品ID：{}，错误：{}", task.getProductId(), e.getMessage(), e);
        } finally {
            task.setUpdateTime(DateUtils.getNowDate());
        }
    }

    /**
     * 从URL下载图片并上传到阿里云
     */
    private String downloadAndUploadToAliYun(String imageUrl) {
        try {
            // 从URL下载图片
            URL url = new URL(imageUrl);
            try (InputStream inputStream = url.openStream()) {
                // 创建临时的MultipartFile对象
                byte[] imageBytes = inputStream.readAllBytes();
                String fileName = extractFileNameFromUrl(imageUrl);
                
                // 创建自定义的MultipartFile实现
                CustomMultipartFile multipartFile = new CustomMultipartFile(imageBytes, fileName);
                
                // 上传到阿里云
                String aliYunUrl = aliYunFileService.uploadALiYun(multipartFile);
                
                // 进行图片内容审核
                if (StringUtils.isNotEmpty(aliYunUrl)) {
                    boolean isValid = aliYunFileService.authenticateUrl(aliYunUrl);
                    if (!isValid) {
                        log.warn("图片内容审核不通过，URL：{}", aliYunUrl);
                        return null;
                    }
                }
                
                return aliYunUrl;
            }
        } catch (Exception e) {
            log.error("下载并上传图片到阿里云失败，URL：{}，错误：{}", imageUrl, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新产品信息
     */
    private void updateProductInfo(Long productId, String aliYunUrl) {
        try {
            ProductInfo productInfo = new ProductInfo();
            productInfo.setId(productId);
            productInfo.setProductImageUrl(aliYunUrl);
            productInfo.setStatus("2"); // 更新状态为已处理
            productInfo.setUpdateTime(DateUtils.getNowDate());
            
            int result = productInfoMapper.updateProductInfo(productInfo);
            if (result > 0) {
                log.info("产品信息更新成功，产品ID：{}，阿里云URL：{}", productId, aliYunUrl);
            } else {
                log.error("产品信息更新失败，产品ID：{}", productId);
            }
        } catch (Exception e) {
            log.error("更新产品信息失败，产品ID：{}，错误：{}", productId, e.getMessage(), e);
        }
    }

    /**
     * 从URL中提取文件名
     */
    private String extractFileNameFromUrl(String url) {
        try {
            String path = new URL(url).getPath();
            String fileName = path.substring(path.lastIndexOf('/') + 1);
            if (StringUtils.isEmpty(fileName) || !fileName.contains(".")) {
                fileName = "image.jpg"; // 默认文件名
            }
            return fileName;
        } catch (Exception e) {
            return "image.jpg"; // 默认文件名
        }
    }

    /**
     * 自定义MultipartFile实现
     */
    private static class CustomMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String originalFilename;

        public CustomMultipartFile(byte[] content, String filename) {
            this.content = content;
            this.name = "file";
            this.originalFilename = filename;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return "image/jpeg";
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            throw new UnsupportedOperationException("transferTo not supported");
        }
    }
}
