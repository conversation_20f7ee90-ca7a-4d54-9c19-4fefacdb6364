package com.dataxai.web.mapper;

import java.util.List;
import java.util.Map;

import com.dataxai.web.domain.OrdinalImgResult;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.domain.TaskPush;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Mapper
public interface TaskMapper
{
    /**
     * 查询任务
     *
     * @param taskId 任务主键
     * @return 任务
     */
    public Task selectTaskByUserIdAndTaskId(@Param("userId") Long userId,@Param("taskId") String taskId);


    public int selectTaskQueueNumByUserIdAndTaskId(@Param("taskId") String taskId,@Param("flag") int flag);


    /**
     * 查询任务
     *
     * @param taskId 任务主键
     * @return 任务
     */
    public Task selectTaskByTaskId(String taskId);

    public List<Task> selectTaskByTaskIds(@Param("taskIds") String[] taskIds,@Param("userId")Long userId);

    /**
     * 查询任务(仅仅只是查询任务信息，不包含关联的子表的信息)
     *
     * @param taskId 任务主键
     * @return 任务
     */
    public Task selectTaskInfoByTaskId(String taskId);

    /**
     * 查询任务列表
     *
     * @param userId 用户id
     * @return 任务集合
     */
    public List<Task> selectTaskListByUserId(Long userId);

    /**
     * 查询任务列表
     *
     * @param task 任务
     * @return 任务集合
     */
    public List<Task> selectTaskList(Task task);

    public List<Task> selectTaskListAddUrl(Task task);

    public List<Integer> selectStatus(@Param("taskId") String taskId);

    /**
     * 新增任务
     *
     * @param task 任务
     * @return 结果
     */
    public int insertTask(Task task);

    public void insertBatchTask(List<Task> taskList);

    /**
     * 修改任务
     *
     * @param task 任务
     * @return 结果
     */
    public int updateTask(Task task);

    /**
     * 删除任务
     *
     * @param taskId 任务主键
     * @return 结果
     */
    public int deleteTaskByTaskId(String taskId);

    /**
     * 批量逻辑删除任务
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int logicDeleteTaskByTaskIds(@Param("userId") Long userId, @Param("taskIds") String[] taskIds);

    /**
     * 批量删除任务
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskByTaskIds(String[] taskIds);

    /**
     * 批量逻辑删除任务次数
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int logicDeleteTaskOrdinalByTaskIds(@Param("userId")Long userId,@Param("taskIds") String[] taskIds);

    /**
     * 批量删除任务次数
     *
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTaskOrdinalByTaskIds(String[] taskIds);

    /**
     * 批量新增任务次数
     *
     * @param taskOrdinalList 任务次数列表
     * @return 结果
     */
    public int batchTaskOrdinal(List<TaskOrdinal> taskOrdinalList);


    /**
     * 通过任务主键删除任务次数信息
     *
     * @param taskId 任务ID
     * @return 结果
     */
    public int deleteTaskOrdinalByTaskId(String taskId);

    Task selectTaskSegData(Task task);

    /**
     * 查询任务列表
     *
     * @param taskStatus 任务
     * @return 任务集合
     */
    public List<Task> selectTaskListByTaskStatus(@Param("taskStatus") Integer[] taskStatus);

    int resetTask(Task task);

    public List<Task> selectTaskListByTaskStatusAndUserId(@Param("taskStatus") Integer[] taskStatus,@Param("userId") Long userId);
    public List<Task> selectTaskListByTaskStatusAndUserIdAndTaskId(@Param("taskStatus") Integer[] taskStatus,@Param("userId") Long userId,@Param("taskId") String taskId);

    List<Task> getTaskListByTaskStatus(@Param("taskStatus") Integer[] taskStatus,@Param("fourK") Integer fourK,
                                        @Param("myPageNum") int myPageNum,@Param("myPageSize") int myPageSize,@Param("typeList") Integer[] typeList);

    int getTaskCountByTaskStatus(@Param("taskStatus") Integer[] taskStatus,@Param("fourK") Integer fourK,
                                 @Param("typeList")Integer[] typeList);

    int getTextureTaskCount(@Param("status")Integer status, @Param("fourK")Integer fourK);

    List<Task> getTextureTaskList(@Param("taskStatus") Integer[] taskStatus, @Param("fourK") Integer fourK,
                                  @Param("myPageNum")int myPageNum, @Param("myPageSize")int myPageSize);

    // 只要type 不等于 4 都需要调用蒙版接口 计算符合条件的数量
    int getTaskSegCount(@Param("taskStatus") Integer[] taskStatus,@Param("fourK") Integer fourK);
    // 只要type 不等于 4 都需要调用蒙版接口 查询符合条件的集合
    List<Task> getSegTaskList(@Param("taskStatus") Integer[] taskStatus,@Param("fourK") Integer fourK,
                              @Param("myPageNum") int myPageNum,@Param("myPageSize") int myPageSize);


    int selectTextureAddTaskCount(@Param("status")Integer status, @Param("fourK")Integer fourK);

    List<Task> selectTextureAddTaskList(@Param("status") Integer taskStatus, @Param("fourK") Integer fourK,
                                        @Param("myPageNum")int myPageNum, @Param("myPageSize")int myPageSize);

    List<Task> selectByUserId(@Param("userId")Long userId,@Param("types")List<Long> types,@Param("limit")Integer limit,
                              @Param("statuses")List<Integer> statuses);

    //截取部分原图图片地址
    Integer updateOrdinalUrlSub( @Param("originImgUrl")String originImgUrl,@Param("taskId")String taskId);

    int newTaskExecute(@Param("status")Integer status);

    List<Task> selectNewTaskExecute(@Param("status") Integer status, @Param("myPageNum")int myPageNum, @Param("myPageSize")int myPageSize);

    void logicDeleteImageByTaskIds(@Param("userId")Long userId,@Param("taskIds") String[] taskIds);

    void batchInsert(List<Task> taskList);

    void updateTaskStatus(Task task);


    int getTaskBatchCount(@Param("taskStatus") Integer[] taskStatus,@Param("fourK") Integer fourK,
                          @Param("typeList")Integer[] typeList);

    List<Task> getBatchTaskList(@Param("taskStatus") Integer[] taskStatus,@Param("fourK") Integer fourK,
                                @Param("myPageNum") int myPageNum,@Param("myPageSize") int myPageSize,@Param("typeList") Integer[] typeList);


    Task selectTypeByUrl(String url);

    Task selectTypeByUrlAndBatchId(@Param("url")String url,@Param("batchId")String batchId);

    List<Task> selectByTaskIds(@Param("taskIds") String[] taskIds);

    @MapKey("id")
    List<Map<String, String>> getWaitList(@Param("isBatch") int isBatch, @Param("createTime") String createTime, @Param("type") String type);

    Integer getGpuCount(@Param("isBatch") int isBatch,@Param("type") String type);

    public List<Task> selectTasksByTaskIds(@Param("taskIds") String[] taskIds);

    List<Task> getTaskByPush(@Param("taskIds") String[] taskIds);
    List<Task> getTaskByIds(@Param("taskIds") String[] taskIds);
    List<TaskPush> getTaskPush();


}
