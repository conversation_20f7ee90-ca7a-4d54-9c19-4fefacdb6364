package com.dataxai.web.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class CroppingData {

    private Task croppingData;

    /** 任务id */
    @ApiModelProperty(value = "任务id")
    private String taskId;



    /** 任务类型(0-真人图，1-人台图，2-商品图，3-配饰图) */
    @ApiModelProperty(value = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)",required = true)
    private Long type;

    /** 任务所属用户id */
    @ApiModelProperty(value = "任务所属用户id",hidden = true)
    private Long userId;

    @ApiModelProperty(value = "任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中)",hidden = true)
    private Integer status;
}
