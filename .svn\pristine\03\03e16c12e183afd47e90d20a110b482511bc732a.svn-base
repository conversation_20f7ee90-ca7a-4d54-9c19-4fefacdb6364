<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.TBaseinfoContactusMapper">
    
    <resultMap type="TBaseinfoContactus" id="TBaseinfoContactusResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="content"    column="content"    />
    </resultMap>

    <sql id="selectTBaseinfoContactusVo">
        select id, name, content from t_baseinfo_contactus
    </sql>

    <select id="selectTBaseinfoContactusList" parameterType="TBaseinfoContactus" resultMap="TBaseinfoContactusResult">
        <include refid="selectTBaseinfoContactusVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
        </where>
    </select>
    
    <select id="selectTBaseinfoContactusById" parameterType="Long" resultMap="TBaseinfoContactusResult">
        <include refid="selectTBaseinfoContactusVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTBaseinfoContactus" parameterType="TBaseinfoContactus" useGeneratedKeys="true" keyProperty="id">
        insert into t_baseinfo_contactus
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="content != null">content,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="content != null">#{content},</if>
         </trim>
    </insert>

    <update id="updateTBaseinfoContactus" parameterType="TBaseinfoContactus">
        update t_baseinfo_contactus
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="content != null">content = #{content},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTBaseinfoContactusById" parameterType="Long">
        delete from t_baseinfo_contactus where id = #{id}
    </delete>

    <delete id="deleteTBaseinfoContactusByIds" parameterType="String">
        delete from t_baseinfo_contactus where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>