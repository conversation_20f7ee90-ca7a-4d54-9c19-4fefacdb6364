package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.TBaseinfoUseguide;

/**
 * 使用指南Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface ITBaseinfoUseguideService 
{
    /**
     * 查询使用指南
     * 
     * @param id 使用指南主键
     * @return 使用指南
     */
    public TBaseinfoUseguide selectTBaseinfoUseguideById(Long id);

    /**
     * 查询使用指南列表
     * 
     * @param tBaseinfoUseguide 使用指南
     * @return 使用指南集合
     */
    public List<TBaseinfoUseguide> selectTBaseinfoUseguideList(TBaseinfoUseguide tBaseinfoUseguide);

    /**
     * 新增使用指南
     * 
     * @param tBaseinfoUseguide 使用指南
     * @return 结果
     */
    public int insertTBaseinfoUseguide(TBaseinfoUseguide tBaseinfoUseguide);

    /**
     * 修改使用指南
     * 
     * @param tBaseinfoUseguide 使用指南
     * @return 结果
     */
    public int updateTBaseinfoUseguide(TBaseinfoUseguide tBaseinfoUseguide);

    /**
     * 批量删除使用指南
     * 
     * @param ids 需要删除的使用指南主键集合
     * @return 结果
     */
    public int deleteTBaseinfoUseguideByIds(Long[] ids);

    /**
     * 删除使用指南信息
     * 
     * @param id 使用指南主键
     * @return 结果
     */
    public int deleteTBaseinfoUseguideById(Long id);
}
