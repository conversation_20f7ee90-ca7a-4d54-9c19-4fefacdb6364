package com.dataxai.web.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * @version 1.0
 * @Author:xg
 * @Date:2024-02-02 11:58
 * @Desciption:com.dataxai.web.domain
 */
@Data
public class PayResultData implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long userId; //推送的用户id
    private int status; // 0-支付失败，1-支付成功
    private int type; // 类型(0-加油包，1-套餐)
    private String msg; // 支付失败/成功的 提示信息
    private String orderNo; //订单号
}
