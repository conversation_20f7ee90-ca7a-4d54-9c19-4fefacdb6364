package com.dataxai.web.listener;

import com.dataxai.event.ProductImageUploadEvent;
import com.dataxai.service.IProductImageUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 产品图片上传事件监听器
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Component
public class ProductImageUploadEventListener {

    @Autowired
    private IProductImageUploadService productImageUploadService;

    /**
     * 处理产品图片上传事件
     */
    @EventListener
    @Async
    public void handleProductImageUploadEvent(ProductImageUploadEvent event) {
        log.info("接收到产品图片上传事件，产品ID：{}，图片URL：{}", 
                event.getProductId(), event.getOriginalImageUrl());
        
        try {
            productImageUploadService.createImageUploadTask(
                event.getProductId(), 
                event.getOriginalImageUrl(), 
                event.getUserId()
            );
        } catch (Exception e) {
            log.error("处理产品图片上传事件失败，产品ID：{}，错误：{}", 
                    event.getProductId(), e.getMessage(), e);
        }
    }
}
