package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.OrdinalImgResult;
import org.apache.ibatis.annotations.Param;

/**
 * 每次任务执行后生成的图片Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-14
 */
public interface OrdinalImgResultMapper
{
     public List<OrdinalImgResult> selectImageResult(@Param("batchId") String batchId);

    /**
     * 查询每次任务执行后生成的图片
     *
     * @param imageId 每次任务执行后生成的图片主键
     * @return 每次任务执行后生成的图片
     */
    public OrdinalImgResult selectOrdinalImgResultByImageId(String imageId);

    /**
     * 查询每次任务执行后生成的图片列表
     *
     * @param ordinalImgResult 每次任务执行后生成的图片
     * @return 每次任务执行后生成的图片集合
     */
    public List<OrdinalImgResult> selectOrdinalImgResultList(OrdinalImgResult ordinalImgResult);

    /**
     * 新增每次任务执行后生成的图片
     *
     * @param ordinalImgResult 每次任务执行后生成的图片
     * @return 结果
     */
    public int insertOrdinalImgResult(OrdinalImgResult ordinalImgResult);

    /**
     * 修改每次任务执行后生成的图片
     *
     * @param ordinalImgResult 每次任务执行后生成的图片
     * @return 结果
     */
    public int updateOrdinalImgResult(OrdinalImgResult ordinalImgResult);

    /**
     * 删除每次任务执行后生成的图片
     *
     * @param imageId 每次任务执行后生成的图片主键
     * @return 结果
     */
    public int deleteOrdinalImgResultByImageId(String imageId);

    /**
     * 批量删除每次任务执行后生成的图片
     *
     * @param imageIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrdinalImgResultByImageIds(String[] imageIds);

    List<OrdinalImgResult> listByUserIdTypes(@Param("ordinalImgResult") OrdinalImgResult ordinalImgResult,@Param("types") List<Long> types);
    List<OrdinalImgResult> listByTaskIds(@Param("taskIds") String[] taskIds );
    List<OrdinalImgResult> listByTaskOrdinalId(@Param("taskOrdinalId") String taskOrdinalId );

    // 根据结果图获得任务类型
    OrdinalImgResult selectTypeByUrl(String url);

}
