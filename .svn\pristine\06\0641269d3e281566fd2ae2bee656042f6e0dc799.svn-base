package com.dataxai.web.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 套餐中包含的不同价格和有效期的子套餐对象 t_tariff_package_sub
 * 
 * <AUTHOR>
 * @date 2024-01-04
 */
@ApiModel(value = "TariffPackageSub", description = "套餐中包含的不同价格和有效期的子套餐对象")
@Data
public class TariffPackageSub extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 套餐档位id */
    @ApiModelProperty(name = "子套餐id")
    private String tariffPackageSubId;

    @ApiModelProperty(name = "子套餐名称")
    private String tariffPackageSubName;

    /** 父套餐id */
    @Excel(name = "父套餐id")
    @ApiModelProperty(name = "父套餐id")
    private String parentId;

    /** 积分 */
    @Excel(name = "积分")
    @ApiModelProperty(name = "积分")
    private Long score;

    /** 套餐的生效的开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "套餐的生效的开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "套餐的生效的开始时间",hidden = true)
    private Date startTime;

    /** 套餐的过期的截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "套餐的过期的截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(name = "套餐的过期的截止时间",hidden = true)
    private Date endTime;

    /** 金额 */
    @Excel(name = "金额")
    @ApiModelProperty(name = "金额")
    private Double subMoney;

    /** 套餐有效周期， */
    @Excel(name = "套餐有效周期单位天")
    @ApiModelProperty(name = "套餐有效周期，单位天")
    private Integer cycle;

    /** 是否上架(0-未上架，1-已上架) */
    @Excel(name = "是否上架(0-未上架，1-已上架)")
    @ApiModelProperty(name = "是否上架(0-未上架，1-已上架)")
    private Long subOnSale;


}
