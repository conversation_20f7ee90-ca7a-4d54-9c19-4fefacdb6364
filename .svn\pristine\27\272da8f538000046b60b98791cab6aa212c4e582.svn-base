package com.dataxai.web.enums.wxpay;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum WxRefundStatus {

    /**
     * 退款成功
     * 0 - SUCCESS
     */
    SUCCESS(0),

    /**
     * 退款关闭
     * 1 - CLOSED
     */
    CLOSED(1),

    /**
     * 退款处理中
     * 2 - PROCESSING
     */
    PROCESSING(2),

    /**
     * 退款异常
     * 3 - ABNORMAL
     */
    ABNORMAL(3);

    /**
     * 类型
     */
    private final int type;
}
