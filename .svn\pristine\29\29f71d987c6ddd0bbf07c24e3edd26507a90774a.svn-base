package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 图片切割后返回的数据对象
 * 
 * <AUTHOR>
 * @date 2024-01-10
 */
@ApiModel
@Data
public class TaskSegDatResult implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;

//    /**
//     * 任务名称
//     */
//    @Excel(name = "任务名称")
//    @ApiModelProperty(value = "任务名称", hidden = true)
//    private String taskName;

    /**
     * 任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)
     */
    @Excel(name = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)")
    @ApiModelProperty(value = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)", required = true)
    private Long type;

    /**
     * 任务所属用户id
     */
    @Excel(name = "任务所属用户id")
    @ApiModelProperty(value = "任务所属用户id", hidden = true)
    private Long userId;

    //根据图片反推的文本
    private String desc;

    public String getReferedTaskOrdinalId() {
        return referedTaskOrdinalId;
    }

    public void setReferedTaskOrdinalId(String referedTaskOrdinalId) {
        this.referedTaskOrdinalId = referedTaskOrdinalId;
    }

    /**
     * 相关任务某次执行的id
     */
    @Excel(name = "相关任务某次执行的id")
    @ApiModelProperty(value = "相关任务某次执行的id,通过复制创意方式创建任务时，需要传递该参数",hidden = true)
    private String referedTaskOrdinalId;


    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Excel(name = "任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中)")
    @ApiModelProperty(value = "任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中)",hidden = true)
    private Integer status;

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getTaskId() {
        return taskId;
    }

//    public void setTaskName(String taskName) {
//        this.taskName = taskName;
//    }
//
//    public String getTaskName() {
//        return taskName;
//    }

    public void setType(Long type) {
        this.type = type;
    }

    public Long getType() {
        return type;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }



//    public String getOriginalUrl() {
//        return originalUrl;
//    }
//
//    public void setOriginalUrl(String originalUrl) {
//        this.originalUrl = originalUrl;
//    }
//
//
//    private String originalUrl;

    public SegData getSeg_data() {
        return seg_data;
    }

    public void setSeg_data(SegData seg_data) {
        this.seg_data = seg_data;
    }

    private SegData seg_data;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("taskId", getTaskId())
                .append("status", getStatus())
                .append("type", getType())
                .append("userId", getUserId())
                .append("referedTaskId", getReferedTaskOrdinalId())
//                .append("originalUrl", getOriginalUrl())
                .toString();
    }
}
