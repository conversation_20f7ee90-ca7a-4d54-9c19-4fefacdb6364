package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 任务次数对象 t_task_ordinal
 * 
 * <AUTHOR>
 * @date 2024-01-18
 */
@ApiModel(value = "TaskOrdinal", description = "任务次数对象 t_task_ordinal")
public class AdminTaskOrdinal extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id，仅仅是个主键id */
    @ApiModelProperty(value = "id，仅仅是个主键id")
    private String taskOrdinalId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    @ApiModelProperty(value = "任务名称")
    private String name;

    /** 所属任务id */
    @Excel(name = "所属任务id")
    @ApiModelProperty(value = "所属任务id")
    private String taskId;

    /** 任务类型(0-真人图，1-人台图，2-商品图，3-配饰图) */
    @Excel(name = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)")
    @ApiModelProperty(value = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)")
    private Long type;

    /** 任务所属用户id */
    @Excel(name = "任务所属用户id")
    @ApiModelProperty(value = "任务所属用户id")
    private Long userId;

    /** 相关任务id */
    @Excel(name = "相关任务id")
    @ApiModelProperty(value = "相关任务id")
    private String referedTaskId;

    /** 相关任务第几次执行的id */
    @Excel(name = "相关任务第几次执行的id")
    @ApiModelProperty(value = "相关任务第几次执行的id")
    private String referedTaskOrdinalId;

    /** 执行第几次 */
    @Excel(name = "执行第几次")
    @ApiModelProperty(value = "执行第几次")
    private Long ordinal;

    /** 任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中) */
    @Excel(name = "任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中)")
    @ApiModelProperty(value = "任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中)")
    private Integer status;

    /** 原始图片url地址 */
    @Excel(name = "原始图片url地址")
    @ApiModelProperty(value = "原始图片url地址")
    private String originImgUrl;

    /** 处理后的图片的url地址 */
    @Excel(name = "处理后的图片的url地址")
    @ApiModelProperty(value = "处理后的图片的url地址")
    private String markImgUrl;

    /** 任务描述类型(0-快捷描述,1-预设模块，2-高级描述) */
    @Excel(name = "任务描述类型(0-快捷描述,1-预设模块，2-高级描述)")
    @ApiModelProperty(value = "任务描述类型(0-快捷描述,1-预设模块，2-高级描述)")
    private String desType;

    /** 快捷描述内容 */
    @Excel(name = "快捷描述内容")
    @ApiModelProperty(value = "快捷描述内容")
    private String shortCutDesc;

    /** 选中的模特的特征描述 */
    @Excel(name = "选中的模特的特征描述")
    @ApiModelProperty(value = "选中的模特的特征描述")
    private String modelPrompt;

    /** 预设模版中的模特特征id */
    @Excel(name = "预设模版中的模特特征id")
    @ApiModelProperty(value = "预设模版中的模特特征id")
    private Long modelCharacterId;

    /** 预设模版中的模特性别(0-男，1-女) */
    @Excel(name = "预设模版中的模特性别(0-男，1-女)")
    @ApiModelProperty(value = "预设模版中的模特性别(0-男，1-女)")
    private String modelSex;

    /** 预设模版中的模特年龄 */
    @Excel(name = "预设模版中的模特年龄")
    @ApiModelProperty(value = "预设模版中的模特年龄")
    private String modelAge;

    /** 预设模版中的模特皮肤 */
    @Excel(name = "预设模版中的模特皮肤")
    @ApiModelProperty(value = "预设模版中的模特皮肤")
    private String modelSkin;

    /** 预设模版中的模特表情 */
    @Excel(name = "预设模版中的模特表情")
    @ApiModelProperty(value = "预设模版中的模特表情",hidden = true)
    @JsonIgnore
    private String modelExpression;

    /** 预设模版中的模特气质 */
    @Excel(name = "预设模版中的模特气质")
    @ApiModelProperty(value = "预设模版中的模特气质",hidden = true)
    @JsonIgnore
    private String modelTemperament;

    /** 预设模版中的模特图片id */
    @Excel(name = "预设模版中的模特图片id")
    @ApiModelProperty(value = "预设模版中的模特图片id",hidden = true)
    @JsonIgnore
    private String modelImgId;

    /** 预设模版中自定义模特图片url */
    @Excel(name = "预设模版中自定义模特图片url")
    @ApiModelProperty(value = "预设模版中自定义模特图片url")
    private String modelUploadUrl;

    /** 预设模版中的场景类型(0-室内，1-室外，2-纯色) */
    @Excel(name = "预设模版中的场景类型(0-室内，1-室外，2-纯色)")
    @ApiModelProperty(value = "预设模版中的场景类型(0-室内，1-室外，2-纯色)",hidden = true)
    @JsonIgnore
    private Long sceneType;

    /** 预设模版中的场景类型(室内，室外，纯色) */
    @Excel(name = "预设模版中的场景类型(室内，室外，纯色)")
    @ApiModelProperty(value = "预设模版中的场景类型(室内，室外，纯色)")
    private String sceneTypeStr;

    /** 预设模版中的场景大类型 */
    @Excel(name = "预设模版中的场景大类型")
    @ApiModelProperty(value = "预设模版中的场景大类型")
    private String sceneCategoryLarge;

    /** 预设模版中的场景大类型下的小类型 */
    @Excel(name = "预设模版中的场景大类型下的小类型")
    @ApiModelProperty(value = "预设模版中的场景大类型下的小类型")
    private String sceneCategorySmall;

    /** 预设模版中的场景图片url */
    @Excel(name = "预设模版中的场景图片url")
    @ApiModelProperty(value = "预设模版中的场景图片url",hidden = true)
    @JsonIgnore
    private String sceneImgId;

    /** 预设模版中的场景id */
    @Excel(name = "预设模版中的场景id")
    @ApiModelProperty(value = "预设模版中的场景id")
    private Long sceneId;

    /** 预设模版中的商品场景大类 */
    @Excel(name = "预设模版中的商品场景大类")
    @ApiModelProperty(value = "预设模版中的商品场景大类")
    private String goodsSceneCategory;

    /** 预设模版中的商品场景大类下的小类 */
    @Excel(name = "预设模版中的商品场景大类下的小类")
    @ApiModelProperty(value = "预设模版中的商品场景大类下的小类")
    private String goodsSceneCategorySub;

    /** 高级描述中的权重 */
    @Excel(name = "高级描述中的权重")
    @ApiModelProperty(value = "高级描述中的权重")
    private Integer weight;

    /** 预设模版中的场景所表示的信息 */
    @Excel(name = "预设模版中的场景所表示的信息")
    @ApiModelProperty(value = "预设模版中的场景所表示的信息")
    private String scenePrompt;

    /** 高级描述中的正向咒语 */
    @Excel(name = "高级描述中的正向咒语")
    @ApiModelProperty(value = "高级描述中的正向咒语")
    private String forward;

    /** 高级描述中的反向咒语 */
    @Excel(name = "高级描述中的反向咒语")
    @ApiModelProperty(value = "高级描述中的反向咒语")
    private String reverse;

    /** 任务执行 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "任务执行 时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(value = "任务执行 时间",hidden = true)
    @JsonIgnore
    private Date executeTime;

    /** 任务成功后生成的8图片的url地址 */
    @Excel(name = "任务成功后生成的8图片的url地址")
    @ApiModelProperty(value = "任务成功后生成的8图片的url地址",hidden = true)
    @JsonIgnore
    private String resultImgUrls;

    /** 是否删除(0-未删除，1-已删除) */
    @ApiModelProperty(value = "是否删除(0-未删除，1-已删除)",hidden = true)
    @JsonIgnore
    private Long delFlag;

    private Date beginCreateTime;

    private Date endCreateTime;

    public Date getBeginCreateTime() {
        return beginCreateTime;
    }

    public void setBeginCreateTime(Date beginCreateTime) {
        this.beginCreateTime = beginCreateTime;
    }

    public Date getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Date endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    public Long getSeed() {
        return seed;
    }

    public void setSeed(Long seed) {
        this.seed = seed;
    }

    //随机种子
    private Long seed;

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    /** 执行任务时前端需要使用的额外的一些参数 */
    @ApiModelProperty(value = "执行任务时前端需要使用的额外的一些参数")
    private String extra;

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    /** 快捷描述内容 */
    @Excel(name = "快捷描述内容")
    @ApiModelProperty(value = "快捷描述内容,用于前端页面展示使用")
    private String prompt;

    /** 每次任务执行后生成的图片信息 */
    private List<OrdinalImgResult> ordinalImgResultList;

    public void setTaskOrdinalId(String taskOrdinalId)
    {
        this.taskOrdinalId = taskOrdinalId;
    }

    public String getTaskOrdinalId()
    {
        return taskOrdinalId;
    }
    public void setName(String name)
    {
        this.name = name;
    }

    public String getName()
    {
        return name;
    }
    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskId()
    {
        return taskId;
    }
    public void setType(Long type)
    {
        this.type = type;
    }

    public Long getType()
    {
        return type;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setReferedTaskId(String referedTaskId)
    {
        this.referedTaskId = referedTaskId;
    }

    public String getReferedTaskId()
    {
        return referedTaskId;
    }
    public void setReferedTaskOrdinalId(String referedTaskOrdinalId)
    {
        this.referedTaskOrdinalId = referedTaskOrdinalId;
    }

    public String getReferedTaskOrdinalId()
    {
        return referedTaskOrdinalId;
    }
    public void setOrdinal(Long ordinal)
    {
        this.ordinal = ordinal;
    }

    public Long getOrdinal()
    {
        return ordinal;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setOriginImgUrl(String originImgUrl)
    {
        this.originImgUrl = originImgUrl;
    }

    public String getOriginImgUrl()
    {
        return originImgUrl;
    }
    public void setMarkImgUrl(String markImgUrl)
    {
        this.markImgUrl = markImgUrl;
    }

    public String getMarkImgUrl()
    {
        return markImgUrl;
    }
    public void setDesType(String desType)
    {
        this.desType = desType;
    }

    public String getDesType()
    {
        return desType;
    }
    public void setShortCutDesc(String shortCutDesc)
    {
        this.shortCutDesc = shortCutDesc;
    }

    public String getShortCutDesc()
    {
        return shortCutDesc;
    }
    public void setModelPrompt(String modelPrompt)
    {
        this.modelPrompt = modelPrompt;
    }

    public String getModelPrompt()
    {
        return modelPrompt;
    }
    public void setModelCharacterId(Long modelCharacterId)
    {
        this.modelCharacterId = modelCharacterId;
    }

    public Long getModelCharacterId()
    {
        return modelCharacterId;
    }
    public void setModelSex(String modelSex)
    {
        this.modelSex = modelSex;
    }

    public String getModelSex()
    {
        return modelSex;
    }
    public void setModelAge(String modelAge)
    {
        this.modelAge = modelAge;
    }

    public String getModelAge()
    {
        return modelAge;
    }
    public void setModelSkin(String modelSkin)
    {
        this.modelSkin = modelSkin;
    }

    public String getModelSkin()
    {
        return modelSkin;
    }
    public void setModelExpression(String modelExpression)
    {
        this.modelExpression = modelExpression;
    }

    public String getModelExpression()
    {
        return modelExpression;
    }
    public void setModelTemperament(String modelTemperament)
    {
        this.modelTemperament = modelTemperament;
    }

    public String getModelTemperament()
    {
        return modelTemperament;
    }
    public void setModelImgId(String modelImgId)
    {
        this.modelImgId = modelImgId;
    }

    public String getModelImgId()
    {
        return modelImgId;
    }
    public void setModelUploadUrl(String modelUploadUrl)
    {
        this.modelUploadUrl = modelUploadUrl;
    }

    public String getModelUploadUrl()
    {
        return modelUploadUrl;
    }
    public void setSceneType(Long sceneType)
    {
        this.sceneType = sceneType;
    }

    public Long getSceneType()
    {
        return sceneType;
    }
    public void setSceneTypeStr(String sceneTypeStr)
    {
        this.sceneTypeStr = sceneTypeStr;
    }

    public String getSceneTypeStr()
    {
        return sceneTypeStr;
    }
    public void setSceneCategoryLarge(String sceneCategoryLarge)
    {
        this.sceneCategoryLarge = sceneCategoryLarge;
    }

    public String getSceneCategoryLarge()
    {
        return sceneCategoryLarge;
    }
    public void setSceneCategorySmall(String sceneCategorySmall)
    {
        this.sceneCategorySmall = sceneCategorySmall;
    }

    public String getSceneCategorySmall()
    {
        return sceneCategorySmall;
    }
    public void setSceneImgId(String sceneImgId)
    {
        this.sceneImgId = sceneImgId;
    }

    public String getSceneImgId()
    {
        return sceneImgId;
    }
    public void setSceneId(Long sceneId)
    {
        this.sceneId = sceneId;
    }

    public Long getSceneId()
    {
        return sceneId;
    }
    public void setGoodsSceneCategory(String goodsSceneCategory)
    {
        this.goodsSceneCategory = goodsSceneCategory;
    }

    public String getGoodsSceneCategory()
    {
        return goodsSceneCategory;
    }
    public void setGoodsSceneCategorySub(String goodsSceneCategorySub)
    {
        this.goodsSceneCategorySub = goodsSceneCategorySub;
    }

    public String getGoodsSceneCategorySub()
    {
        return goodsSceneCategorySub;
    }
    public void setWeight(Integer weight)
    {
        this.weight = weight;
    }

    public Integer getWeight()
    {
        return weight;
    }
    public void setScenePrompt(String scenePrompt)
    {
        this.scenePrompt = scenePrompt;
    }

    public String getScenePrompt()
    {
        return scenePrompt;
    }
    public void setForward(String forward)
    {
        this.forward = forward;
    }

    public String getForward()
    {
        return forward;
    }
    public void setReverse(String reverse)
    {
        this.reverse = reverse;
    }

    public String getReverse()
    {
        return reverse;
    }
    public void setExecuteTime(Date executeTime)
    {
        this.executeTime = executeTime;
    }

    public Date getExecuteTime()
    {
        return executeTime;
    }
    public void setResultImgUrls(String resultImgUrls)
    {
        this.resultImgUrls = resultImgUrls;
    }

    public String getResultImgUrls()
    {
        return resultImgUrls;
    }
    public void setDelFlag(Long delFlag)
    {
        this.delFlag = delFlag;
    }

    public Long getDelFlag()
    {
        return delFlag;
    }

    public List<OrdinalImgResult> getOrdinalImgResultList()
    {
        return ordinalImgResultList;
    }

    public void setOrdinalImgResultList(List<OrdinalImgResult> ordinalImgResultList)
    {
        this.ordinalImgResultList = ordinalImgResultList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("taskOrdinalId", getTaskOrdinalId())
                .append("name", getName())
                .append("taskId", getTaskId())
                .append("type", getType())
                .append("userId", getUserId())
                .append("referedTaskId", getReferedTaskId())
                .append("referedTaskOrdinalId", getReferedTaskOrdinalId())
                .append("ordinal", getOrdinal())
                .append("status", getStatus())
                .append("originImgUrl", getOriginImgUrl())
                .append("markImgUrl", getMarkImgUrl())
                .append("desType", getDesType())
                .append("shortCutDesc", getShortCutDesc())
                .append("modelPrompt", getModelPrompt())
                .append("modelCharacterId", getModelCharacterId())
                .append("modelSex", getModelSex())
                .append("modelAge", getModelAge())
                .append("modelSkin", getModelSkin())
                .append("modelExpression", getModelExpression())
                .append("modelTemperament", getModelTemperament())
                .append("modelImgId", getModelImgId())
                .append("modelUploadUrl", getModelUploadUrl())
                .append("sceneType", getSceneType())
                .append("sceneTypeStr", getSceneTypeStr())
                .append("sceneCategoryLarge", getSceneCategoryLarge())
                .append("sceneCategorySmall", getSceneCategorySmall())
                .append("sceneImgId", getSceneImgId())
                .append("sceneId", getSceneId())
                .append("goodsSceneCategory", getGoodsSceneCategory())
                .append("goodsSceneCategorySub", getGoodsSceneCategorySub())
                .append("weight", getWeight())
                .append("scenePrompt", getScenePrompt())
                .append("forward", getForward())
                .append("reverse", getReverse())
                .append("executeTime", getExecuteTime())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("resultImgUrls", getResultImgUrls())
                .append("delFlag", getDelFlag())
                .append("ordinalImgResultList", getOrdinalImgResultList())
                .toString();
    }

    public WebCharacterModel getWebCharacterModel() {
        return webCharacterModel;
    }

    public void setWebCharacterModel(WebCharacterModel webCharacterModel) {
        this.webCharacterModel = webCharacterModel;
    }

    private WebCharacterModel webCharacterModel;

    public WebScenRealHuman getWebScenRealHuman() {
        return webScenRealHuman;
    }

    public void setWebScenRealHuman(WebScenRealHuman webScenRealHuman) {
        this.webScenRealHuman = webScenRealHuman;
    }

    private WebScenRealHuman webScenRealHuman;

    public WebScenGoodsCategorySub getWebScenGoodsCategorySub() {
        return webScenGoodsCategorySub;
    }

    public void setWebScenGoodsCategorySub(WebScenGoodsCategorySub webScenGoodsCategorySub) {
        this.webScenGoodsCategorySub = webScenGoodsCategorySub;
    }

    private WebScenGoodsCategorySub webScenGoodsCategorySub;



}
