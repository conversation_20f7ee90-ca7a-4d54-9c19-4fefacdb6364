package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.WebShortcutGoods;

/**
 * 快捷模板-推荐信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-04
 */
public interface IWebShortcutGoodsService 
{
    /**
     * 查询快捷模板-推荐信息
     * 
     * @param id 快捷模板-推荐信息主键
     * @return 快捷模板-推荐信息
     */
    public WebShortcutGoods selectWebShortcutGoodsById(Integer id);

    /**
     * 查询快捷模板-推荐信息列表
     * 
     * @param webShortcutGoods 快捷模板-推荐信息
     * @return 快捷模板-推荐信息集合
     */
    public List<WebShortcutGoods> selectWebShortcutGoodsList(WebShortcutGoods webShortcutGoods);

    /**
     * 新增快捷模板-推荐信息
     * 
     * @param webShortcutGoods 快捷模板-推荐信息
     * @return 结果
     */
    public int insertWebShortcutGoods(WebShortcutGoods webShortcutGoods);

    /**
     * 修改快捷模板-推荐信息
     * 
     * @param webShortcutGoods 快捷模板-推荐信息
     * @return 结果
     */
    public int updateWebShortcutGoods(WebShortcutGoods webShortcutGoods);

    /**
     * 批量删除快捷模板-推荐信息
     * 
     * @param ids 需要删除的快捷模板-推荐信息主键集合
     * @return 结果
     */
    public int deleteWebShortcutGoodsByIds(Integer[] ids);

    /**
     * 删除快捷模板-推荐信息信息
     * 
     * @param id 快捷模板-推荐信息主键
     * @return 结果
     */
    public int deleteWebShortcutGoodsById(Integer id);
}
