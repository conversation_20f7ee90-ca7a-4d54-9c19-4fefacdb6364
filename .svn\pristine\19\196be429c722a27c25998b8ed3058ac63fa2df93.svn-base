package com.dataxai.web.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.PageDomain;
import com.dataxai.common.core.page.TableSupport;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.sql.SqlUtil;
import com.dataxai.web.Constants.CommonStatusEnum;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import com.dataxai.web.dto.BatchDTO;
import com.dataxai.web.dto.BatchPageDTO;
import com.dataxai.web.dto.PromptDTO;
import com.dataxai.web.dto.ZIPDTO;
import com.dataxai.web.mapper.*;
import com.dataxai.web.service.AliYunFileService;
import com.dataxai.web.service.BatchService;
import com.dataxai.web.utils.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
/**
* <AUTHOR>
* @description 针对表【t_batch(任务批次表)】的数据库操作Service实现
* @createDate 2025-04-10 09:45:16
*/
@Service
@Slf4j
public class BatchServiceImpl implements BatchService {

    @Autowired
    private TaskServiceImpl taskServiceImpl;

    @Autowired
    private AliYunFileService aliYunFileService;

    @Autowired
    private BatchMapper batchMapper;

    @Autowired
    private TaskMapper taskMapper;
    @Autowired
    private OrdinalImgResultMapper ordinalImgResultMapper;

    @Autowired
    private TaskOrdinalMapper taskOrdinalMapper;

    @Value("${mydesgin.url}")
    private String url;

    @Value("${mydesgin.sign}")
    private String sign;

    @Value("${python.gpu_read_pictures}")
    private String readPictures;


    /**
     * 新增批次信息
     * @param dto
     * @return
     */
    @Override
    public Batch add(BatchDTO dto, List<MultipartFile> files,MultipartFile table,List<Path> pathList) throws IOException {
        //验证积分够不够扣
        Integer amount = checkIntegral(dto, table, files,pathList);
        //新增批次信息
        Batch batch = insertBatchInfo(dto,amount);
        return batch;
    }


    @Async("taskExecutor")
    public void insertTaskAndOrdinalAsyncNew(BatchDTO dto, Batch batch, List<byte[]> fileBytesList, byte[] tableBytes,
                                          Long userId,List<MultipartFile> files,List<Path> pathList)throws IOException {
        if(dto.getType() == 15){
            // 新建任务 并且任务存缩略图
            List<Task> taskList = new ArrayList<>();
            for (byte[] fileBytes : fileBytesList) {
                String imgUrl = aliYunFileService.upload(new ByteArrayResource(fileBytes));
                // 生成缩略图并转换为MultipartFile
                File thumbnailFile = ThumbnailUtils.generateThumbnail(fileBytes,300);
                MultipartFile thumbnailMultipartFile = new FileAdapter(thumbnailFile);
                // 上传缩略图
                String thumbnailUrl =  aliYunFileService.uploadALiYun(thumbnailMultipartFile);
                // 清理临时文件
                thumbnailFile.delete();
                Task task = new Task();
                task.setBatchId(batch.getBatchId());
                task.setTaskId(SnowFlakeUtils.nextIdStr());
                task.setStatus(Constants.TASK_STATUS_SUCCESS);
                task.setType(dto.getType());
                task.setOriginalUrl(CommonUtils.subCosPrefix(imgUrl));
                task.setThumbnailUrl(CommonUtils.subCosPrefix(thumbnailUrl));
                task.setUserId(userId);
                task.setCreateTime(batch.getCreateTime());
                task.setUpdateTime(batch.getCreateTime());
                task.setDelFlag(0);
                task.setDescStatus(0);
                taskList.add(task);
                Batch batchDb = batchMapper.selectById(batch.getBatchId());
                if(batchDb.getSuccessAmount() ==null){
                    batchDb.setSuccessAmount(1);
                }else{
                    batchDb.setSuccessAmount(batchDb.getSuccessAmount()+1);
                }
                if(batchDb.getTotalAmount() == batchDb.getSuccessAmount()){
                    batchDb.setStatus(Constants.TASK_STATUS_SUCCESS);
                }else{
                    batchDb.setStatus(Constants.TASK_STATUS_EXECUTING);
                }
                batchMapper.updateBat(batchDb);
            }

            if(taskList.size()>0){
                taskMapper.insertBatchTask(taskList);
            }

        }else{
            List<Task> taskList = new ArrayList<>();
            List<String> imgUrl = new ArrayList<>();

            log.info("图片一共："+ pathList.size() + "张");
            if (!CollectionUtil.isEmpty(pathList) && !pathList.isEmpty()){
                //上传文件，获取imgUrl
                //获取上传文件(返回的为文件存储的路径)
                List<MultipartFile> multipartFiles = new ArrayList<>();
                for (Path path : pathList) {
                    try {
                        // 文件处理（如上传到云存储、数据库操作等）
                        MultipartFile multipartFile = convertPathToMultipartFile(path);
                        multipartFiles.add(multipartFile);
                        // 处理成功后删除临时文件
                        deleteTempFile(path);
                    } catch (Exception e) {
                        // 处理失败：记录日志，可添加重试逻辑
                        logProcessingError(path, e);
                    }
                }
                List<String> urlList  = aliYunFileService.batchUploadALiYun(multipartFiles);
                log.info("上传后，图片一共："+ urlList.size() + "张");
                String url = urlList.stream().collect(Collectors.joining(","));
                dto.setImgUrl(url);
            }

            if(StringUtils.isNotEmpty(dto.getImgUrl())){
                String[] split = dto.getImgUrl().split(",");
                imgUrl = Arrays.asList(split);
            }
            ObjectMapper objectMapper = new ObjectMapper();
            String taskParam = new String();
            OrdinalParamDTO param = new OrdinalParamDTO();
            if(StringUtils.isNotEmpty(dto.getTaskParam())){
                taskParam = dto.getTaskParam();
                param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
            }
            ObjectMapper mapper = new ObjectMapper();
            if(dto.getType() != 8 && dto.getType() != 9){
                // 相似图裂变 和批量上传图片是生成4张结果图 其他类型是 生成 1张结果图、
                param.setUsedIntegral(1l);
                param.setImageNumber(1);
            }else if(dto.getType() ==12 && param.getMagnification() == 4){
                // 放大倍数是4 扣2积分
                param.setUsedIntegral(2L);
                param.setImageNumber(1);

            }else{
                param.setImageNumber(4);

            }
            taskParam = mapper.writeValueAsString(param);
            List<TaskOrdinal> taskOrdinalList = new ArrayList<>();

            // 如果是表格导入
            if(dto.getType() == 8 && tableBytes != null){
                List<PromptDTO> promptDTOS = PromptDTO.parseTableBytes(tableBytes);
                for (PromptDTO promptDTO : promptDTOS) {
                    Task task = insertTask(dto, userId, param, batch);
                    task.setDescStatus(0);
                    taskList.add(task);
                    TaskOrdinal taskOrdinal =insertTaskOriginalBase(userId,taskParam,dto.getType(),task.getTaskId(),param.getOriginImgUrl(),batch);
                    taskOrdinal.setShortCutDesc(promptDTO.getPrompt());
                    taskOrdinalList.add(taskOrdinal);
                }
            }else if(dto.getType() == 8 && StringUtils.isNotEmpty(dto.getImgUrl())){
                log.info("操作批次ID<<<<<<"+batch.getBatchId()+"文生图导入的图片信息<<<<<<"+dto.getImgUrl());
                //如果是文生图图片导入
                for (String url : imgUrl) {
                    log.info("操作调用图片识图转文接口参数<<<<<<,"+url);
                    Task task = insertTask(dto, userId, param, batch);
                    task.setOriginalUrl(CommonUtils.subCosPrefix(url));
                    task.setDescStatus(0);
                    taskList.add(task);
                    TaskOrdinal taskOrdinal =insertTaskOriginalBase(userId,taskParam,dto.getType(),task.getTaskId(),url,batch);
                    taskOrdinal.setIsFive("1");
                    taskOrdinalList.add(taskOrdinal);
                }
            }else if((dto.getType() == 9 || dto.getType() == 11 || dto.getType() == 12 || dto.getType() == 14) && StringUtils.isNotEmpty(dto.getImgUrl())){
                for (String url : imgUrl) {
                    Task task = insertTask(dto, userId, param, batch);
                    task.setOriginalUrl(CommonUtils.subCosPrefix(url));
                    task.setDescStatus(0);
                    taskList.add(task);
                    TaskOrdinal taskOrdinal =insertTaskOriginalBase(userId,taskParam,dto.getType(),task.getTaskId(),url,batch);
                    taskOrdinalList.add(taskOrdinal);
                }
            }

            if (!taskList.isEmpty()){
                taskMapper.insertBatchTask(taskList);
            }

            if (!taskOrdinalList.isEmpty()){
                for (TaskOrdinal curTaskOrdinal : taskOrdinalList){
                    taskServiceImpl.executeTask(curTaskOrdinal);
                }
            }
        }

    }

    private MultipartFile convertPathToMultipartFile(Path path) throws IOException {
        // 读取文件内容
        byte[] content = Files.readAllBytes(path);

        // 创建 MockMultipartFile（参数：字段名、文件名、内容类型、字节内容）
        return new CustomMultipartFile(
            "file", // 字段名（可自定义）
            path.getFileName().toString(),
            Files.probeContentType(path),
            content
        );
    }

    private void deleteTempFile(Path path) {
        try {
            Files.deleteIfExists(path);
            System.out.println("已删除临时文件: " + path.getFileName());
        } catch (IOException e) {
            System.err.println("删除临时文件失败: " + path + ", 原因: " + e.getMessage());
        }
    }

    private void logProcessingError(Path path, Exception e) {
        System.err.println("处理文件失败: " + path.getFileName() + ", 错误: " + e.getMessage());
        e.printStackTrace();
    }

    //@Async
    @Async("taskExecutor")
    public void insertTaskAndOrdinalAsync(BatchDTO dto, Batch batch,List<byte[]> fileBytesList, byte[] tableBytes,Long userId) throws IOException {
        Date date = new Date();
        //dto.setTaskParam("{\"styleId\":50,\"style\":\"复古霓虹\",\"stylePrompt\":\"A neon sign-inspired design of [用户关键词], glowing text effects, dark background, retro 80s and 90s arcade vibes, bright and eye-catching.\"}");
        if(dto.getType() == 15){
            // 新建任务 并且任务存缩略图
            List<Task> taskList = new ArrayList<>();
            for (byte[] fileBytes : fileBytesList) {
                String imgUrl = aliYunFileService.upload(new ByteArrayResource(fileBytes));
                // 生成缩略图并转换为MultipartFile
                File thumbnailFile = ThumbnailUtils.generateThumbnail(fileBytes,300);
                MultipartFile thumbnailMultipartFile = new FileAdapter(thumbnailFile);
                // 上传缩略图
                String thumbnailUrl =  aliYunFileService.uploadALiYun(thumbnailMultipartFile);
                // 清理临时文件
                thumbnailFile.delete();
                Task task = new Task();
                task.setBatchId(batch.getBatchId());
                task.setTaskId(SnowFlakeUtils.nextIdStr());
                task.setStatus(Constants.TASK_STATUS_SUCCESS);
                task.setType(dto.getType());
                task.setOriginalUrl(CommonUtils.subCosPrefix(imgUrl));
                task.setThumbnailUrl(CommonUtils.subCosPrefix(thumbnailUrl));
                task.setUserId(userId);
                task.setCreateTime(date);
                task.setUpdateTime(date);
                task.setDelFlag(0);
//                taskMapper.insertTask(task);
                task.setDescStatus(0);
                taskList.add(task);
                Batch batchDb = batchMapper.selectById(batch.getBatchId());
                if(batchDb.getSuccessAmount() ==null){
                    batchDb.setSuccessAmount(1);
                }else{
                    batchDb.setSuccessAmount(batchDb.getSuccessAmount()+1);
                }
                if(batchDb.getTotalAmount() == batchDb.getSuccessAmount()){
                    batchDb.setStatus(Constants.TASK_STATUS_SUCCESS);
                }else{
                    batchDb.setStatus(Constants.TASK_STATUS_EXECUTING);
                }
                batchMapper.updateBat(batchDb);
            }

            if(taskList.size()>0){
                taskMapper.insertBatchTask(taskList);
            }

        }else{
            List<Task> taskList = new ArrayList<>();
            List<String> imgUrl = new ArrayList<>();
            if(StringUtils.isNotEmpty(dto.getImgUrl())){
                String[] split = dto.getImgUrl().split(",");
                imgUrl = Arrays.asList(split);
            }
            ObjectMapper objectMapper = new ObjectMapper();
            String taskParam = new String();
            OrdinalParamDTO param = new OrdinalParamDTO();
            if(StringUtils.isNotEmpty(dto.getTaskParam())){
                taskParam = dto.getTaskParam();
                param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
            }
            ObjectMapper mapper = new ObjectMapper();
            if(dto.getType() != 8 && dto.getType() != 9){
                // 相似图裂变 和批量上传图片是生成4张结果图 其他类型是 生成 1张结果图、
                param.setUsedIntegral(1l);
                param.setImageNumber(1);
            }else if(dto.getType() ==12 && param.getMagnification() == 4){
                // 放大倍数是4 扣2积分
                param.setUsedIntegral(2L);
                param.setImageNumber(1);

            }else{
//                param.setUsedIntegral(4L);
                param.setImageNumber(4);

            }
            taskParam = mapper.writeValueAsString(param);

            List<TaskOrdinal> taskOrdinalList = new ArrayList<>();

            // 如果是表格导入
            if(dto.getType() == 8 && tableBytes != null){
                List<PromptDTO> promptDTOS = PromptDTO.parseTableBytes(tableBytes);
                for (PromptDTO promptDTO : promptDTOS) {
                    Task task = insertTask(dto, userId, param, batch);
                    task.setDescStatus(0);
                    taskList.add(task);
//                    taskMapper.insertTask(task);
                    TaskOrdinal taskOrdinal =insertTaskOriginalBase(userId,taskParam,dto.getType(),task.getTaskId(),param.getOriginImgUrl(),batch);
                    taskOrdinal.setShortCutDesc(promptDTO.getPrompt());
//                    taskServiceImpl.executeTask(taskOrdinal);
                    taskOrdinalList.add(taskOrdinal);
                }
            }else if(dto.getType() == 8 && StringUtils.isNotEmpty(dto.getImgUrl())){
                log.info("操作批次ID<<<<<<"+batch.getBatchId()+"文生图导入的图片信息<<<<<<"+dto.getImgUrl());
                //如果是文生图图片导入
                for (String url : imgUrl) {
                    log.info("操作调用图片识图转文接口参数<<<<<<,"+url);
//                    String requestUrl = readPictures;
//                    String requestUrlAll = requestUrl+"?type="+ dto.getType() +"&url="+url;
//                    //设置调用超时时间为2分钟
//                    String response = HttpUtil.get(requestUrlAll,120000);
//                    if (response.startsWith("\"") && response.endsWith("\"")) {
//                        response = response.substring(1, response.length() - 1);
//                    }
                    Task task = insertTask(dto, userId, param, batch);
                    task.setOriginalUrl(CommonUtils.subCosPrefix(url));
//                    taskMapper.insertTask(task);
                    task.setDescStatus(0);
                    taskList.add(task);
                    TaskOrdinal taskOrdinal =insertTaskOriginalBase(userId,taskParam,dto.getType(),task.getTaskId(),url,batch);
//                    taskOrdinal.setShortCutDesc(response);
//                    taskServiceImpl.executeTask(taskOrdinal);
                    taskOrdinalList.add(taskOrdinal);
                }
            }else if((dto.getType() == 9 || dto.getType() == 11 || dto.getType() == 12 || dto.getType() == 14) && StringUtils.isNotEmpty(dto.getImgUrl())){
                for (String url : imgUrl) {
                    Task task = insertTask(dto, userId, param, batch);
                    task.setOriginalUrl(CommonUtils.subCosPrefix(url));
//                    taskMapper.insertTask(task);
                    task.setDescStatus(0);
                    taskList.add(task);
                    TaskOrdinal taskOrdinal =insertTaskOriginalBase(userId,taskParam,dto.getType(),task.getTaskId(),url,batch);
//                    taskServiceImpl.executeTask(taskOrdinal);
                    taskOrdinalList.add(taskOrdinal);
                }
            }

            if (taskList.size()>0){
                taskMapper.insertBatchTask(taskList);
            }

            //需要后续优化
            if (taskOrdinalList.size()>0){
                for (TaskOrdinal curTaskOrdinal : taskOrdinalList){
                    taskServiceImpl.executeTask(curTaskOrdinal);
                }
            }
        }
    }

    /**
     *
     * @param userId  用户id
     * @param taskParam  其他json 格式的参数
     * @param type  任务type
     * @param taskId 任务 ID
     * @param url  原图地址
     * @return 组装的基本任务次数参数
     */
    private TaskOrdinal insertTaskOriginalBase(Long userId, String taskParam, Long type, String taskId, String url,Batch batch) {
        TaskOrdinal taskOrdinal = new TaskOrdinal();
        taskOrdinal.setUserId(userId);
        taskOrdinal.setTaskParam(taskParam);
        taskOrdinal.setType(type);
        taskOrdinal.setTaskId(taskId);
        taskOrdinal.setOriginImgUrl(CommonUtils.subCosPrefix(url));
        taskOrdinal.setIsDeductPoints(false);
        taskOrdinal.setCreateTime(batch.getCreateTime());
        taskOrdinal.setUpdateTime(batch.getUpdateTime());
        return taskOrdinal;
    }

    private Task insertTask(BatchDTO dto,Long userId,OrdinalParamDTO param,Batch batch){
        Task task = new Task();
        task.setType(dto.getType());
        task.setCreateTime(batch.getCreateTime());
        task.setUpdateTime(batch.getCreateTime());
        task.setType(dto.getType());
        task.setBatchId(batch.getBatchId());
        task.setUserId(userId);
        if(StringUtils.isNotEmpty(param.getOriginImgUrl())){
            task.setOriginalUrl(CommonUtils.subCosPrefix(param.getOriginImgUrl()));
            task.setThumbnailUrl(task.getOriginalUrl()+Constants.OOS_URL_THUMBNAIL);
        }
        task.setDelFlag(0);
        task.setTaskId(SnowFlakeUtils.nextIdStr());
        task.setStatus(Constants.TASK_STATUS_PARKING);
        return task;
    }

    @Override
    public List<Batch> getInfo(BatchPageDTO dto) {

        dto.setUserId(SecurityUtils.getUserId());

        List<Batch> batchList = batchMapper.getInfo(dto);

        if(dto.getType() == 15){
            for (Batch batch : batchList) {
                Task task = new Task();
                task.setBatchId(batch.getBatchId());
                List<Task> list = taskMapper.selectTaskListAddUrl(task);
                batch.setTasks(list);
            }
        }else{
            for (Batch batch : batchList) {
                Task task = new Task();
                task.setBatchId(batch.getBatchId());
                List<OrdinalImgResult> list = ordinalImgResultMapper.selectImageResult(batch.getBatchId());
                batch.setWaitTime(calculateWaitTime(batch,null,1));
                batch.setImgResults(list);
            }
        }

        return batchList;
    }

    private List<Task> getTaskInfo(List<Task> list) {
        if(CollectionUtil.isNotEmpty(list )){
            list= list.stream().map(
            map->{
                Task taskNew = new Task();
                BeanUtils.copyProperties(map,taskNew);
                taskNew.setOriginalUrl(CommonUtils.addCosPrefix(taskNew.getOriginalUrl()));
                taskNew.setThumbnailUrl(CommonUtils.addCosPrefix(taskNew.getThumbnailUrl()));
                return taskNew;
            }).collect(Collectors.toList());
            for (Task taskDB : list) {
                TaskOrdinal taskOrdinal = new TaskOrdinal();
                taskOrdinal.setDelFlag(0L);
                taskOrdinal.setTaskId(taskDB.getTaskId());

                List< TaskOrdinal> taskOrdinals = taskOrdinalMapper.selectTaskOrdinalList(taskOrdinal);
                if(CollectionUtil.isNotEmpty(taskOrdinals)){
                    for (TaskOrdinal ordinal : taskOrdinals) {
                        OrdinalImgResult ordinalImgResult = new OrdinalImgResult();
                        ordinalImgResult.setTaskOrdinalId(ordinal.getTaskOrdinalId());
                        ordinalImgResult.setTaskId(ordinal.getTaskId());
                        ordinalImgResult.setDelFlag(0l);
                        ordinal.setOriginImgUrl(CommonUtils.addCosPrefix(ordinal.getOriginImgUrl()));
                        List<OrdinalImgResult> ordinalImgResults = ordinalImgResultMapper.selectOrdinalImgResultList(ordinalImgResult);
                        for (OrdinalImgResult y : ordinalImgResults) {
                            y.setOriginalImgUrl(CommonUtils.addCosPrefix(y.getOriginalImgUrl()));
                            y.setResImgUrl(CommonUtils.addCosPrefix(y.getResImgUrl()));
                            y.setResSmallImgUrl(CommonUtils.addSizeHalf(y.getResSmallImgUrl()));
                            y.setResWhiteImgUrl(CommonUtils.addCosPrefix(y.getResWhiteImgUrl()));
                        }
                        ordinal.setOrdinalImgResultList(ordinalImgResults);
                    }
                    taskDB.setTaskOrdinalList(taskOrdinals);
                }
            }
        }
        return list;
    }

    @Override
    public Batch getOne(String batchId) {
        Batch batch =batchMapper.selectById(batchId);
        if(batch != null){
            Task task = new Task();
            task.setBatchId(batchId);
            task.setDelFlag(0);
            task.setStatus(Constants.TASK_STATUS_SUCCESS);
            List<Task> list = taskMapper.selectTaskList(task);
            list = getTaskInfo(list);
            batch.setTasks(list);
        }
        return batch;
    }

    @Override
    public Integer logicDeleteTaskByTaskIds(Long userId, String batchId) {
        Batch batchDB = batchMapper.selectById(batchId);
        if(batchDB.getStatus() == Constants.TASK_STATUS_SUCCESS ){
            throw new ServiceException("批次任务已结束 无法操作");
        }
        Batch batch = new Batch();
        batch.setDelFlag(1);
        batch.setBatchId(batchId);
        Integer count = batchMapper.updateBat(batch);
        Task task = new Task();
        task.setBatchId(batchId);
        List<Task> tasks = taskMapper.selectTaskList(task);
        List<String> taskIds= tasks.stream().map(map -> map.getTaskId()).collect(Collectors.toList());
        String[] array = taskIds.toArray(new String[0]);
        if(CollectionUtil.isNotEmpty(taskIds)){
            //taskServiceImpl.deleteImageAliYun(array,userId,2);
            taskMapper.logicDeleteTaskOrdinalByTaskIds(userId,array);
           taskMapper.logicDeleteTaskByTaskIds(userId,array);
        }
        return count;
    }

    /**
     * 终止任务
     * @param userId
     * @param batchId
     * @return
     */
    @Override
    public Integer stop(Long userId, String batchId) {
        Batch batchDB = batchMapper.selectById(batchId);
        if(batchDB.getStatus() == Constants.TASK_STATUS_SUCCESS ){
            throw new ServiceException("批次任务已结束 无法操作");
        }
        Batch batch = new Batch();
        batch.setStatus(Constants.TASK_STATUS_STOP);
        batch.setBatchId(batchId);
        Integer count = batchMapper.updateBat(batch);
        Task task = new Task();
        task.setBatchId(batchId);
        task.setStatus(Constants.TASK_STATUS_STOP);
        taskMapper.updateTask(task);
        taskMapper.updateTaskStatus(task);
        List<Task> tasks = taskMapper.selectTaskList(task);
        List<String> taskIds= tasks.stream().map(map -> map.getTaskId()).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(taskIds)){
            for (String taskId : taskIds) {
                TaskOrdinal taskOrdinal = new TaskOrdinal();
                taskOrdinal.setTaskId(taskId);
                taskOrdinal.setStatus(Constants.TASK_STATUS_STOP);
                taskOrdinalMapper.updateTaskOrdinalStatus(taskOrdinal);
            }
        }
        return count;

    }

    @Override
    public Integer updateImage(MultipartFile file, String imageId) throws IOException {
        OrdinalImgResult ordinalImgResult = ordinalImgResultMapper.selectOrdinalImgResultByImageId(imageId);
        if(ordinalImgResult == null){
            throw new ServiceException("图片结果不存在");
        }
        String resImgUrl = aliYunFileService.uploadALiYun(file);
        ordinalImgResult.setResImgUrl(CommonUtils.subCosPrefix(resImgUrl));
        ordinalImgResult.setResSmallImgUrl(CommonUtils.subCosPrefix(resImgUrl+Constants.OSS_URL_HALF));
        ordinalImgResult.setUpdateTime(new Date());
        ordinalImgResult.setUpdateBy(SecurityUtils.getUserId().toString());
        Integer count = ordinalImgResultMapper.updateOrdinalImgResult(ordinalImgResult);
        return count;
    }

    @Override
    public HashMap<String,Object> getOnePage(BatchPageDTO dto) {
        Batch batch =batchMapper.selectById(dto.getBatchId());
        if(batch != null){
            PageDomain pageDomain = TableSupport.buildPageRequest();
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            Boolean reasonable = pageDomain.getReasonable();
            PageHelper.startPage(dto.getPageNumber(), dto.getPageSize(), orderBy).setReasonable(reasonable);
            Task task = new Task();
            task.setBatchId(dto.getBatchId());
            task.setDelFlag(0);
            task.setStatus(Constants.TASK_STATUS_SUCCESS);
            List<Task> originalList = taskMapper.selectTaskList(task); // 原始分页结果
            PageInfo<Task> pageInfo = new PageInfo<>(originalList);
            long total = pageInfo.getTotal(); // 正确的总记录数
            List<Task> processedList = getTaskInfo(originalList);
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("total", total);
            dataMap.put("data", processedList);
            dataMap.put("batch", batch);

            return dataMap;
        }
        return null;
    }

    /**
     * 将图片同步到设计器
     */

    @Override
    public String synchronization(ZIPDTO dto) throws Exception {
        String imageUrls = dto.getImageUrls();
        String phone = SecurityUtils.getLoginUser().getAppUser().getPhone();
        System.out.println("登录人的手机号"+phone);
        if( StringUtils.isNotEmpty(phone)){
            String[] split = imageUrls.split(",");
            String subUrl = CommonUtils.subCosPrefix(split[0]);
            //先去结果表查数据res_img_url/res_white_img_url，没有的话就去主任务表查original_url
            OrdinalImgResult imgResult = ordinalImgResultMapper.selectTypeByUrl(subUrl);
            String typeToString = null;
            if(imgResult != null) {
                typeToString = getTypeToString(imgResult.getType().intValue());
            }else {
                if(null!=dto.getBatchId()){
                    Task task = taskMapper.selectTypeByUrlAndBatchId(subUrl,dto.getBatchId());
                    typeToString = getTypeToString(task.getType().intValue());
                }else{
                    Task task = taskMapper.selectTypeByUrl(subUrl);
                    typeToString = getTypeToString(task.getType().intValue());
                }
            }
            // 调用设计器接口 获得请求
            String currentTime = String.valueOf(System.currentTimeMillis());
            // 构造请求体数据
            Map<String, Object> badyData = new HashMap<>();
            badyData.put("phone", phone);
            badyData.put("images", imageUrls);
            badyData.put("name", typeToString);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("time", currentTime);
            requestBody.put("data", badyData);
            requestBody.put("sign",currentTime+sign);
            // 将 Map 转换为 JSON 字符串
            String jsonBody = JSONUtil.toJsonStr(requestBody);
            System.out.println("requestBody请求体:"+jsonBody);
            // 发送 POST 请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json").body(jsonBody).execute();
            //             输出响应状态码和内容
             String body = response.body();
            log.info("同步定制器返回信息："+response.body());
            // 创建 ObjectMapper 实例  将 JSON 字符串解析为 JsonNode 对象
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode jsonNode = objectMapper.readTree(body);
            // 提取 msg 字段
            if(response.getStatus()== R.SUCCESS && jsonNode.get("flag").asText().equals( "1") ){
                //业务逻辑是根据传入的图片地址集合，取第一个图片对应任务id和图片地址集合等构造参数跳转设计器接口
                //跳转成功后更新图片list在result表标识，用来返回给前端是否上传过设计器。
                List<String> urlList = Arrays.asList(split);
                List<ZIPDTO> list = new ArrayList<>();
                for (String url1 : urlList) {
                    ZIPDTO zipdto = new ZIPDTO(CommonUtils.subCosPrefix(url1));
                    list.add(zipdto);
                }
                //类型15的图片只存在主任务表，所以类型15只更新主任务,其它更新result结果表
                if("15".equals(dto.getType())){
                    ordinalImgResultMapper.batchUpdateTaskFlags(list, true,dto.getBatchId());
                }else{
                    ordinalImgResultMapper.batchUpdateImageFlags(list, true);
                }
                return "上传成功";
                //return null;
            }
/*            else if("13160056061".equals(phone)){
                List<String> urlList = Arrays.asList(split);
                List<ZIPDTO> list = new ArrayList<>();
                for (String url1 : urlList) {
                    ZIPDTO zipdto = new ZIPDTO(CommonUtils.subCosPrefix(url1));
                    list.add(zipdto);
                }
                //类型15的图片只存在主任务表，所以类型15只更新主任务,其它更新result结果表
                if("15".equals(dto.getType())){
                    ordinalImgResultMapper.batchUpdateTaskFlags(list, true,dto.getBatchId());
                }else{
                    ordinalImgResultMapper.batchUpdateImageFlags(list, true);
                }
                return "上传成功";
            }*/
            else{
                throw new ServiceException(jsonNode.get("msg").asText());
            }
        }else{
            throw new ServiceException("用户手机号不存在");
        }
    }

    private String getTypeToString(Integer type) {
        if(type == 8){
            return "文生图";
        }else if(type == 9){
            return "相似图裂变";
        }else if(type == 11){
            return "图片去背景";
        }else if(type == 12){
            return "图片变清晰";
        }else if(type == 14){
            return "图案裁剪";
        }else if(type == 15){
            return "图片上传";
        }
        throw new ServiceException("类型错误");
    }

    private Batch insertBatchInfo(BatchDTO dto,Integer amount) {
        Batch batch = new Batch();
        BeanUtils.copyProperties(dto,batch);
        Date date = new Date();
        batch.setCreateTime(date);
        batch.setUpdateTime(date);
        Long userId = SecurityUtils.getUserId();
        // 获取时间戳（毫秒级）
        String timestamp = String.valueOf(System.currentTimeMillis());
        batch.setUserId(userId);
        batch.setBatchNumber(userId.toString()+timestamp);
        batch.setTotalAmount(amount);
        batch.setDelFlag(0);
        batch.setStatus(Constants.TASK_STATUS_PARKING);
        String batchId = SnowFlakeUtils.nextIdStr();
        batch.setBatchId(batchId);
        batchMapper.insertBat(batch);
        return batch;

    }



    /**
     * 验证积分够不够扣
     * @param dto
     * @param table
     */
    private Integer checkIntegral(BatchDTO dto, MultipartFile table,List<MultipartFile> files,List<Path> pathList) throws JsonProcessingException {
        Integer amount= 0;
        Long integral =0l;
        if(dto.getType() != 15){
            //如果类型不是15 需要验证积分是否可以够抵扣
            if((dto.getType() ==11 || dto.getType() == 12 || dto.getType() ==14 || dto.getType()==9) && StringUtils.isEmpty(dto.getImgUrl())){
                throw new ServiceException("图片地址不能为空");
            }else if((dto.getType() ==11 || dto.getType() == 12 || dto.getType() ==14 || dto.getType()==9) && StringUtils.isNotEmpty(dto.getImgUrl())){
                String[] split = dto.getImgUrl().split(",");
                int slength = !pathList.isEmpty() ? pathList.size() : split.length;
                integral = (long) slength;
                amount = slength;
                // 如果是相似图裂变 则抵扣 4倍积分
                if(dto.getType() == 9){
                    integral = (long) slength * 4;
                }
                if(dto.getType()==12 ){
                    ObjectMapper objectMapper = new ObjectMapper();
                    OrdinalParamDTO  param = objectMapper.readValue(dto.getTaskParam(), OrdinalParamDTO.class);
                    if(param.getMagnification() == 4){
                        integral = (long) slength * 2;
                    }
                }
            }
            if(dto.getType() == 8 && StringUtils.isNotEmpty(dto.getImgUrl())){
                String[] split = dto.getImgUrl().split(",");
                int slength = !pathList.isEmpty() ? pathList.size() : split.length;
                amount = slength;
                integral = (long) slength * 4;
            }
            if(dto.getType() == 8 && table != null){
                List<PromptDTO>  promptDTO = aliYunFileService.parseTable(table);
                amount = promptDTO.size();
                integral = (long) promptDTO.size() * 4;
            }
            System.out.println("应该抵扣的积分是：" + integral);

        }else{
            amount = files.size();
            integral= (long) files.size();
        }
        boolean deductioned =  taskServiceImpl.deductionPoints(SecurityUtils.getUserId(), integral);
        if (!deductioned) {
            throw new ServiceException(CommonStatusEnum.SCORE_ERROR.getMessage(), CommonStatusEnum.SCORE_ERROR.getCode());
        }
        Long userId = SecurityUtils.getUserId();
        taskServiceImpl.addScoreHistoryLog(userId, Constants.SCORE_TYPE_4, -integral);
        return amount;
    }

    //计算等待时间
    private int calculateWaitTime(Batch batch,String taskId,int isBatch){
        String type = "";
        String createTime = "";
        if (isBatch == 1){
            if (batch == null || !"4".equals(String.valueOf(batch.getStatus()))) {
                return 0;
            }
            type = batch.getType().toString();
            createTime = dateFormat(batch.getCreateTime());

        }else{
            Task task = taskMapper.selectTaskByTaskId(taskId);
            if (task == null || !"4".equals(String.valueOf(task.getStatus()))) {
                return 0;
            }
            type = task.getType().toString();
            createTime = dateFormat(task.getCreateTime());
        }
        //获取这个任务的GPU数量
        int gpuCount = taskMapper.getGpuCount(isBatch,type);

        //获取之前等待中的数据和时间
        List<Map<String, String>> waitList = taskMapper.getWaitList(isBatch,createTime,type);
        if (waitList.isEmpty()) {
            return 0;
        }

        List<Integer> timeList = new ArrayList<>();
        for (Map<String,String> waitMap : waitList) {
            timeList.add((int) Math.ceil(Double.parseDouble(String.valueOf(waitMap.get("totalTime")))));
        }
        List<Integer> waitingTimes = WaitSeqUtils.calculateWindowWaitingTimes(timeList, gpuCount);
        if (waitingTimes.isEmpty()) {
            return 0;
        }

        //获取list最小的那个
        log.info("等待时间 waitingTimes: " + waitingTimes.toString());
        int time = waitingTimes.stream()
            .mapToInt(Integer::intValue)
            .min()
            .getAsInt();

        return time == 0 ? 0 : (int) Math.ceil((double) time / 60);
    }

    private String dateFormat(Date date) {
        if (date == null) {
            return null;
        }
        // 将 Date 转换为 Instant，再结合系统默认时区转换为 LocalDateTime
        LocalDateTime localDateTime = date.toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime();

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return df.format(localDateTime);
    }
}




