<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="套餐档位id" prop="tariffPackageSubId">
        <el-input
          v-model="queryParams.tariffPackageSubId"
          placeholder="请输入套餐档位id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="权益名称" prop="tariffPackageSubName">
        <el-input
          v-model="queryParams.tariffPackageSubName"
          placeholder="请输入权益名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="父套餐id" prop="parentId">
        <el-input
          v-model="queryParams.parentId"
          placeholder="请输入父套餐id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="积分" prop="score">
        <el-input
          v-model="queryParams.score"
          placeholder="请输入积分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐的生效的开始时间">
        <el-date-picker
          v-model="daterangeStartTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="套餐的过期的截止时间">
        <el-date-picker
          v-model="daterangeEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="金额" prop="subMoney">
        <el-input
          v-model="queryParams.subMoney"
          placeholder="请输入金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="套餐有效周期" prop="cycle">
        <el-input
          v-model="queryParams.cycle"
          placeholder="请输入套餐有效周期"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否上架" prop="subOnSale">
        <el-select v-model="queryParams.subOnSale" placeholder="请选择是否上架" clearable>
          <el-option
            v-for="dict in dict.type.on_sale"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['subpackage:adminsub:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['subpackage:adminsub:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['subpackage:adminsub:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['subpackage:adminsub:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="adminsubList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="套餐档位id" align="center" prop="tariffPackageSubId" />
      <el-table-column label="权益名称" align="center" prop="tariffPackageSubName" />
      <el-table-column label="父套餐id" align="center" prop="parentId" />
      <el-table-column label="积分" align="center" prop="score" />
      <el-table-column label="套餐的生效的开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="套餐的过期的截止时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="金额" align="center" prop="subMoney" />
      <el-table-column label="套餐有效周期" align="center" prop="cycle" />
      <el-table-column label="是否上架" align="center" prop="subOnSale">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.on_sale" :value="scope.row.subOnSale"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['subpackage:adminsub:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['subpackage:adminsub:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改子套餐对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="权益名称" prop="tariffPackageSubName">
          <el-input v-model="form.tariffPackageSubName" placeholder="请输入权益名称" />
        </el-form-item>
        <el-form-item label="父套餐id" prop="parentId">
          <el-input v-model="form.parentId" placeholder="请输入父套餐id" />
        </el-form-item>
        <el-form-item label="积分" prop="score">
          <el-input v-model="form.score" placeholder="请输入积分" />
        </el-form-item>
        <el-form-item label="套餐的生效的开始时间" prop="startTime">
          <el-date-picker clearable
            v-model="form.startTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择套餐的生效的开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="套餐的过期的截止时间" prop="endTime">
          <el-date-picker clearable
            v-model="form.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择套餐的过期的截止时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="金额" prop="subMoney">
          <el-input v-model="form.subMoney" placeholder="请输入金额" />
        </el-form-item>
        <el-form-item label="套餐有效周期" prop="cycle">
          <el-input v-model="form.cycle" placeholder="请输入套餐有效周期" />
        </el-form-item>
        <el-form-item label="是否上架" prop="subOnSale">
          <el-select v-model="form.subOnSale" placeholder="请选择是否上架">
            <el-option
              v-for="dict in dict.type.on_sale"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAdminsub, getAdminsub, delAdminsub, addAdminsub, updateAdminsub } from "@/api/subpackage/adminsub";

export default {
  name: "Adminsub",
  dicts: ['on_sale'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 子套餐表格数据
      adminsubList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否上架时间范围
      daterangeStartTime: [],
      // 是否上架时间范围
      daterangeEndTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tariffPackageSubId: null,
        tariffPackageSubName: null,
        parentId: null,
        score: null,
        startTime: null,
        endTime: null,
        subMoney: null,
        cycle: null,
        subOnSale: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询子套餐列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeStartTime && '' != this.daterangeStartTime) {
        this.queryParams.params["beginStartTime"] = this.daterangeStartTime[0];
        this.queryParams.params["endStartTime"] = this.daterangeStartTime[1];
      }
      if (null != this.daterangeEndTime && '' != this.daterangeEndTime) {
        this.queryParams.params["beginEndTime"] = this.daterangeEndTime[0];
        this.queryParams.params["endEndTime"] = this.daterangeEndTime[1];
      }
      listAdminsub(this.queryParams).then(response => {
        this.adminsubList = response.data;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tariffPackageSubId: null,
        tariffPackageSubName: null,
        parentId: null,
        score: null,
        startTime: null,
        endTime: null,
        subMoney: null,
        cycle: null,
        subOnSale: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeStartTime = [];
      this.daterangeEndTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.tariffPackageSubId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加子套餐";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const tariffPackageSubId = row.tariffPackageSubId || this.ids
      getAdminsub(tariffPackageSubId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改子套餐";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.tariffPackageSubId != null) {
            updateAdminsub(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAdminsub(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tariffPackageSubIds = row.tariffPackageSubId || this.ids;
      this.$modal.confirm('是否确认删除子套餐编号为"' + tariffPackageSubIds + '"的数据项？').then(function() {
        return delAdminsub(tariffPackageSubIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('subpackage/adminsub/export', {
        ...this.queryParams
      }, `adminsub_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
