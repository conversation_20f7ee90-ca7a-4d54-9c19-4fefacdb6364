package com.dataxai.web.controller.front;

import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.web.task.ProductImageUploadProcessor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 产品图片上传管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Api(tags = "产品图片上传管理")
@RestController
@RequestMapping("/product/image")
@Slf4j
public class ProductImageUploadController {

    @Autowired
    private ProductImageUploadProcessor productImageUploadProcessor;

    /**
     * 获取产品图片上传队列状态
     */
    @ApiOperation("获取产品图片上传队列状态")
    @GetMapping("/queue/status")
    public AjaxResult getQueueStatus() {
        try {
            String status = productImageUploadProcessor.getQueueStatus();
            return AjaxResult.success(status);
        } catch (Exception e) {
            log.error("获取队列状态失败：{}", e.getMessage(), e);
            return AjaxResult.error("获取队列状态失败：" + e.getMessage());
        }
    }
}
