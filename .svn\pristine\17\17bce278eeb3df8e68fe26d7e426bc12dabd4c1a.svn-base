package com.dataxai.web.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 图片收藏对象 t_favorite
 * 
 * <AUTHOR>
 * @date 2024-01-13
 */
public class Favorite extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Excel(name = "主键id")
    private String favoriteId;

    /** 所属用户id */
    @Excel(name = "所属用户id")
    private Long userId;

    /** 收藏的图片的url */
    @Excel(name = "收藏的图片的url")
    private String imgUrl;

    /** 收藏的图片所属任务id */
    @Excel(name = "收藏的图片所属任务id")
    private String taskId;

    /** 收藏的图片所属任务的第几次 */
    @Excel(name = "收藏的图片所属任务的第几次")
    private String taskOrdinalId;

    /** 是否删除(0-未删除，1-已删除) */
    private Long delFlag;

    public void setFavoriteId(String favoriteId)
    {
        this.favoriteId = favoriteId;
    }

    public String getFavoriteId()
    {
        return favoriteId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setImgUrl(String imgUrl) 
    {
        this.imgUrl = imgUrl;
    }

    public String getImgUrl() 
    {
        return imgUrl;
    }
    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskId()
    {
        return taskId;
    }
    public void setTaskOrdinalId(String taskOrdinalId)
    {
        this.taskOrdinalId = taskOrdinalId;
    }

    public String getTaskOrdinalId()
    {
        return taskOrdinalId;
    }
    public void setDelFlag(Long delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Long getDelFlag() 
    {
        return delFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("favoriteId", getFavoriteId())
            .append("userId", getUserId())
            .append("imgUrl", getImgUrl())
            .append("taskId", getTaskId())
            .append("taskOrdinalId", getTaskOrdinalId())
            .append("delFlag", getDelFlag())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .toString();
    }
}
