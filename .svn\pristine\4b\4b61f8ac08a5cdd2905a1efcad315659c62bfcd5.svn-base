package com.dataxai.web.controller.front;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.core.domain.R;
import com.dataxai.web.domain.WebGoodsDicDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.WebGoodsDic;
import com.dataxai.web.service.IWebGoodsDicService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 商品的咒语词典Controller
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
@RestController
@RequestMapping("/goods/dic")
@Api(tags = "商品图咒语信息")
public class WebGoodsDicController extends BaseController
{
    @Autowired
    private IWebGoodsDicService webGoodsDicService;

    /**
     * 查询商品的咒语词典列表
     */
//    @PreAuthorize("@ss.hasPermi('goods:dic:list')")
    @GetMapping("/list/category")
//    @ApiOperation(value = "查询商品图的咒语信息分类")
    public R<HashMap<String,Object>> listCategory()
    {
//        startPage();
        List<WebGoodsDic> list = webGoodsDicService.selectWebGoodsCategoryDicList(null);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total", total);
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    /**
     * 查询商品的咒语词典列表
     */
//    @PreAuthorize("@ss.hasPermi('goods:dic:list')")
    @PostMapping("/list")
//    @ApiOperation(value = "查询商品图的咒语信息分类下的具体咒语信息")
    public R<HashMap<String,Object>> list(WebGoodsDic webGoodsDic)
    {
//        startPage();
        List<WebGoodsDic> list = webGoodsDicService.selectWebGoodsDicList(webGoodsDic);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total", total);
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    @GetMapping("/listAll")
    @ApiOperation(value = "查询商品图的所有咒语信息")
    public R<HashMap<String,Object>> listAll()
    {
//        startPage();
        List<WebGoodsDic> listCategory = webGoodsDicService.selectWebGoodsCategoryDicList(null);
        List<Object> collect = listCategory.stream().map(item -> {
            List<WebGoodsDic> list = webGoodsDicService.selectWebGoodsDicList(item);
            WebGoodsDicDTO webGoodsDicDTO = new WebGoodsDicDTO();
            webGoodsDicDTO.setCharacteristic(item.getCharacteristic());
            webGoodsDicDTO.setData(list);
            return webGoodsDicDTO;
        }).collect(Collectors.toList());

        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(collect).getTotal();
        dataMap.put("total", total);
        dataMap.put("data",collect);
        return R.ok(dataMap);
    }

    /**
     * 导出商品的咒语词典列表
     */
    @PreAuthorize("@ss.hasPermi('goods:dic:export')")
    @Log(title = "商品的咒语词典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WebGoodsDic webGoodsDic)
    {
        List<WebGoodsDic> list = webGoodsDicService.selectWebGoodsDicList(webGoodsDic);
        ExcelUtil<WebGoodsDic> util = new ExcelUtil<WebGoodsDic>(WebGoodsDic.class);
        util.exportExcel(response, list, "商品的咒语词典数据");
    }

    /**
     * 获取商品的咒语词典详细信息
     */
//    @PreAuthorize("@ss.hasPermi('goods:dic:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "根据咒语信息id查询具体的咒语信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(webGoodsDicService.selectWebGoodsDicById(id));
    }

    /**
     * 新增商品的咒语词典
     */
    @PreAuthorize("@ss.hasPermi('goods:dic:add')")
    @Log(title = "商品的咒语词典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WebGoodsDic webGoodsDic)
    {
        return toAjax(webGoodsDicService.insertWebGoodsDic(webGoodsDic));
    }

    /**
     * 修改商品的咒语词典
     */
    @PreAuthorize("@ss.hasPermi('goods:dic:edit')")
    @Log(title = "商品的咒语词典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WebGoodsDic webGoodsDic)
    {
        return toAjax(webGoodsDicService.updateWebGoodsDic(webGoodsDic));
    }

    /**
     * 删除商品的咒语词典
     */
    @PreAuthorize("@ss.hasPermi('goods:dic:remove')")
    @Log(title = "商品的咒语词典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(webGoodsDicService.deleteWebGoodsDicByIds(ids));
    }
}
