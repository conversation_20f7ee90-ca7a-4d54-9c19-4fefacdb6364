package com.dataxai.web.controller.front;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.TableDataInfo;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.utils.uuid.IdUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import com.dataxai.web.service.*;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网站的相关配置信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-16
 */
@RestController
@RequestMapping("/front/web/info")
@Api(tags={"网站的配置信息"})
public class WebInfoController extends BaseController
{
    @Autowired
    private ITBaseinfoAgreementService tBaseinfoAgreementService;   //用户协议

    @Autowired
    private ITBaseinfoDisclaimersService tBaseinfoDisclaimersService;   //隐私政策

    @Autowired
    private ITBaseinfoManageService tBaseinfoManageService; // 公司基础信息

    @Autowired
    private ITBaseinfoBusinessService tBaseinfoBusinessService;  // 定制业务

    @Autowired
    private ITBaseinfoUseguideService tBaseinfoUseguideService; //使用指南

    @Autowired
    private ITBaseinfoContactusService tBaseinfoContactusService;   //上传指南

    /**
     * 前端页面查询 隐私政策和用户协议
     */
    @GetMapping("/protocol/{type}")
    @ApiOperation(value = "隐私政策和用户协议")
    @ApiImplicitParam(name = "type", value = "隐私政策和用户协议(0-用户协议，1-隐私政策)", required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public R<ProtocolInfo> protocol(@PathVariable("type") Long type)
    {
        ProtocolInfo protocolInfo = new ProtocolInfo();
        //用户服务协议
        if(type==0){
            List<TBaseinfoAgreement> tBaseinfoAgreements = tBaseinfoAgreementService.selectTBaseinfoAgreementList(null);
            if(CollectionUtil.isNotEmpty(tBaseinfoAgreements)){
                TBaseinfoAgreement tBaseinfoAgreement = tBaseinfoAgreements.get(0);
                protocolInfo.setTitle(tBaseinfoAgreement.getAgreementName());
                protocolInfo.setContent(tBaseinfoAgreement.getAgreementContent());
            }
        }else {
            //隐私政策
            List<TBaseinfoDisclaimers> tBaseinfoDisclaimers = tBaseinfoDisclaimersService.selectTBaseinfoDisclaimersList(null);
            if(CollectionUtil.isNotEmpty(tBaseinfoDisclaimers)){
                TBaseinfoDisclaimers tBaseinfoDisclaimers1 = tBaseinfoDisclaimers.get(0);
                protocolInfo.setTitle(tBaseinfoDisclaimers1.getDisclaimersName());
                protocolInfo.setContent(tBaseinfoDisclaimers1.getDisclaimersContent());
            }
        }
        return R.ok(protocolInfo);
    }



    /**
     * 查询公司名称，备案号，定制业务，上传指南，使用指南
     */
    @GetMapping("/base")
    @ApiOperation("查询公司名称，备案号，定制业务，上传指南，使用指南")
    public R<BaseInfo> base()
    {
        List<TBaseinfoManage> tBaseinfoManages = tBaseinfoManageService.selectTBaseinfoManageList(null);
        BaseInfo baseInfo = new BaseInfo();
        if(CollectionUtil.isNotEmpty(tBaseinfoManages)){
            for (TBaseinfoManage tBaseinfoManage : tBaseinfoManages) {
                if(tBaseinfoManage.getName().equals(Constants.COMPANY_NAME_KEY)){
                    baseInfo.setCompanyName(tBaseinfoManage.getData());
                }else {
                    baseInfo.setRecordNumber(tBaseinfoManage.getData());
                }
            }
        }

        //设置 定制业务
        List<TBaseinfoBusiness> tBaseinfoBusinesses = tBaseinfoBusinessService.selectTBaseinfoBusinessList(null);
        if(CollectionUtil.isNotEmpty(tBaseinfoBusinesses)){
            TBaseinfoBusiness tBaseinfoBusiness = tBaseinfoBusinesses.get(0);
            baseInfo.setCustomizedBusinessName(tBaseinfoBusiness.getCustomizedBusinessName());
            baseInfo.setCustomizedBusinessContent(tBaseinfoBusiness.getCustomizedBusinessContent());
        }

        //设置 使用指南
        List<TBaseinfoUseguide> tBaseinfoUseguideList = tBaseinfoUseguideService.selectTBaseinfoUseguideList(null);
        baseInfo.settBaseinfoUseguideList(tBaseinfoUseguideList);

        // 设置 上传指南
        List<TBaseinfoContactus> tBaseinfoContactuses = tBaseinfoContactusService.selectTBaseinfoContactusList(null);
        baseInfo.settBaseinfoContactusList(tBaseinfoContactuses);

        return R.ok(baseInfo);
    }





}
