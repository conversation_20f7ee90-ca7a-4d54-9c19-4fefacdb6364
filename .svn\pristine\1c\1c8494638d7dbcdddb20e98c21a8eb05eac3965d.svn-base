package com.dataxai.web.domain;

import com.dataxai.web.mapper.WxpayMapper;
import com.wechat.pay.contrib.apache.httpclient.WechatPayHttpClientBuilder;
import com.wechat.pay.contrib.apache.httpclient.auth.PrivateKeySigner;
import com.wechat.pay.contrib.apache.httpclient.auth.ScheduledUpdateCertificatesVerifier;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Credentials;
import com.wechat.pay.contrib.apache.httpclient.auth.WechatPay2Validator;
import com.wechat.pay.contrib.apache.httpclient.util.PemUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.impl.client.CloseableHttpClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;

/**
 * 微信支付配置信息
 * @TableName wxpay
 */
@Data
//@Slf4j
//@Configuration
public class Wxpay implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 证书序列号
     */
    private String mchSerialNo;

    /**
     * 私钥
     */
    private String privateKeyPath;

    /**
     * V3秘钥
     */
    private String apiV3Key;

    /**
     * 公众号ID
     */
    private String appId;

    /**
     * 微信域名
     */
    private String domain;

    /**
     * 测试回调地址
     */
    private String notifyDomain;

    /**
     * 生产回调地址
     */
    private String notifyDomainProd;

    /**
     * 验证秘钥
     */
    private String partnerKey;

    private static final long serialVersionUID = 1L;

    @Autowired
    private WxpayMapper wxpayMapper;

//    /**
//     * 获取商户的私钥文件
//     * @param filename
//     * @return
//     */
//    private PrivateKey getPrivateKey(String filename) {
//
//        System.out.println("filename = "+filename);
//        filename = "/config/"+filename;
//        ClassPathResource resource = new ClassPathResource(filename);
//        InputStream inputStream = null;
//        try {
//            inputStream = resource.getInputStream();
//            return PemUtil.loadPrivateKey(inputStream);
//        } catch (IOException e) {
//            throw new RuntimeException("私钥文件不存在",e);
//        }
//
//    }
//
//    /**
//     * 获取签名验证器
//     * @return
//     */
//    @Bean
//    public ScheduledUpdateCertificatesVerifier getVerifier(){
//        Wxpay wxpay = wxpayMapper.getOne(1l);
//
//        log.info("获取签名验证器");
//
//        //获取商户私钥
//        PrivateKey privateKey = getPrivateKey(wxpay.getPrivateKeyPath());
//
//        //私钥签名对象
//        PrivateKeySigner privateKeySigner = new PrivateKeySigner(wxpay.getMchSerialNo(), privateKey);
//
//        //身份认证对象
//        WechatPay2Credentials wechatPay2Credentials = new WechatPay2Credentials(wxpay.getMchId(), privateKeySigner);
//
//        // 使用定时更新的签名验证器，不需要传入证书
//        ScheduledUpdateCertificatesVerifier verifier = new ScheduledUpdateCertificatesVerifier(
//                wechatPay2Credentials,
//                wxpay.getApiV3Key().getBytes(StandardCharsets.UTF_8));
//
//        return verifier;
//    }
//
//
//    /**
//     * 获取http请求对象
//     * @param verifier
//     * @return
//     */
//    @Bean(name = "wxPayClient")
//    public CloseableHttpClient getWxPayClient(ScheduledUpdateCertificatesVerifier verifier){
//        Wxpay wxpay = wxpayMapper.getOne(1l);
//        log.info("获取httpClient");
//
//        //获取商户私钥
//        PrivateKey privateKey = getPrivateKey(wxpay.getPrivateKeyPath());
//
//        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
//                .withMerchant(wxpay.getMchId(), wxpay.getMchSerialNo(), privateKey)
//                .withValidator(new WechatPay2Validator(verifier));
//        // ... 接下来，你仍然可以通过builder设置各种参数，来配置你的HttpClient
//
//        // 通过WechatPayHttpClientBuilder构造的HttpClient，会自动的处理签名和验签，并进行证书自动更新
//        CloseableHttpClient httpClient = builder.build();
//
//        return httpClient;
//    }
//
//    /**
//     * 获取HttpClient，无需进行应答签名验证，跳过验签的流程
//     */
//    @Bean(name = "wxPayNoSignClient")
//    public CloseableHttpClient getWxPayNoSignClient(){
//        Wxpay wxpay = wxpayMapper.getOne(1l);
//
//        //获取商户私钥
//        PrivateKey privateKey = getPrivateKey(wxpay.getPrivateKeyPath());
//
//        //用于构造HttpClient
//        WechatPayHttpClientBuilder builder = WechatPayHttpClientBuilder.create()
//                //设置商户信息
//                .withMerchant(wxpay.getMchId(), wxpay.getMchSerialNo(), privateKey)
//                //无需进行签名验证、通过withValidator((response) -> true)实现
//                .withValidator((response) -> true);
//
//        // 通过WechatPayHttpClientBuilder构造的HttpClient，会自动的处理签名和验签，并进行证书自动更新
//        CloseableHttpClient httpClient = builder.build();
//
//        log.info("== getWxPayNoSignClient END ==");
//
//        return httpClient;
//    }

}