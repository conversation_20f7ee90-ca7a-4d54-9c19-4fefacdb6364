<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.AdminScoreHistoryMapper">
    
    <resultMap type="AdminScoreHistory" id="AdminScoreHistoryResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="userNickName"    column="user_nick_name"    />
        <result property="userPhone"    column="user_phone"    />
        <result property="score"    column="score"    />
        <result property="type"    column="type"    />
        <result property="remainScore"    column="remain_score"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAdminScoreHistoryVo">
        select id, user_id, user_nick_name, user_phone, score, type, remain_score, update_time from score_history
    </sql>

    <select id="selectAdminScoreHistoryList" parameterType="AdminScoreHistory" resultMap="AdminScoreHistoryResult">
        <include refid="selectAdminScoreHistoryVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="userNickName != null  and userNickName != ''"> and user_nick_name like concat('%', #{userNickName}, '%')</if>
            <if test="userPhone != null  and userPhone != ''"> and user_phone = #{userPhone}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and update_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
        order by update_time desc
    </select>
    
    <select id="selectAdminScoreHistoryById" parameterType="Long" resultMap="AdminScoreHistoryResult">
        <include refid="selectAdminScoreHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertAdminScoreHistory" parameterType="AdminScoreHistory">
        insert into score_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="userNickName != null">user_nick_name,</if>
            <if test="userPhone != null">user_phone,</if>
            <if test="score != null">score,</if>
            <if test="type != null">type,</if>
            <if test="remainScore != null">remain_score,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userNickName != null">#{userNickName},</if>
            <if test="userPhone != null">#{userPhone},</if>
            <if test="score != null">#{score},</if>
            <if test="type != null">#{type},</if>
            <if test="remainScore != null">#{remainScore},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAdminScoreHistory" parameterType="AdminScoreHistory">
        update score_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userNickName != null">user_nick_name = #{userNickName},</if>
            <if test="userPhone != null">user_phone = #{userPhone},</if>
            <if test="score != null">score = #{score},</if>
            <if test="type != null">type = #{type},</if>
            <if test="remainScore != null">remain_score = #{remainScore},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAdminScoreHistoryById" parameterType="Long">
        delete from score_history where id = #{id}
    </delete>

    <delete id="deleteAdminScoreHistoryByIds" parameterType="String">
        delete from score_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>