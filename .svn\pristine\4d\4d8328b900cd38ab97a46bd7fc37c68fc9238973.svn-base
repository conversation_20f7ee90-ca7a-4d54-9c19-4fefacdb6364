package com.dataxai.web.controller.front;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.WebShortcutGoods;
import com.dataxai.web.service.IWebShortcutGoodsService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 快捷模板-推荐信息Controller
 * 
 * <AUTHOR>
 * @date 2024-02-04
 */
@RestController
@RequestMapping("/short-cut/goods")
@Api(tags={"【快捷模板-推荐信息】Controller"})
@ApiIgnore
public class WebShortcutGoodsController extends BaseController
{
    @Autowired
    private IWebShortcutGoodsService webShortcutGoodsService;

    /**
     * 查询快捷模板-推荐信息列表
     */
    @PreAuthorize("@ss.hasPermi('short-cut:goods:list')")
    @GetMapping("/list")
    @ApiOperation("查询快捷模板-推荐信息列表")
    public TableDataInfo list(WebShortcutGoods webShortcutGoods)
    {
        startPage();
        List<WebShortcutGoods> list = webShortcutGoodsService.selectWebShortcutGoodsList(webShortcutGoods);
        return getDataTable(list);
    }

    /**
     * 导出快捷模板-推荐信息列表
     */
    @PreAuthorize("@ss.hasPermi('short-cut:goods:export')")
    @Log(title = "快捷模板-推荐信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出快捷模板-推荐信息列表Excel")
    public void export(HttpServletResponse response, WebShortcutGoods webShortcutGoods)
    {
        List<WebShortcutGoods> list = webShortcutGoodsService.selectWebShortcutGoodsList(webShortcutGoods);
        ExcelUtil<WebShortcutGoods> util = new ExcelUtil<WebShortcutGoods>(WebShortcutGoods.class);
        util.exportExcel(response, list, "快捷模板-推荐信息数据");
    }

    /**
     * 获取快捷模板-推荐信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('short-cut:goods:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取快捷模板-推荐信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(webShortcutGoodsService.selectWebShortcutGoodsById(id));
    }

    /**
     * 新增快捷模板-推荐信息
     */
    @PreAuthorize("@ss.hasPermi('short-cut:goods:add')")
    @Log(title = "快捷模板-推荐信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增快捷模板-推荐信息")
    public AjaxResult add(@RequestBody WebShortcutGoods webShortcutGoods)
    {
        return toAjax(webShortcutGoodsService.insertWebShortcutGoods(webShortcutGoods));
    }

    /**
     * 修改快捷模板-推荐信息
     */
    @PreAuthorize("@ss.hasPermi('short-cut:goods:edit')")
    @Log(title = "快捷模板-推荐信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改快捷模板-推荐信息")
    public AjaxResult edit(@RequestBody WebShortcutGoods webShortcutGoods)
    {
        return toAjax(webShortcutGoodsService.updateWebShortcutGoods(webShortcutGoods));
    }

    /**
     * 删除快捷模板-推荐信息
     */
    @PreAuthorize("@ss.hasPermi('short-cut:goods:remove')")
    @Log(title = "快捷模板-推荐信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	@ApiOperation("删除快捷模板-推荐信息")
    public AjaxResult remove(@PathVariable Integer[] ids)
    {
        return toAjax(webShortcutGoodsService.deleteWebShortcutGoodsByIds(ids));
    }
}
