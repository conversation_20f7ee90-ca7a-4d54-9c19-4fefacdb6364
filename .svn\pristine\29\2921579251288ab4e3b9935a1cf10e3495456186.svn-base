package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.AdminRefuelingBag;

/**
 * 加油包Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface IAdminRefuelingBagService 
{
    /**
     * 查询加油包
     * 
     * @param refuelingBagId 加油包主键
     * @return 加油包
     */
    public AdminRefuelingBag selectAdminRefuelingBagByRefuelingBagId(String refuelingBagId);

    /**
     * 查询加油包列表
     * 
     * @param adminRefuelingBag 加油包
     * @return 加油包集合
     */
    public List<AdminRefuelingBag> selectAdminRefuelingBagList(AdminRefuelingBag adminRefuelingBag);

    /**
     * 新增加油包
     * 
     * @param adminRefuelingBag 加油包
     * @return 结果
     */
    public int insertAdminRefuelingBag(AdminRefuelingBag adminRefuelingBag);

    /**
     * 修改加油包
     * 
     * @param adminRefuelingBag 加油包
     * @return 结果
     */
    public int updateAdminRefuelingBag(AdminRefuelingBag adminRefuelingBag);

    /**
     * 批量删除加油包
     * 
     * @param refuelingBagIds 需要删除的加油包主键集合
     * @return 结果
     */
    public int deleteAdminRefuelingBagByRefuelingBagIds(String[] refuelingBagIds);

    /**
     * 删除加油包信息
     * 
     * @param refuelingBagId 加油包主键
     * @return 结果
     */
    public int deleteAdminRefuelingBagByRefuelingBagId(String refuelingBagId);
}
