<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.WebCharacterModelMapper">
    
    <resultMap type="WebCharacterModel" id="WebCharacterModelResult">
        <result property="id"    column="id"    />
        <result property="prompt"    column="prompt"    />
        <result property="gender"    column="gender"    />
        <result property="age"    column="age"    />
        <result property="skin"    column="skin"    />
        <result property="expression"    column="expression"    />
        <result property="bigSize"    column="big_size"    />
        <result property="hairStyles"    column="hair_styles"    />
        <result property="image"    column="image"    />
        <result property="sort"    column="sort"    />
        <result property="isVaild"    column="is_vaild"    />
    </resultMap>

    <sql id="selectWebCharacterModelVo">
        select id, prompt, gender, age, skin, expression, big_size, hair_styles, image,sort, is_vaild from web_character_model
    </sql>

    <select id="selectWebCharacterModelList" parameterType="WebCharacterModel" resultMap="WebCharacterModelResult">
        <include refid="selectWebCharacterModelVo"/>
        <where>  
            <if test="prompt != null  and prompt != ''"> and prompt = #{prompt}</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="age != null  and age != ''"> and age = #{age}</if>
            <if test="skin != null  and skin != ''"> and skin = #{skin}</if>
            <if test="expression != null  and expression != ''"> and expression = #{expression}</if>
            <if test="bigSize != null  and bigSize != ''"> and big_size = #{bigSize}</if>
            <if test="hairStyles != null  and hairStyles != ''"> and hair_styles = #{hairStyles}</if>
            <if test="image != null  and image != ''"> and image = #{image}</if>
            <if test="sort != null  "> and sort = #{sort}</if>
            <if test="isVaild != null  "> and is_vaild = #{isVaild}</if>
        </where>
        ORDER BY sort,id ASC
    </select>
    
    <select id="selectWebCharacterModelById" parameterType="Long" resultMap="WebCharacterModelResult">
        <include refid="selectWebCharacterModelVo"/>
        where id = #{id} and is_vaild = 1 ORDER BY sort,id ASC
    </select>
        
    <insert id="insertWebCharacterModel" parameterType="WebCharacterModel" useGeneratedKeys="true" keyProperty="id">
        insert into web_character_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prompt != null and prompt != ''">prompt,</if>
            <if test="gender != null and gender != ''">gender,</if>
            <if test="age != null and age != ''">age,</if>
            <if test="skin != null and skin != ''">skin,</if>
            <if test="expression != null">expression,</if>
            <if test="bigSize != null">big_size,</if>
            <if test="hairStyles != null">hair_styles,</if>
            <if test="image != null">image,</if>
            <if test="sort != null">sort,</if>
            <if test="isVaild != null">is_vaild,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prompt != null and prompt != ''">#{prompt},</if>
            <if test="gender != null and gender != ''">#{gender},</if>
            <if test="age != null and age != ''">#{age},</if>
            <if test="skin != null and skin != ''">#{skin},</if>
            <if test="expression != null">#{expression},</if>
            <if test="bigSize != null">#{bigSize},</if>
            <if test="hairStyles != null">#{hairStyles},</if>
            <if test="image != null">#{image},</if>
            <if test="sort != null">#{sort},</if>
            <if test="isVaild != null">#{isVaild},</if>
         </trim>
    </insert>

    <update id="updateWebCharacterModel" parameterType="WebCharacterModel">
        update web_character_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="prompt != null and prompt != ''">prompt = #{prompt},</if>
            <if test="gender != null and gender != ''">gender = #{gender},</if>
            <if test="age != null and age != ''">age = #{age},</if>
            <if test="skin != null and skin != ''">skin = #{skin},</if>
            <if test="expression != null">expression = #{expression},</if>
            <if test="bigSize != null">big_size = #{bigSize},</if>
            <if test="hairStyles != null">hair_styles = #{hairStyles},</if>
            <if test="image != null">image = #{image},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="isVaild != null">is_vaild = #{isVaild},</if>
        </trim>
        where id = #{id} and is_vaild = 1
    </update>

    <delete id="deleteWebCharacterModelById" parameterType="Long">
        delete from web_character_model where id = #{id}
    </delete>

    <delete id="deleteWebCharacterModelByIds" parameterType="String">
        delete from web_character_model where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectGenderList" resultType="String">
        SELECT gender FROM web_character_model WHERE is_vaild = 1 GROUP BY gender ORDER BY min(sort);
    </select>

    <select id="selectAgeList" resultType="String">
        SELECT age FROM web_character_model WHERE is_vaild = 1 GROUP BY age ORDER BY min(sort);
    </select>

    <select id="selectSkinList" resultType="String">
        SELECT skin FROM web_character_model WHERE is_vaild = 1 GROUP BY skin ORDER BY min(sort);
    </select>

    <select id="selectExpressionList" resultType="String">
        SELECT expression FROM web_character_model WHERE is_vaild = 1 GROUP BY expression ORDER BY min(sort);
    </select>

    <select id="selectBigSizeList" resultType="String">
        SELECT big_size FROM web_character_model WHERE is_vaild = 1 GROUP BY big_size ORDER BY min(sort);
    </select>

    <select id="selectHairStylesList" resultType="String">
        SELECT hair_styles FROM web_character_model WHERE is_vaild = 1 GROUP BY hair_styles ORDER BY min(sort);
    </select>

    <select id="selectTotalCountBelow" resultType="integer">
        SELECT COUNT(*)
        FROM web_character_model
        WHERE sort &lt;= #{sort}  and is_vaild = 1
        AND IF((#{gender} IS NOT NULL and #{gender} != ''),  gender = #{gender},1=1)
        AND IF((#{age} IS NOT NULL and #{age} != ''),  age = #{age},1=1)
        AND IF((#{skin} IS NOT NULL and #{skin} != ''),  skin = #{skin},1=1)
        ORDER BY sort,id ASC
    </select>


</mapper>