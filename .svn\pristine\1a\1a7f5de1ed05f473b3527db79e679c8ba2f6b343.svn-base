package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * 
 * <AUTHOR>
 * @date 2024-01-08
 */
public class TaskSegData implements Serializable
{
    private static final long serialVersionUID = 1L;

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    /** 任务id */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    public byte[] getSeg() {
        return seg;
    }

    public void setSeg(byte[] seg) {
        this.seg = seg;
    }

    @JsonIgnore
    private byte[] seg;

    public String getSegData() {
        return segData;
    }

    public void setSegData(String segData) {
        this.segData = segData;
    }

    @ApiModelProperty(value = "图片切割信息")
    private String segData;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("segData", getSegData())
            .toString();
    }
}
