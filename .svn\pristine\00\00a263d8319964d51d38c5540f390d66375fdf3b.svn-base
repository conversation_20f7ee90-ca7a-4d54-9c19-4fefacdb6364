<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.Web4kProcessMapper">
    
    <resultMap type="Web4kProcess" id="Web4kProcessResult">
        <result property="ordinal"    column="ordinal"    />
        <result property="originImgUrl"    column="originImgUrl"    />
        <result property="status"    column="status"    />
        <result property="taskId"    column="taskId"    />
        <result property="taskOrdinalId"    column="taskOrdinalId"    />
        <result property="userId"    column="userId"    />
        <result property="isEnhance"    column="is_Enhance"    />
        <result property="ordinalImgResultList"    column="ordinalImgResultList"    />
    </resultMap>

    <sql id="selectWeb4kProcessVo">
        select ordinal, originImgUrl, status, taskId, taskOrdinalId, userId, is_Enhance, ordinalImgResultList from web_4k_process
    </sql>

    <select id="selectWeb4kProcessList" parameterType="Web4kProcess" resultMap="Web4kProcessResult">
        <include refid="selectWeb4kProcessVo"/>
        <where>  
            <if test="ordinal != null "> and ordinal = #{ordinal}</if>
            <if test="originImgUrl != null  and originImgUrl != ''"> and originImgUrl = #{originImgUrl}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="taskId != null  and taskId != ''"> and taskId = #{taskId}</if>
            <if test="taskOrdinalId != null  and taskOrdinalId != ''"> and taskOrdinalId = #{taskOrdinalId}</if>
            <if test="userId != null "> and userId = #{userId}</if>
            <if test="isEnhance != null "> and is_Enhance = #{isEnhance}</if>
            <if test="ordinalImgResultList != null  and ordinalImgResultList != ''"> and ordinalImgResultList = #{ordinalImgResultList}</if>
        </where>
    </select>
    
    <select id="selectWeb4kProcessByOrdinal" parameterType="Long" resultMap="Web4kProcessResult">
        <include refid="selectWeb4kProcessVo"/>
        where ordinal = #{ordinal}
    </select>
        
    <insert id="insertWeb4kProcess" parameterType="Web4kProcess">
        insert into web_4k_process
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ordinal != null">ordinal,</if>
            <if test="originImgUrl != null">originImgUrl,</if>
            <if test="status != null">status,</if>
            <if test="taskId != null">taskId,</if>
            <if test="taskOrdinalId != null">taskOrdinalId,</if>
            <if test="userId != null">userId,</if>
            <if test="isEnhance != null">is_Enhance,</if>
            <if test="ordinalImgResultList != null">ordinalImgResultList,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ordinal != null">#{ordinal},</if>
            <if test="originImgUrl != null">#{originImgUrl},</if>
            <if test="status != null">#{status},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="taskOrdinalId != null">#{taskOrdinalId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="isEnhance != null">#{isEnhance},</if>
            <if test="ordinalImgResultList != null">#{ordinalImgResultList},</if>
         </trim>
    </insert>

    <update id="updateWeb4kProcess" parameterType="Web4kProcess">
        update web_4k_process
        <trim prefix="SET" suffixOverrides=",">
            <if test="originImgUrl != null">originImgUrl = #{originImgUrl},</if>
            <if test="status != null">status = #{status},</if>
            <if test="taskId != null">taskId = #{taskId},</if>
            <if test="taskOrdinalId != null">taskOrdinalId = #{taskOrdinalId},</if>
            <if test="userId != null">userId = #{userId},</if>
            <if test="isEnhance != null">is_Enhance = #{isEnhance},</if>
            <if test="ordinalImgResultList != null">ordinalImgResultList = #{ordinalImgResultList},</if>
        </trim>
        where ordinal = #{ordinal}
    </update>

    <delete id="deleteWeb4kProcessByOrdinal" parameterType="Long">
        delete from web_4k_process where ordinal = #{ordinal}
    </delete>

    <delete id="deleteWeb4kProcessByOrdinals" parameterType="String">
        delete from web_4k_process where ordinal in 
        <foreach item="ordinal" collection="array" open="(" separator="," close=")">
            #{ordinal}
        </foreach>
    </delete>
</mapper>