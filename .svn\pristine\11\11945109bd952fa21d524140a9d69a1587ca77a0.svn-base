package com.dataxai.web.controller.front;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.WebScenGoodsCategorySub;
import com.dataxai.web.service.IWebScenGoodsCategorySubService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;
import springfox.documentation.annotations.ApiIgnore;

/**
 * 商品场景信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
//@RestController
//@RequestMapping("/scen/goods/sub")
//@Api(tags={"【商品场景信息】Controller"})
@ApiIgnore
public class WebScenGoodsCategorySubController extends BaseController
{
    @Autowired
    private IWebScenGoodsCategorySubService webScenGoodsCategorySubService;

    /**
     * 查询商品场景信息列表
     */
    @PreAuthorize("@ss.hasPermi('scen:goods/sub:list')")
    @GetMapping("/list")
    @ApiOperation("查询商品场景信息列表")
    public TableDataInfo list(WebScenGoodsCategorySub webScenGoodsCategorySub)
    {
        startPage();
        List<WebScenGoodsCategorySub> list = webScenGoodsCategorySubService.selectWebScenGoodsCategorySubList(webScenGoodsCategorySub);
        return getDataTable(list);
    }

    /**
     * 导出商品场景信息列表
     */
    @PreAuthorize("@ss.hasPermi('scen:goods/sub:export')")
    @Log(title = "商品场景信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出商品场景信息列表Excel")
    public void export(HttpServletResponse response, WebScenGoodsCategorySub webScenGoodsCategorySub)
    {
        List<WebScenGoodsCategorySub> list = webScenGoodsCategorySubService.selectWebScenGoodsCategorySubList(webScenGoodsCategorySub);
        ExcelUtil<WebScenGoodsCategorySub> util = new ExcelUtil<WebScenGoodsCategorySub>(WebScenGoodsCategorySub.class);
        util.exportExcel(response, list, "商品场景信息数据");
    }

    /**
     * 获取商品场景信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('scen:goods/sub:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取商品场景信息详细信息")
    public AjaxResult getInfo(@PathVariable("id") Integer id)
    {
        return success(webScenGoodsCategorySubService.selectWebScenGoodsCategorySubById(id));
    }

    /**
     * 新增商品场景信息
     */
    @PreAuthorize("@ss.hasPermi('scen:goods/sub:add')")
    @Log(title = "商品场景信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增商品场景信息")
    public AjaxResult add(@RequestBody WebScenGoodsCategorySub webScenGoodsCategorySub)
    {
        return toAjax(webScenGoodsCategorySubService.insertWebScenGoodsCategorySub(webScenGoodsCategorySub));
    }

    /**
     * 修改商品场景信息
     */
    @PreAuthorize("@ss.hasPermi('scen:goods/sub:edit')")
    @Log(title = "商品场景信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改商品场景信息")
    public AjaxResult edit(@RequestBody WebScenGoodsCategorySub webScenGoodsCategorySub)
    {
        return toAjax(webScenGoodsCategorySubService.updateWebScenGoodsCategorySub(webScenGoodsCategorySub));
    }

    /**
     * 删除商品场景信息
     */
    @PreAuthorize("@ss.hasPermi('scen:goods/sub:remove')")
    @Log(title = "商品场景信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
	@ApiOperation("删除商品场景信息")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(webScenGoodsCategorySubService.deleteWebScenGoodsCategorySubByIds(ids));
    }
}
