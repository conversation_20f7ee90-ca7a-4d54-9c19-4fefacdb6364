package com.dataxai.web.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.dataxai.web.domain.WebScenGoodsCategory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.WebScenGoodsMapper;
import com.dataxai.web.domain.WebScenGoods;
import com.dataxai.web.service.IWebScenGoodsService;

/**
 * 商品场景信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Service
public class WebScenGoodsServiceImpl implements IWebScenGoodsService 
{
    @Autowired
    private WebScenGoodsMapper webScenGoodsMapper;

    /**
     * 查询商品场景信息
     * 
     * @param id 商品场景信息主键
     * @return 商品场景信息
     */
    @Override
    public WebScenGoods selectWebScenGoodsById(Long id)
    {
        return webScenGoodsMapper.selectWebScenGoodsById(id);
    }

    /**
     * 查询商品场景信息列表
     * 
     * @param webScenGoods 商品场景信息
     * @return 商品场景信息
     */
    @Override
    public List<WebScenGoods> selectWebScenGoodsList(WebScenGoods webScenGoods)
    {
        return webScenGoodsMapper.selectWebScenGoodsList(webScenGoods);
    }

    /**
     * 新增商品场景信息
     * 
     * @param webScenGoods 商品场景信息
     * @return 结果
     */
    @Override
    public int insertWebScenGoods(WebScenGoods webScenGoods)
    {
        return webScenGoodsMapper.insertWebScenGoods(webScenGoods);
    }

    /**
     * 修改商品场景信息
     * 
     * @param webScenGoods 商品场景信息
     * @return 结果
     */
    @Override
    public int updateWebScenGoods(WebScenGoods webScenGoods)
    {
        return webScenGoodsMapper.updateWebScenGoods(webScenGoods);
    }

    /**
     * 批量删除商品场景信息
     * 
     * @param ids 需要删除的商品场景信息主键
     * @return 结果
     */
    @Override
    public int deleteWebScenGoodsByIds(Long[] ids)
    {
        return webScenGoodsMapper.deleteWebScenGoodsByIds(ids);
    }

    /**
     * 删除商品场景信息信息
     * 
     * @param id 商品场景信息主键
     * @return 结果
     */
    @Override
    public int deleteWebScenGoodsById(Long id)
    {
        return webScenGoodsMapper.deleteWebScenGoodsById(id);
    }

    @Override
    public List<WebScenGoodsCategory> selectWebScenGoodsCategoryList() {
        List<WebScenGoodsCategory> result = new ArrayList<>();
        List<String> stringList =  webScenGoodsMapper.setWebScenGoodsCategoryList();
        stringList.stream().peek(x->{
            WebScenGoodsCategory category = new WebScenGoodsCategory();
            category.setCategory(x);
            WebScenGoods webScenGoods = new WebScenGoods();
            webScenGoods.setCategory(x);
            List<WebScenGoods> selectWebScenGoodsList = webScenGoodsMapper.selectWebScenGoodsList(webScenGoods);
            if(null != selectWebScenGoodsList && selectWebScenGoodsList.size()>0){
                category.setImage(selectWebScenGoodsList.get(0).getImage());
            }
            result.add(category);
        }).collect(Collectors.toList());
        return result;
    }
}
