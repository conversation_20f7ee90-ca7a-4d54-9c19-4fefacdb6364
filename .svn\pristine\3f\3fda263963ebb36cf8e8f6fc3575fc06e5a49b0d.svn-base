package com.dataxai.web.controller.wechat;

import com.dataxai.common.constant.CacheConstants;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.model.LoginUser;
import com.dataxai.common.core.domain.model.User;
import com.dataxai.common.core.redis.RedisCache;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.framework.web.service.TokenService;
import com.dataxai.web.Constants.ConstantEnvPropertiesUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.core.config.ConstantWxLoginPropertiesUtil;
import com.dataxai.web.domain.*;
import com.dataxai.web.entity.MessageCode;
import com.dataxai.web.entity.MsgHandle;
import com.dataxai.web.entity.ResultRes;
import com.dataxai.web.mapper.FrontUserMapper;
import com.dataxai.web.mapper.TariffPackageMapper;
import com.dataxai.web.mapper.UserPackageMapper;
import com.dataxai.web.utils.SnowFlakeUtils;
import com.dataxai.web.utils.wechat.ParseXml;
import com.dataxai.web.utils.wechat.WeixinCheckoutUtil;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author:xg
 * @Date:2023-12-29 14:10
 * @Desciption:com.dataxai.web.controller.wechat
 */
@RestController
@RequestMapping("/wechat")
@ApiIgnore
public class WeChatOfficialAccount extends BaseController {

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private FrontUserMapper frontUserMapper;

    @Autowired
    private TariffPackageMapper tariffPackageMapper;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private UserPackageMapper userPackageMapper;

    @ApiOperation(value = "验证微信服务器",hidden = true)
    @GetMapping("/access")
    public void wechatInterface(HttpServletRequest request, HttpServletResponse response) throws UnsupportedEncodingException {
        System.out.println("............................. 验证微信服务号信息开始............ ");
        //微信服务器POST消息时用的是UTF-8编码，在接收时也要用同样的编码，否则中文会乱码；
        request.setCharacterEncoding("UTF-8");
        //在响应消息(回复消息给用户)时，也将编码方式设置为UTF-8,原理同上;
        response.setCharacterEncoding("UTF-8");
        // String respXml = weixinCoreService.weixinMessageHandelCoreService(request,response);
        // 微信加密签名
        String signature = request.getParameter("signature");
        //时间戳
        String timestamp = request.getParameter("timestamp");
        //随机数
        String nonce = request.getParameter("nonce");
        //随机字符串
        String echostr = request.getParameter("echostr");
        //
        System.out.println("Signature is :" + signature + "--timestamp is： " + timestamp + "    --nonce is :" + nonce);
        if (WeixinCheckoutUtil.checkSignature(signature, timestamp, nonce)) {
            System.out.println("验证微信服务号结束");
            try {
                response.getWriter().write(echostr);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
//            return echostr;
        } else {
            //此处可以实现其他逻辑
            System.out.println("不是微信服务器发过来的请求，请小心！");
//            return null;
        }
    }

    @ApiOperation(value = "用户关注/或者取消关注",hidden = true)
    @PostMapping("/access")
    @Transactional
    public synchronized void follow(HttpServletRequest request, HttpServletResponse response) throws Exception {
//        request.setCharacterEncoding("UTF-8");
//        response.setCharacterEncoding("UTF-8");
//        PrintWriter out = response.getWriter();
//        Map<String, String> parseXml = parseXml(request);
//        String msgType = parseXml.get("MsgType");
//        String content = parseXml.get("Content");
//        String fromusername = parseXml.get("FromUserName");
//        String tousername = parseXml.get("ToUserName");
//        System.out.println(msgType);
//        System.out.println(content);
//        System.out.println(fromusername);
//        System.out.println(tousername);
        System.out.println("接收微信服务器发送的回调事件");
        try { //try-catch后面会用到
            Map<String, String> paramMap = ParseXml.parseXml(request);
            String type = paramMap.get("MsgType");
            String event = null;
            //获取自定义点击/推送事件
            if(MessageCode.REQ_MESSAGE_TYPE_EVENT.equals(type)){
//                请求消息类型：推送
                if(paramMap.get("Event") != null){
                    event  = paramMap.get("Event");
                    if(MessageCode.EVENT_TYPE_SUBSCRIBE.equals(event)){
                        System.out.println("用户关注了公众号");
                        //用户订阅公众号后，获取openid
                        handleSubscribeEvent(paramMap);
                    }else if(MessageCode.EVENT_TYPE_SCAN.equals(event)){
                        System.out.println("用户已经订阅公众号");
                        handleScanEvent(paramMap);
                    }else if(MessageCode.EVENT_TYPE_UNSUBSCRIBE.equals(event)){
                        System.out.println("用户取消订阅公众号");
                    }
                }
            }
            //处理消息事件
            if(MessageCode.REQ_MESSAGE_TYPE_TEXT.equals(type)){
                System.out.println("进入消息事件！");
                MsgHandle msgHandle = new MsgHandle();
                ResultRes.response(msgHandle.processMessage(paramMap),response);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void handleScanEvent(Map<String, String> paramMap) {
        String openId = paramMap.get("FromUserName");
        String eventKeyValue = paramMap.get("EventKey");
        logger.info(DateUtils.getNowDate()+"    handleScanEvent openId =   "+openId+"  eventKeyValue = "+eventKeyValue);
        if(StringUtils.isNotEmpty(eventKeyValue)){
            //已经关注过了微信公众号，则直接获取到传递过来的参数值，不用按照"_"进行切割
            String sceneStr = eventKeyValue;
            if(sceneStr.length() == 32){
                //表示传递过来的是一个uuid,那么就表示是通过直接扫码微信公众号进行登录
                updateOpenIdToUser(openId, sceneStr);
            }else {
                //通过短信验证登录后，已经关注过微信公众号了
                bindOpenId(sceneStr, openId);
            }
        }else {
            System.out.println("传递过来的参数为空");
        }
    }

    private void handleSubscribeEvent(Map<String, String> paramMap) {
        String openId = paramMap.get("FromUserName");
        String eventKeyValue = paramMap.get("EventKey");
        logger.info(DateUtils.getNowDate()+"    handleSubscribeEvent openId=    "+openId+"        eventKeyValue=  "+eventKeyValue);
        if(StringUtils.isNotEmpty(eventKeyValue)){
            String[] split = eventKeyValue.split("_");
            String sceneStr = split[1];
            if(StringUtils.isNotEmpty(sceneStr)){
                if(sceneStr.length() == 32){
                    //表示传递过来的是一个uuid,那么就表示是通过直接扫码微信公众号，进行微信公众号的关注
                    updateOpenIdToUser(openId, sceneStr);
                }else {
                    //通过短信验证码登录后，在关注微信公众号,完成登录操作
                    bindOpenId(sceneStr, openId);
                }
            }else {
                System.out.println("传递过来的参数是空值");
            }
        }else {
            System.out.println("传递过来的参数是空值");
        }
    }

    private void bindOpenId(String sceneStr, String openId) {
        long userId = Long.parseLong(sceneStr);
        logger.info("关注公众号的用户id   "+userId+"    用户关注微信公众号后，将openid"+ openId+ "   写入user表中");
        updateOpenIdToFrontUser(openId, userId);
    }

    private void updateOpenIdToFrontUser(String openId, long userId) {
        User userByUserId = frontUserMapper.selectUserByUserId(userId);
        if(null != userByUserId && StringUtils.isEmpty(userByUserId.getWxOpenId())){
            userByUserId.setWxOpenId(openId);
            userByUserId.setUpdateTime(DateUtils.getNowDate());
            frontUserMapper.updateUser(userByUserId);
        }
    }

    private void updateOpenIdToUser(String openId, String sceneStr) {
        logger.info("openId =   "+openId);
        Long userId = SecurityUtils.getUserId();
        updateOpenIdToFrontUser(openId, userId);
    }
}
