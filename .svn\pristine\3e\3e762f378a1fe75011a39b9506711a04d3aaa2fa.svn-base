package com.dataxai.web.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

import java.io.Serializable;

/**
 * 商品的咒语词典对象 web_goods_dic
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
public class WebGoodsDic implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 特征名称 */
    @Excel(name = "特征名称")
    private String characteristic;

    /** 特征值中文 */
    @Excel(name = "特征值中文")
    private String vaCh;

    /** 特征值英文 */
    @Excel(name = "特征值英文")
    private String vaEn;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCharacteristic(String characteristic) 
    {
        this.characteristic = characteristic;
    }

    public String getCharacteristic() 
    {
        return characteristic;
    }
    public void setVaCh(String vaCh) 
    {
        this.vaCh = vaCh;
    }

    public String getVaCh() 
    {
        return vaCh;
    }
    public void setVaEn(String vaEn) 
    {
        this.vaEn = vaEn;
    }

    public String getVaEn() 
    {
        return vaEn;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("characteristic", getCharacteristic())
            .append("vaCh", getVaCh())
            .append("vaEn", getVaEn())
            .toString();
    }
}
