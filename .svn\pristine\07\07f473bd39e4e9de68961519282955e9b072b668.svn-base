package com.dataxai.web.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @version 1.0
 * @Author:xg
 * @Date:2024-01-08 10:32
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class VerificationCodeDTO {
    @ApiModelProperty("前台用户手机号")
    private String frontUserPhone;

    @ApiModelProperty("验证码或密码")
    private String verificationCode;

    @ApiModelProperty("微信openId")
    private String openId;

    @ApiModelProperty("登录类型 1 验证码 2 密码 ")
    private Integer type;

    @ApiModelProperty(value = "后台用户手机号",hidden = true)
    private String sysUserPhone;
}
