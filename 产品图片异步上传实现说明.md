# 产品图片异步上传实现说明

## 概述
本实现修改了产品信息新增接口，将产品图片保存到`original_image_url`字段，状态默认为1，然后通过Redis队列异步下载图片并上传到阿里云OSS，完成后更新产品状态。

## 实现架构

### 1. 事件驱动架构
- **ProductImageUploadEvent**: 产品图片上传事件
- **ProductImageUploadEventListener**: 事件监听器，异步处理事件
- **ProductInfoServiceImpl**: 发布事件的服务

### 2. Redis队列处理
- **ProductImageUploadTask**: 图片上传任务实体
- **ProductImageUploadProcessor**: 队列消费者，持续监听队列
- **ProductImageUploadServiceImpl**: 任务处理服务

### 3. 配置类
- **TasksRedisConfig**: Redis序列化配置
- **AsyncConfig**: 异步线程池配置

## 核心文件说明

### 新增文件

1. **ruoyi-system/src/main/java/com/dataxai/event/ProductImageUploadEvent.java**
   - 产品图片上传事件类
   - 包含产品ID、图片URL、用户ID等信息

2. **ruoyi-system/src/main/java/com/dataxai/domain/ProductImageUploadTask.java**
   - 图片上传任务实体类
   - 实现Serializable接口，支持Redis序列化

3. **ruoyi-admin/src/main/java/com/dataxai/web/listener/ProductImageUploadEventListener.java**
   - 事件监听器，使用@Async异步处理
   - 接收事件后创建Redis队列任务

4. **ruoyi-admin/src/main/java/com/dataxai/service/IProductImageUploadService.java**
   - 图片上传服务接口

5. **ruoyi-admin/src/main/java/com/dataxai/web/service/impl/ProductImageUploadServiceImpl.java**
   - 图片上传服务实现类
   - 负责创建队列任务、处理任务、下载上传图片

6. **ruoyi-admin/src/main/java/com/dataxai/web/task/ProductImageUploadProcessor.java**
   - 队列处理器，持续监听Redis队列
   - 使用独立线程池处理任务

7. **ruoyi-admin/src/main/java/com/dataxai/web/controller/front/ProductImageUploadController.java**
   - 管理接口，提供队列状态查询

### 修改文件

1. **ruoyi-system/src/main/java/com/dataxai/service/impl/ProductInfoServiceImpl.java**
   - 修改insertProductInfo方法
   - 保存图片到original_image_url字段
   - 设置默认状态为"1"
   - 发布图片上传事件

2. **ruoyi-admin/src/main/java/com/dataxai/web/core/config/TasksRedisConfig.java**
   - 添加ProductImageUploadTask的Redis序列化配置

## 处理流程

1. **产品创建**
   - 用户调用产品新增接口
   - ProductInfoServiceImpl.insertProductInfo()执行
   - 图片URL保存到original_image_url字段
   - 状态设置为"1"（待同步图片）

2. **事件发布**
   - 插入成功后发布ProductImageUploadEvent事件
   - 包含产品ID、图片URL、用户ID

3. **异步处理**
   - ProductImageUploadEventListener监听事件
   - 异步调用ProductImageUploadService创建队列任务

4. **队列处理**
   - ProductImageUploadProcessor持续监听Redis队列
   - 取出任务后调用processImageUploadTask处理

5. **图片处理**
   - 下载原始图片URL的图片
   - 转换为MultipartFile格式
   - 上传到阿里云OSS
   - 更新产品的productImageUrl字段
   - 更新状态为"2"（已同步图片）

6. **错误处理**
   - 处理失败时更新状态为"3"（同步图片失败）
   - 记录错误信息

## Redis队列

- **队列名称**: `product:image:upload:queue`
- **数据结构**: List
- **序列化**: GenericJackson2JsonRedisSerializer

## 状态说明

- **1**: 待同步图片（默认状态）
- **2**: 已同步图片（处理成功）
- **3**: 同步图片失败（处理失败）

## 监控接口

- **GET /product/image/queue/status**: 获取队列状态
- 返回当前队列长度信息

## 配置要点

1. **异步配置**: 使用@Async("taskExecutor")指定线程池
2. **Redis配置**: 使用专用的RedisTemplate配置
3. **事件驱动**: 解耦模块间依赖
4. **错误处理**: 完善的异常处理和状态更新

## 扩展性

- 支持批量处理
- 支持重试机制
- 支持监控和统计
- 支持不同图片处理策略
