package com.dataxai.web.controller.admincontroller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.AdminTariffPackage;
import com.dataxai.web.service.IAdminTariffPackageService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 套餐Controller
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@RestController
@RequestMapping("/package/adminpackage")
public class AdminTariffPackageController extends BaseController
{
    @Autowired
    private IAdminTariffPackageService adminTariffPackageService;

    /**
     * 查询套餐列表
     */
    @PreAuthorize("@ss.hasPermi('package:adminpackage:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdminTariffPackage adminTariffPackage)
    {
        startPage();
        Map<String, Object> params = adminTariffPackage.getParams();
        if(null != params){
            if(params.containsKey("beginCreateTime")){
                String beginCreateTime = (String) params.get("beginCreateTime");
                params.put("beginCreateTime",beginCreateTime+" 00:00:00");
            }
            if(params.containsKey("endCreateTime")){
                String endCreateTime = (String) params.get("endCreateTime");
                params.put("endCreateTime",endCreateTime+" 23:59:59");
            }
        }
        List<AdminTariffPackage> list = adminTariffPackageService.selectAdminTariffPackageList(adminTariffPackage);
        return getDataTable(list);
    }

    /**
     * 导出套餐列表
     */
    @PreAuthorize("@ss.hasPermi('package:adminpackage:export')")
    @Log(title = "套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdminTariffPackage adminTariffPackage)
    {
        List<AdminTariffPackage> list = adminTariffPackageService.selectAdminTariffPackageList(adminTariffPackage);
        ExcelUtil<AdminTariffPackage> util = new ExcelUtil<AdminTariffPackage>(AdminTariffPackage.class);
        util.exportExcel(response, list, "套餐数据");
    }

    /**
     * 获取套餐详细信息
     */
    @PreAuthorize("@ss.hasPermi('package:adminpackage:query')")
    @GetMapping(value = "/{tariffPackageId}")
    public AjaxResult getInfo(@PathVariable("tariffPackageId") String tariffPackageId)
    {
        return success(adminTariffPackageService.selectAdminTariffPackageByTariffPackageId(tariffPackageId));
    }

    /**
     * 新增套餐
     */
    @PreAuthorize("@ss.hasPermi('package:adminpackage:add')")
    @Log(title = "套餐", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdminTariffPackage adminTariffPackage)
    {
        int result = adminTariffPackageService.insertAdminTariffPackage(adminTariffPackage);
        if(result == -1){
            return AjaxResult.error("套餐添加失败，只能有一个上架的默认套餐");
        }
        if(result == -2){
            return AjaxResult.error("套餐添加失败，上架的套餐只能有4个");
        }
        return toAjax(result);
    }

    /**
     * 修改套餐
     */
    @PreAuthorize("@ss.hasPermi('package:adminpackage:edit')")
    @Log(title = "套餐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdminTariffPackage adminTariffPackage)
    {
        int result = adminTariffPackageService.updateAdminTariffPackage(adminTariffPackage);
        if(result == -1){
            return AjaxResult.error("套餐更新失败，只能有一个上架的默认套餐");
        }
        if(result == -2){
            return AjaxResult.error("套餐更新失败，上架的套餐只能有4个");
        }
        //如果将仅有的一个上架的默认套餐更新为下架后，又忘记了上架一个默认套餐，则需要给操作人员一个提示信息，提示操作人员，
        // 记得上架一个默认的套餐
        AdminTariffPackage adminTariffPackageParam = new AdminTariffPackage();
        adminTariffPackageParam.setIsDefault(1L);
        adminTariffPackageParam.setOnSale(1L);
        List<AdminTariffPackage> adminTariffPackages = adminTariffPackageService.selectAdminTariffPackageList(adminTariffPackageParam);
        if(null == adminTariffPackages){
            return AjaxResult.success("当前不存在上架的默认套餐,请务必上架一个默认套餐");
        }else {
            if(adminTariffPackages.size() == 0){
                return AjaxResult.success("当前不存在上架的默认套餐,请务必上架一个默认套餐");
            }
        }
        return toAjax(result);
    }

    /**
     * 删除套餐
     */
    @PreAuthorize("@ss.hasPermi('package:adminpackage:remove')")
    @Log(title = "套餐", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tariffPackageIds}")
    public AjaxResult remove(@PathVariable String[] tariffPackageIds)
    {
        return toAjax(adminTariffPackageService.deleteAdminTariffPackageByTariffPackageIds(tariffPackageIds));
    }
}
