package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.TBaseinfoBusiness;

/**
 * 定制业务介绍Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface TBaseinfoBusinessMapper 
{
    /**
     * 查询定制业务介绍
     * 
     * @param id 定制业务介绍主键
     * @return 定制业务介绍
     */
    public TBaseinfoBusiness selectTBaseinfoBusinessById(Long id);

    /**
     * 查询定制业务介绍列表
     * 
     * @param tBaseinfoBusiness 定制业务介绍
     * @return 定制业务介绍集合
     */
    public List<TBaseinfoBusiness> selectTBaseinfoBusinessList(TBaseinfoBusiness tBaseinfoBusiness);

    /**
     * 新增定制业务介绍
     * 
     * @param tBaseinfoBusiness 定制业务介绍
     * @return 结果
     */
    public int insertTBaseinfoBusiness(TBaseinfoBusiness tBaseinfoBusiness);

    /**
     * 修改定制业务介绍
     * 
     * @param tBaseinfoBusiness 定制业务介绍
     * @return 结果
     */
    public int updateTBaseinfoBusiness(TBaseinfoBusiness tBaseinfoBusiness);

    /**
     * 删除定制业务介绍
     * 
     * @param id 定制业务介绍主键
     * @return 结果
     */
    public int deleteTBaseinfoBusinessById(Long id);

    /**
     * 批量删除定制业务介绍
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTBaseinfoBusinessByIds(Long[] ids);
}
