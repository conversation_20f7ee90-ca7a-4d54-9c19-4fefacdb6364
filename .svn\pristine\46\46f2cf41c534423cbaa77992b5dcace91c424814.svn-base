package com.dataxai.web.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 前台用户管理对象 t_user
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public class AdminUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 用户id */
    private Long userId;

    /** 用户昵称 */
    @Excel(name = "用户昵称")
    private String nickName;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phone;

    /** 用户头像 */
    @Excel(name = "用户头像")
    private String avatar;

    /** openid */
    @Excel(name = "openid")
    private String wxOpenId;

    /** 套餐id */
    @Excel(name = "套餐id")
    private String packageId;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long delFlag;

    /** 是否禁用 */
    @Excel(name = "是否禁用")
    private Long enable;

    /** 性别 */
    @Excel(name = "性别")
    private Long gender;

    /** 登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "登录时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loginTime;

    /** 绑定失败原因 */
    @Excel(name = "绑定失败原因")
    private Long bindFail;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginCreateTime;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endCreateTime;

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setNickName(String nickName) 
    {
        this.nickName = nickName;
    }

    public String getNickName() 
    {
        return nickName;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }
    public void setWxOpenId(String wxOpenId) 
    {
        this.wxOpenId = wxOpenId;
    }

    public String getWxOpenId() 
    {
        return wxOpenId;
    }
    public void setPackageId(String packageId) 
    {
        this.packageId = packageId;
    }

    public String getPackageId() 
    {
        return packageId;
    }
    public void setDelFlag(Long delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Long getDelFlag() 
    {
        return delFlag;
    }
    public void setEnable(Long enable) 
    {
        this.enable = enable;
    }

    public Long getEnable() 
    {
        return enable;
    }
    public void setGender(Long gender) 
    {
        this.gender = gender;
    }

    public Long getGender() 
    {
        return gender;
    }
    public void setLoginTime(Date loginTime) 
    {
        this.loginTime = loginTime;
    }

    public Date getLoginTime() 
    {
        return loginTime;
    }
    public void setBindFail(Long bindFail) 
    {
        this.bindFail = bindFail;
    }

    public Long getBindFail() 
    {
        return bindFail;
    }

    public Date getBeginCreateTime() {
        return beginCreateTime;
    }

    public void setBeginCreateTime(Date beginCreateTime) {
        this.beginCreateTime = beginCreateTime;
    }

    public Date getEndCreateTime() {
        return endCreateTime;
    }

    public void setEndCreateTime(Date endCreateTime) {
        this.endCreateTime = endCreateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("userId", getUserId())
            .append("nickName", getNickName())
            .append("phone", getPhone())
            .append("avatar", getAvatar())
            .append("wxOpenId", getWxOpenId())
            .append("packageId", getPackageId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("enable", getEnable())
            .append("gender", getGender())
            .append("loginTime", getLoginTime())
            .append("bindFail", getBindFail())
            .toString();
    }
}
