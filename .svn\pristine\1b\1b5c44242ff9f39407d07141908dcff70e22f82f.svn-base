package com.dataxai.web.service.impl;

import java.util.List;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.domain.TTagDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.TTagMapper;
import com.dataxai.web.domain.TTag;
import com.dataxai.web.service.ITTagService;

/**
 * 标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@Service
public class TTagServiceImpl implements ITTagService
{
    @Autowired
    private TTagMapper tTagMapper;

    /**
     * 查询标签
     *
     * @param id 标签主键
     * @return 标签
     */
    @Override
    public TTag selectTTagById(Long id)
    {
        return tTagMapper.selectTTagById(id);
    }

    /**
     * 查询标签列表
     *
     * @param tTag 标签
     * @return 标签
     */
    @Override
    public List<TTag> selectTTagList(TTag tTag)
    {
        return tTagMapper.selectTTagList(tTag);
    }

    @Override
    public List<TTagDto> selectWebTTagList(TTag tTag)
    {
        return tTagMapper.selectWebTTagList(tTag);
    }


    /**
     * 新增标签
     *
     * @param tTag 标签
     * @return 结果
     */
    @Override
    public int insertTTag(TTag tTag)
    {
        tTag.setCreateTime(DateUtils.getNowDate());
        return tTagMapper.insertTTag(tTag);
    }

    /**
     * 修改标签
     *
     * @param tTag 标签
     * @return 结果
     */
    @Override
    public int updateTTag(TTag tTag)
    {
        tTag.setUpdateTime(DateUtils.getNowDate());
        return tTagMapper.updateTTag(tTag);
    }

    /**
     * 批量删除标签
     *
     * @param ids 需要删除的标签主键
     * @return 结果
     */
    @Override
    public int deleteTTagByIds(Long[] ids)
    {
        return tTagMapper.deleteTTagByIds(ids);
    }

    /**
     * 删除标签信息
     *
     * @param id 标签主键
     * @return 结果
     */
    @Override
    public int deleteTTagById(Long id)
    {
        return tTagMapper.deleteTTagById(id);
    }
}
