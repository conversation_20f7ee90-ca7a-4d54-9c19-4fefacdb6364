package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 任务生成图对象 t_ordinal_img_result
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public class AdminOrdinalImgResultStatistics extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Excel(name = "主键")
    private Long imageId;

    /** 生成大图片 */
    @Excel(name = "生成大图片")
    private String resImgUrl;

    /** 生成小图片 */
    @Excel(name = "生成小图片")
    private String resSmallImgUrl;

    /** 是否收藏 */
    @Excel(name = "是否收藏")
    private Long follow;

    /** 点赞点踩 */
    @Excel(name = "点赞点踩")
    private Long zan;

    /** 图片生成进度 */
    @Excel(name = "图片生成进度")
    private Long progress;

    /** 排队 */
    @Excel(name = "排队")
    private Long queue;

    /** 执行id */
    @Excel(name = "执行id")
    private String taskOrdinalId;

    /** 任务id */
    @Excel(name = "任务id")
    private String taskId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 是否删除 */
    @Excel(name = "是否删除")
    private Long delFlag;

    /** 原始图片url */
    @Excel(name = "原始图片url")
    private String originalImgUrl;

    /** 随机种子 */
    @Excel(name = "随机种子")
    private String seed;

    private String userPhone;

    private String taskName;

    private Long ordinal;

    public void setImageId(Long imageId) 
    {
        this.imageId = imageId;
    }

    public Long getImageId() 
    {
        return imageId;
    }
    public void setResImgUrl(String resImgUrl) 
    {
        this.resImgUrl = resImgUrl;
    }

    public String getResImgUrl() 
    {
        return resImgUrl;
    }
    public void setResSmallImgUrl(String resSmallImgUrl) 
    {
        this.resSmallImgUrl = resSmallImgUrl;
    }

    public String getResSmallImgUrl() 
    {
        return resSmallImgUrl;
    }
    public void setFollow(Long follow) 
    {
        this.follow = follow;
    }

    public Long getFollow() 
    {
        return follow;
    }
    public void setZan(Long zan) 
    {
        this.zan = zan;
    }

    public Long getZan() 
    {
        return zan;
    }
    public void setProgress(Long progress) 
    {
        this.progress = progress;
    }

    public Long getProgress() 
    {
        return progress;
    }
    public void setQueue(Long queue) 
    {
        this.queue = queue;
    }

    public Long getQueue() 
    {
        return queue;
    }
    public void setTaskOrdinalId(String taskOrdinalId)
    {
        this.taskOrdinalId = taskOrdinalId;
    }

    public String getTaskOrdinalId()
    {
        return taskOrdinalId;
    }
    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskId()
    {
        return taskId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setDelFlag(Long delFlag) 
    {
        this.delFlag = delFlag;
    }

    public Long getDelFlag() 
    {
        return delFlag;
    }
    public void setOriginalImgUrl(String originalImgUrl) 
    {
        this.originalImgUrl = originalImgUrl;
    }

    public String getOriginalImgUrl() 
    {
        return originalImgUrl;
    }
    public void setSeed(String seed) 
    {
        this.seed = seed;
    }

    public String getSeed() 
    {
        return seed;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Long getOrdinal() {
        return ordinal;
    }

    public void setOrdinal(Long ordinal) {
        this.ordinal = ordinal;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("imageId", getImageId())
            .append("resImgUrl", getResImgUrl())
            .append("resSmallImgUrl", getResSmallImgUrl())
            .append("follow", getFollow())
            .append("zan", getZan())
            .append("progress", getProgress())
            .append("queue", getQueue())
            .append("taskOrdinalId", getTaskOrdinalId())
            .append("taskId", getTaskId())
            .append("userId", getUserId())
            .append("delFlag", getDelFlag())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("originalImgUrl", getOriginalImgUrl())
            .append("seed", getSeed())
            .toString();
    }
}
