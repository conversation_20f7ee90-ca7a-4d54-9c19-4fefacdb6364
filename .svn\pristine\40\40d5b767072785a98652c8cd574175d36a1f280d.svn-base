package com.dataxai.web.utils;
import java.io.File;
import java.nio.file.Paths;
public class FileUtil {

    // 定义临时目录路径（建议使用项目内的目录，如 "src/main/resources/temp" 或用户自定义路径）
    public static final String TEMP_DIR_PATH = Paths.get(
        System.getProperty("user.dir"),  // 项目根目录
        "temp"
    ).toString();

    // 初始化目录（确保存在且可写）
    static {
        File tempDir = new File(TEMP_DIR_PATH);
        if (!tempDir.exists()) {
            boolean created = tempDir.mkdirs();
            if (!created) {
                throw new RuntimeException("无法创建临时目录: " + TEMP_DIR_PATH);
            }
        }
        if (!tempDir.canWrite()) {
            throw new RuntimeException("临时目录无写入权限: " + TEMP_DIR_PATH);
        }
    }
}

