package com.dataxai.service;

import com.dataxai.domain.ProductImageUploadTask;

/**
 * 产品图片上传服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface IProductImageUploadService 
{
    /**
     * 创建产品图片上传任务
     * 
     * @param productId 产品ID
     * @param originalImageUrl 原始图片URL
     * @param userId 用户ID
     */
    void createImageUploadTask(Long productId, String originalImageUrl, Long userId);

    /**
     * 处理产品图片上传任务
     * 
     * @param task 上传任务
     */
    void processImageUploadTask(ProductImageUploadTask task);
}
