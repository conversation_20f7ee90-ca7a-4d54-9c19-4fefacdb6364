package com.dataxai.web.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.WebSegProcessMapper;
import com.dataxai.web.domain.WebSegProcess;
import com.dataxai.web.service.IWebSegProcessService;

/**
 * 切割图的进程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-20
 */
@Service
public class WebSegProcessServiceImpl implements IWebSegProcessService 
{
    @Autowired
    private WebSegProcessMapper webSegProcessMapper;

    /**
     * 查询切割图的进程
     * 
     * @param taskId 切割图的进程主键
     * @return 切割图的进程
     */
    @Override
    public WebSegProcess selectWebSegProcessByTaskId(Long taskId)
    {
        return webSegProcessMapper.selectWebSegProcessByTaskId(taskId);
    }

    /**
     * 查询切割图的进程列表
     * 
     * @param webSegProcess 切割图的进程
     * @return 切割图的进程
     */
    @Override
    public List<WebSegProcess> selectWebSegProcessList(WebSegProcess webSegProcess)
    {
        return webSegProcessMapper.selectWebSegProcessList(webSegProcess);
    }

    /**
     * 新增切割图的进程
     * 
     * @param webSegProcess 切割图的进程
     * @return 结果
     */
    @Override
    public int insertWebSegProcess(WebSegProcess webSegProcess)
    {
        return webSegProcessMapper.insertWebSegProcess(webSegProcess);
    }

    /**
     * 修改切割图的进程
     * 
     * @param webSegProcess 切割图的进程
     * @return 结果
     */
    @Override
    public int updateWebSegProcess(WebSegProcess webSegProcess)
    {
        return webSegProcessMapper.updateWebSegProcess(webSegProcess);
    }

    /**
     * 批量删除切割图的进程
     * 
     * @param taskIds 需要删除的切割图的进程主键
     * @return 结果
     */
    @Override
    public int deleteWebSegProcessByTaskIds(Long[] taskIds)
    {
        return webSegProcessMapper.deleteWebSegProcessByTaskIds(taskIds);
    }

    /**
     * 删除切割图的进程信息
     * 
     * @param taskId 切割图的进程主键
     * @return 结果
     */
    @Override
    public int deleteWebSegProcessByTaskId(Long taskId)
    {
        return webSegProcessMapper.deleteWebSegProcessByTaskId(taskId);
    }
}
