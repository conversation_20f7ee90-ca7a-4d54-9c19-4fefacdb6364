package com.dataxai.web.service;

import com.aliyuncs.exceptions.ClientException;
import com.dataxai.web.dto.PromptDTO;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.security.access.method.P;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;


public interface AliYunFileService {

    public String uploadALiYun(MultipartFile file) ;

    //鉴定图片 入参加域名的图片地址
    Boolean  authenticateUrl(String urls) throws Exception;

    public Boolean batchDelete(List<String> fileUrls,Boolean checkStatus,Integer integer);


    Map<String,Object> fileUploadTable(MultipartFile file);

    Map<String,Object> validateUploadTable(MultipartFile file) throws IOException;

    List<PromptDTO> parseTable (MultipartFile file);

    // 上传临时文件
    String upload(ByteArrayResource imag);

    List<PromptDTO> readTable( String table);

    List<String> batchUploadALiYun(List<MultipartFile> files);

    String batchZipUrl(String imeUrls) throws Exception;

}
