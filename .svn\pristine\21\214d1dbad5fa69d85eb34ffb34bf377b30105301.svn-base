package com.dataxai.web.utils.wechat;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.dataxai.common.core.redis.RedisCache;
import com.dataxai.web.Constants.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class WeChatUtil {

    private String appID = "wxf380a931b228a6bc";


    private String appsecret = "3a29e49bf9e28ea825eaaa3426d2a81e";

    @Autowired
    private RedisCache redisCache;

    private static final String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";
    private static final String TICKET_URL = "https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s";
    private static final String USER_INFO_URL = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s&lang=zh_CN";


    /**
     * 获取 access_token
     */
    public String getAccessToken() {
        String accessTokenUrl = String.format(ACCESS_TOKEN_URL, appID, appsecret);
        String s = HttpUtil.get(accessTokenUrl);
        JSONObject result = JSONUtil.parseObj(s);
        String access_token = result.getStr("access_token");
        Integer expires_in = result.getInt("expires_in");
        redisCache.setCacheObject(Constants.ACCESS_TOKEN, access_token, expires_in, TimeUnit.SECONDS);
        return access_token;
    }
//
//    /**
//     * 获取 创建二维码的ticket
//     * @param scene_str
//     */
//    public JSONObject getTicket(String scene_str) throws Exception {
//        String ticketUrl = String.format(TICKET_URL, getReidAccessToken());
//        String body = "{" +
//                "expire_seconds":"1200," +
//                "action_name": "QR_STR_SCENE"," +
//                ""action_info": {" +
//                "scene": {" +
//                ""scene_str": "" + scene_str + """ +
//                "}" +
//                "}" +
//                "}";
//        String ticketJson = HttpUtil.post(ticketUrl, body);
//        return JSONUtil.parseObj(ticketJson);
//    }
//
//    /**
//     * 根据 openid 和 access_token 获取用户信息
//     */
//    public String getUserInfo(String openid) throws Exception {
//        String userInfoUrl = String.format(USER_INFO_URL, getReidAccessToken(), openid);
//        return HttpUtil.get(userInfoUrl);
//    }
//
//    private String getReidAccessToken() throws Exception {
//        String access_token = redisCache.getCacheObject(Constants.ACCESS_TOKEN);
//        if (StringUtils.isBlank(access_token)) {
//            // access_token 过期了
//            // 刷新 access_token
//            access_token = getAccessToken();
//        }
//        return access_token;
//    }
}