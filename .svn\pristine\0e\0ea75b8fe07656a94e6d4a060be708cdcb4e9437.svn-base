<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.BatchMapper">

    <resultMap id="batchMap" type="batch">
            <id property="batchId" column="batch_id" />
            <result property="batchNumber" column="batch_number" />
            <result property="type" column="type" />
            <result property="userId" column="user_id" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="delFlag" column="del_flag" />
            <result property="totalAmount" column="total_amount" />
            <result property="successAmount" column="success_amount" />
            <result property="failAmount" column="fail_amount" />
            <result property="remark" column="remark" />
    </resultMap>
    <resultMap id="BatchTaskResult" type="batch" extends="batchMap">
        <collection property="tasks" notNullColumn="task_id" javaType="java.util.List" resultMap="TaskResult" />
    </resultMap>

    <resultMap type="Task" id="TaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="batchId"    column="batch_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="type"    column="type"    />
        <result property="userId"    column="user_id"    />
        <result property="referedTaskId"    column="refered_task_id"    />
        <result property="status"    column="status"    />
        <result property="segData"    column="seg_data"    />
        <result property="executeTime"    column="execute_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="originalUrl"    column="original_url"    />
        <result property="thumbnailUrl"    column="thumbnail_url"    />
        <result property="proceing"    column="proceing"    />
        <result property="fourK"    column="four_k"    />
        <result property="descStatus"    column="desc_status"    />
        <result property="description"    column="description"    />
        <result property="frameCoordinates"    column="frame_coordinates"    />
        <result property="markImgUrl"    column="mark_img_url"    />
    </resultMap>

    <sql id="Base_Column_List">
        batch_id,batch_number,type,user_id,status,create_time,
        update_time,del_flag,total_amount,success_amount,fail_amount,
        remark
    </sql>
    <insert id="insertBat" parameterType="batch">
        INSERT INTO t_batch (
        <trim suffixOverrides=",">
            <if test="batchId != null">batch_id,</if>
            <if test="batchNumber != null">batch_number,</if>
            <if test="type != null">type,</if>
            <if test="userId != null">user_id,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="successAmount != null">success_amount,</if>
            <if test="failAmount != null">fail_amount,</if>
            <if test="remark != null">remark,</if>
        </trim>
        ) VALUES (
        <trim suffixOverrides=",">
            <if test="batchId != null">#{batchId},</if>
            <if test="batchNumber != null">#{batchNumber},</if>
            <if test="type != null">#{type},</if>
            <if test="userId != null">#{userId},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="successAmount != null">#{successAmount},</if>
            <if test="failAmount != null">#{failAmount},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
        )
    </insert>

    <update id="updateBat" parameterType="batch">
        UPDATE t_batch
        <set>
            <if test="batchNumber != null">batch_number = #{batchNumber},</if>
            <if test="type != null">type = #{type},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="successAmount != null">success_amount = #{successAmount},</if>
            <if test="failAmount != null">fail_amount = #{failAmount},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE batch_id = #{batchId}
    </update>
    <select id="selectById" resultMap="batchMap">
        select  batch_id,batch_number,type,user_id,status,create_time,
                update_time,del_flag,total_amount,success_amount,fail_amount,
                remark from t_batch
        where batch_id=#{batchId} and del_flag=0
    </select>
    <!-- 动态分页查询 -->
    <select id="getInfo" resultMap="batchMap">
        SELECT batch_id,batch_number,type,user_id,status,create_time,
        update_time,del_flag,total_amount,success_amount,fail_amount,
        remark from t_batch
        WHERE del_flag = 0
        <if test="dto.type != null">
            AND type = #{dto.type}
        </if>
        <if test="dto.remark != null and dto.remark != ''">
            AND remark LIKE CONCAT('%', #{dto.remark}, '%')
        </if>
        <if test="dto.batchNumber != null and dto.batchNumber != ''">
            AND batch_number LIKE CONCAT('%', #{dto.batchNumber}, '%')
        </if>
        <if test="dto.userId != null">
            AND user_id = #{dto.userId}
        </if>
        <if test="dto.startTime != null">
            AND create_time <![CDATA[ >=]]> #{dto.startTime}
        </if>
        <if test="dto.endTime != null">
            AND create_time <![CDATA[ <=]]> #{dto.endTime}
        </if>
        ORDER BY create_time DESC
    </select>
    <select id="selectByTaskId" resultMap="batchMap">
        select
            tb.batch_id ,
            tb.batch_number ,
            tb.type ,
            tb.user_id ,
            tb.status ,
            tb.create_time ,
            tb.update_time ,
            tb.del_flag ,
            tb.total_amount ,
            tb.success_amount ,
            tb.fail_amount ,
            tb.remark
        from
            t_batch tb
                join t_task tt on
                tb.batch_id = tt.batch_id
        where
            tt.task_id =#{taskId};
    </select>
    <select id="getBatchByStatus" resultMap="batchMap">
        select
            tb.batch_id ,
            tb.batch_number ,
            tb.type ,
            tb.user_id ,
            tb.status ,
            tb.create_time ,
            tb.update_time ,
            tb.del_flag ,
            tb.total_amount ,
            tb.success_amount ,
            tb.fail_amount ,
            tb.remark
        from
            t_batch tb
        where
        status in
        <foreach item="status" collection="statusArray" open="(" separator="," close=")">
            #{status}
        </foreach>
        and type in
        <foreach item="type" collection="typeList" open="(" separator="," close=")">
            #{type}
        </foreach>
          and del_flag = 0
        order by
            tb.create_time asc
            limit 1;

    </select>

</mapper>
