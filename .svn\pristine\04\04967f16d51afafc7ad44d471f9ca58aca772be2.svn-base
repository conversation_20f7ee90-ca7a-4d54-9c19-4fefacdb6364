import request from '@/utils/request'

// 查询任务列表
export function listAdmintask(query) {
  return request({
    url: '/admintask/admintask/list',
    method: 'get',
    params: query
  })
}

// 查询任务详细
export function getAdmintask(taskId) {
  return request({
    url: '/admintask/admintask/' + taskId,
    method: 'get'
  })
}

// 新增任务
export function addAdmintask(data) {
  return request({
    url: '/admintask/admintask',
    method: 'post',
    data: data
  })
}

// 修改任务
export function updateAdmintask(data) {
  return request({
    url: '/admintask/admintask',
    method: 'put',
    data: data
  })
}

// 删除任务
export function delAdmintask(taskId) {
  return request({
    url: '/admintask/admintask/' + taskId,
    method: 'delete'
  })
}
