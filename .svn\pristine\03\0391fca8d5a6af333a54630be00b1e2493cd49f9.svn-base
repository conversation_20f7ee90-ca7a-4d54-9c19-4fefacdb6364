<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.TBaseinfoBusinessMapper">
    
    <resultMap type="TBaseinfoBusiness" id="TBaseinfoBusinessResult">
        <result property="id"    column="id"    />
        <result property="customizedBusinessName"    column="customized_business_name"    />
        <result property="customizedBusinessContent"    column="customized_business_content"    />
    </resultMap>

    <sql id="selectTBaseinfoBusinessVo">
        select id, customized_business_name, customized_business_content from t_baseinfo_business
    </sql>

    <select id="selectTBaseinfoBusinessList" parameterType="TBaseinfoBusiness" resultMap="TBaseinfoBusinessResult">
        <include refid="selectTBaseinfoBusinessVo"/>
        <where>  
            <if test="customizedBusinessName != null  and customizedBusinessName != ''"> and customized_business_name like concat('%', #{customizedBusinessName}, '%')</if>
        </where>
    </select>
    
    <select id="selectTBaseinfoBusinessById" parameterType="Long" resultMap="TBaseinfoBusinessResult">
        <include refid="selectTBaseinfoBusinessVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTBaseinfoBusiness" parameterType="TBaseinfoBusiness" useGeneratedKeys="true" keyProperty="id">
        insert into t_baseinfo_business
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="customizedBusinessName != null">customized_business_name,</if>
            <if test="customizedBusinessContent != null">customized_business_content,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="customizedBusinessName != null">#{customizedBusinessName},</if>
            <if test="customizedBusinessContent != null">#{customizedBusinessContent},</if>
         </trim>
    </insert>

    <update id="updateTBaseinfoBusiness" parameterType="TBaseinfoBusiness">
        update t_baseinfo_business
        <trim prefix="SET" suffixOverrides=",">
            <if test="customizedBusinessName != null">customized_business_name = #{customizedBusinessName},</if>
            <if test="customizedBusinessContent != null">customized_business_content = #{customizedBusinessContent},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTBaseinfoBusinessById" parameterType="Long">
        delete from t_baseinfo_business where id = #{id}
    </delete>

    <delete id="deleteTBaseinfoBusinessByIds" parameterType="String">
        delete from t_baseinfo_business where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>