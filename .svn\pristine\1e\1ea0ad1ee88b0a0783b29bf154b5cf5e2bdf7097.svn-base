package com.dataxai.web.service.impl;

import java.math.BigDecimal;
import java.util.List;

import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.file.ImageUtils;
import com.dataxai.web.Constants.CommonStatusEnum;
import com.dataxai.web.domain.QrCodeResponse;
import com.dataxai.web.domain.TariffPackage;
import com.dataxai.web.mapper.TariffPackageMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.RefuelingBagMapper;
import com.dataxai.web.domain.RefuelingBag;
import com.dataxai.web.service.IRefuelingBagService;

/**
 * 加油包Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-01
 */
@Service
public class RefuelingBagServiceImpl implements IRefuelingBagService 
{
    @Autowired
    private RefuelingBagMapper refuelingBagMapper;

    @Autowired
    private TariffPackageMapper tariffPackageMapper;

    /**
     * 查询加油包
     * 
     * @param refuelingBagId 加油包主键
     * @return 加油包
     */
    @Override
    public RefuelingBag selectRefuelingBagByRefuelingBagId(String refuelingBagId)
    {
        return refuelingBagMapper.selectRefuelingBagByRefuelingBagId(refuelingBagId);
    }

    /**
     * 查询加油包列表
     * 
     * @param refuelingBag 加油包
     * @return 加油包
     */
    @Override
    public List<RefuelingBag> selectRefuelingBagList(RefuelingBag refuelingBag)
    {
        return refuelingBagMapper.selectRefuelingBagList(refuelingBag);
    }

    /**
     * 新增加油包
     * 
     * @param refuelingBag 加油包
     * @return 结果
     */
    @Override
    public int insertRefuelingBag(RefuelingBag refuelingBag)
    {
        return refuelingBagMapper.insertRefuelingBag(refuelingBag);
    }

    /**
     * 修改加油包
     * 
     * @param refuelingBag 加油包
     * @return 结果
     */
    @Override
    public int updateRefuelingBag(RefuelingBag refuelingBag)
    {
        return refuelingBagMapper.updateRefuelingBag(refuelingBag);
    }

    /**
     * 批量删除加油包
     * 
     * @param refuelingBagIds 需要删除的加油包主键
     * @return 结果
     */
    @Override
    public int deleteRefuelingBagByRefuelingBagIds(String[] refuelingBagIds)
    {
        return refuelingBagMapper.deleteRefuelingBagByRefuelingBagIds(refuelingBagIds);
    }

    /**
     * 删除加油包信息
     * 
     * @param refuelingBagId 加油包主键
     * @return 结果
     */
    @Override
    public int deleteRefuelingBagByRefuelingBagId(String refuelingBagId)
    {
        return refuelingBagMapper.deleteRefuelingBagByRefuelingBagId(refuelingBagId);
    }

    @Override
    public QrCodeResponse buyRefuelingBag(String tariffPackageId, String refuelingBagId) {
        //查询用户是否购买了套餐，根据套餐的折扣，来计算加油包的支付金额,支付成功后，将购买的加油包信息
        // 插入到用户加油包信息表中
        // 1.查询
        TariffPackage tariffPackage = tariffPackageMapper.selectTariffPackageByTariffPackageId(tariffPackageId);
        if(null == tariffPackage){
            throw new ServiceException(CommonStatusEnum.Tariff_Package_ERROR.getMessage(),CommonStatusEnum.Tariff_Package_ERROR.getCode());
        }
        RefuelingBag refuelingBag = refuelingBagMapper.selectRefuelingBagByRefuelingBagId(refuelingBagId);
        if(null == refuelingBag){
            throw new ServiceException(CommonStatusEnum.Refueling_Bag_ERROR.getMessage(),CommonStatusEnum.Refueling_Bag_ERROR.getCode());
        }
        //折扣信息
        Long discount = tariffPackage.getDiscount();
        BigDecimal moneyBigDecimal = refuelingBag.getMoney();
        BigDecimal discountBigDecimal = new BigDecimal(discount);
        BigDecimal payMoneyBigDecimal = moneyBigDecimal.multiply(discountBigDecimal);
        String codeUrl = "www.baidu.com     "+payMoneyBigDecimal.doubleValue();
        QrCodeResponse qrCodeResponse = new QrCodeResponse();
        qrCodeResponse.setQrUrl(codeUrl);
        return qrCodeResponse;
    }
}
