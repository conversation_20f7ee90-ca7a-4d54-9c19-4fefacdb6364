package com.dataxai.web.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 预设模板-模特信息对象 Web_character_model
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel
public class WebCharacterModel implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "主键id")
    private Long id;

    /** 正向关键词 */
    @Excel(name = "正向关键词")
    @ApiModelProperty(value = "正向关键词,执行任务时，只需要传递该参数的数据")
    private String prompt;

    /** 性别 */
    @Excel(name = "性别")
    @ApiModelProperty(value = "性别")
    private String gender;

    /** 年龄段 */
    @Excel(name = "年龄段")
    @ApiModelProperty(value = "年龄段")
    private String age;

    /** 肤色 */
    @Excel(name = "肤色")
    @ApiModelProperty(value = "肤色")
    private String skin;

    /** 表情 */
    @Excel(name = "表情")
    @ApiModelProperty(value ="表情")
    private String expression;

    /** 是否大码 */
    @Excel(name = "是否大码")
    @ApiModelProperty("是否大码")
    private String bigSize;

    /** 发型 */
    @Excel(name = "发型")
    @ApiModelProperty(value = "发型")
    private String hairStyles;

    /** 图片练接 */
    @Excel(name = "图片练接")
    @ApiModelProperty(value = "图片练接")
    private String image;

    /** 排序字段 */
    @Excel(name = "排序字段")
    private Integer sort;

    /** 是否有效 */
    @Excel(name = "是否有效")
    @JsonIgnore
    private Long isVaild;

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    private int pageNum;

    private int pageSize;

    public List<CharacterModel> getParams() {
        return params;
    }

    public void setParams(List<CharacterModel> params) {
        this.params = params;
    }

    private List<CharacterModel> params;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPrompt(String prompt) 
    {
        this.prompt = prompt;
    }

    public String getPrompt() 
    {
        return prompt;
    }
    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }
    public void setAge(String age) 
    {
        this.age = age;
    }

    public String getAge() 
    {
        return age;
    }
    public void setSkin(String skin) 
    {
        this.skin = skin;
    }

    public String getSkin() 
    {
        return skin;
    }
    public void setExpression(String expression) 
    {
        this.expression = expression;
    }

    public String getExpression() 
    {
        return expression;
    }
    public void setBigSize(String bigSize) 
    {
        this.bigSize = bigSize;
    }

    public String getBigSize() 
    {
        return bigSize;
    }
    public void setHairStyles(String hairStyles) 
    {
        this.hairStyles = hairStyles;
    }

    public String getHairStyles() 
    {
        return hairStyles;
    }
    public void setImage(String image) 
    {
        this.image = image;
    }

    public String getImage() 
    {
        return image;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public void setIsVaild(Long isVaild) 
    {
        this.isVaild = isVaild;
    }

    public Long getIsVaild() 
    {
        return isVaild;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("prompt", getPrompt())
            .append("gender", getGender())
            .append("age", getAge())
            .append("skin", getSkin())
            .append("expression", getExpression())
            .append("bigSize", getBigSize())
            .append("hairStyles", getHairStyles())
            .append("image", getImage())
            .append("isVaild", getIsVaild())
            .append("pagNum", getPageNum())
            .append("pageSize", getPageSize())
            .toString();
    }
}
