package com.dataxai.web.controller.push;

import com.alibaba.fastjson2.JSONObject;
import com.dataxai.web.domain.PushDTO;
import com.dataxai.web.entity.PushMessage;
import com.dataxai.web.service.PushMessageService;
import com.dataxai.web.websocket.SocketIOServerHandler;
import com.dataxai.web.websocket.WebSocketHandler;
import com.dataxai.web.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;

/**
 * 对外提供推送PC端功能，同时把推送的消息落库
 * <AUTHOR>
 * @description
 */
@RestController
@Slf4j
public class PushController {

    private final Integer PUSH_WAIT = 0;
    private final Integer PUSH_SUCCESS = 1;

    @Autowired
    private PushMessageService pushMessageService;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private SocketIOServerHandler socketIOServerHandler;

    @Autowired
    private WebSocketHandler webSocketHandler;


//    @PostMapping("/push")
//    public Map push(@RequestBody PushDTO pushDTO){
//        Map map = new HashMap();
//        //1、参数校验，非空~
//        if(StringUtils.isEmpty(pushDTO.getMessage()) || pushDTO.getUserId() == null || pushDTO.getPushType() == null )    {
//            log.info("【消息推送】 参数异常");
//            map.put("code","-1");
//            map.put("msg","参数异常");
//            return map;
//        }
//
//        //2、先将消息落库，默认未推送
//        PushMessage pushMessage = new PushMessage();
//        pushMessage.setContent(pushDTO.getMessage());
//        pushMessage.setUserId(pushDTO.getUserId());
//        pushMessage.setPushType(pushDTO.getPushType());
////        pushMessageService.insert(pushMessage);
//
//        //3、推送消息
//        boolean flag = webSocketServer.sendMsg(pushMessage.getUserId() + "", pushMessage.getContent());
//
//        //4、如果推送成功，修改状态，返回正确的JSON
//        if(flag){
//            PushMessage updatePushMessage = new PushMessage();
//            updatePushMessage.setId(pushMessage.getId());
//            updatePushMessage.setPushState(PUSH_SUCCESS);
////            pushMessageService.updateById(updatePushMessage);
//            map.put("code","0");
//            map.put("msg","成功");
//            return map;
//        }
//
//        //5、返回用户未上线的JSON
//        map.put("code","-2");
//        map.put("msg","用户离线，已保存信息");
//        return map;
//
//    }

    @PostMapping("/push")
    public Map push(@RequestBody PushDTO pushDTO){
        Map map = new HashMap();
//        //1、参数校验，非空~
//        if(StringUtils.isEmpty(pushDTO.getMessage()) || pushDTO.getUserId() == null || pushDTO.getPushType() == null )    {
//            log.info("【消息推送】 参数异常");
//            map.put("code","-1");
//            map.put("msg","参数异常");
//            return map;
//        }
//
//        //2、先将消息落库，默认未推送
//        String message = JSONObject.from(pushDTO.getMessage()).toString();
//        String content = JSONObject.from(pushDTO).toString();
//        PushMessage pushMessage = new PushMessage();
//        pushMessage.setContent(message);
//        pushMessage.setUserId(pushDTO.getUserId());
//        pushMessage.setPushType(pushDTO.getPushType());
//        //3、推送消息
//        boolean flag = webSocketServer.sendMsg(pushMessage.getUserId() + "", content);
//
//        //4、如果推送成功，修改状态，返回正确的JSON
//        if(flag){
//            PushMessage updatePushMessage = new PushMessage();
//            updatePushMessage.setId(pushMessage.getId());
//            updatePushMessage.setPushState(PUSH_SUCCESS);
////            pushMessageService.updateById(updatePushMessage);
//            map.put("code","200");
//            map.put("msg","成功");
//            return map;
//        }
//
//        //5、返回用户未上线的JSON
//        map.put("code","500");
//        map.put("msg","用户离线");
        return map;

    }

    @PostMapping("/push2")
    public Map push2(@RequestBody PushDTO pushDTO){
        Map map = new HashMap();
        if(StringUtils.isEmpty(pushDTO.getMessage()) || pushDTO.getUserId() == null || pushDTO.getPushType() == null )    {
            log.info("【消息推送】 参数异常");
            map.put("code","-1");
            map.put("msg","参数异常");
            return map;
        }

        //2、先将消息落库，默认未推送
        String message = JSONObject.from(pushDTO.getMessage()).toString();
        String content = JSONObject.from(pushDTO).toString();
        PushMessage pushMessage = new PushMessage();
        pushMessage.setContent(message);
        pushMessage.setUserId(pushDTO.getUserId());
        pushMessage.setPushType(pushDTO.getPushType());
        //3、推送消息
        boolean flag = socketIOServerHandler.sendMessageOne(pushMessage.getUserId() + "", content);
        //boolean flag = webSocketHandler.sendMessageOne(pushMessage.getUserId() + "", content);
        //4、如果推送成功，修改状态，返回正确的JSON
        if(flag){
            PushMessage updatePushMessage = new PushMessage();
            updatePushMessage.setId(pushMessage.getId());
            updatePushMessage.setPushState(PUSH_SUCCESS);
//            pushMessageService.updateById(updatePushMessage);
            map.put("code","200");
            map.put("msg","成功");
            return map;
        }

        //5、返回用户未上线的JSON
        map.put("code","500");
        map.put("msg","用户离线");
        return map;

    }

}
