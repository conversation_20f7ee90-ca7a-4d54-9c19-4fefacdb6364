package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.Web4kProcess;

/**
 * 4K图生成信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-02
 */
public interface Web4kProcessMapper 
{
    /**
     * 查询4K图生成信息
     * 
     * @param ordinal 4K图生成信息主键
     * @return 4K图生成信息
     */
    public Web4kProcess selectWeb4kProcessByOrdinal(Long ordinal);

    /**
     * 查询4K图生成信息列表
     * 
     * @param web4kProcess 4K图生成信息
     * @return 4K图生成信息集合
     */
    public List<Web4kProcess> selectWeb4kProcessList(Web4kProcess web4kProcess);

    /**
     * 新增4K图生成信息
     * 
     * @param web4kProcess 4K图生成信息
     * @return 结果
     */
    public int insertWeb4kProcess(Web4kProcess web4kProcess);

    /**
     * 修改4K图生成信息
     * 
     * @param web4kProcess 4K图生成信息
     * @return 结果
     */
    public int updateWeb4kProcess(Web4kProcess web4kProcess);

    /**
     * 删除4K图生成信息
     * 
     * @param ordinal 4K图生成信息主键
     * @return 结果
     */
    public int deleteWeb4kProcessByOrdinal(Long ordinal);

    /**
     * 批量删除4K图生成信息
     * 
     * @param ordinals 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWeb4kProcessByOrdinals(Long[] ordinals);
}
