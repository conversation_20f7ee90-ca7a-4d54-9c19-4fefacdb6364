package com.dataxai.web.controller.admincontroller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.AdminEquityDescription;
import com.dataxai.web.service.IAdminEquityDescriptionService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 权益Controller
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@RestController
@RequestMapping("/adminequity/admindescription")
public class AdminEquityDescriptionController extends BaseController
{
    @Autowired
    private IAdminEquityDescriptionService adminEquityDescriptionService;

    /**
     * 查询权益列表
     */
    @PreAuthorize("@ss.hasPermi('adminequity:admindescription:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdminEquityDescription adminEquityDescription)
    {
        startPage();
        List<AdminEquityDescription> list = adminEquityDescriptionService.selectAdminEquityDescriptionList(adminEquityDescription);
        return getDataTable(list);
    }

    /**
     * 导出权益列表
     */
    @PreAuthorize("@ss.hasPermi('adminequity:admindescription:export')")
    @Log(title = "权益", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdminEquityDescription adminEquityDescription)
    {
        List<AdminEquityDescription> list = adminEquityDescriptionService.selectAdminEquityDescriptionList(adminEquityDescription);
        ExcelUtil<AdminEquityDescription> util = new ExcelUtil<AdminEquityDescription>(AdminEquityDescription.class);
        util.exportExcel(response, list, "权益数据");
    }

    /**
     * 获取权益详细信息
     */
    @PreAuthorize("@ss.hasPermi('adminequity:admindescription:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(adminEquityDescriptionService.selectAdminEquityDescriptionById(id));
    }

    /**
     * 新增权益
     */
    @PreAuthorize("@ss.hasPermi('adminequity:admindescription:add')")
    @Log(title = "权益", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdminEquityDescription adminEquityDescription)
    {
        return toAjax(adminEquityDescriptionService.insertAdminEquityDescription(adminEquityDescription));
    }

    /**
     * 修改权益
     */
    @PreAuthorize("@ss.hasPermi('adminequity:admindescription:edit')")
    @Log(title = "权益", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdminEquityDescription adminEquityDescription)
    {
        return toAjax(adminEquityDescriptionService.updateAdminEquityDescription(adminEquityDescription));
    }

    /**
     * 删除权益
     */
    @PreAuthorize("@ss.hasPermi('adminequity:admindescription:remove')")
    @Log(title = "权益", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(adminEquityDescriptionService.deleteAdminEquityDescriptionByIds(ids));
    }
}
