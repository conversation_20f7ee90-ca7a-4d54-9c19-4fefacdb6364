package com.dataxai.web.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 退款记录对象 t_refund_info
 * 
 * <AUTHOR>
 * @date 2024-02-25
 */
public class RefundInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 退款单id */
    private String id;

    /** 商户订单编号 */
    @Excel(name = "商户订单编号")
    private String orderNo;

    /** 商户退款单编号 */
    @Excel(name = "商户退款单编号")
    private String refundNo;

    /** 支付系统退款单号 */
    @Excel(name = "支付系统退款单号")
    private String refundId;

    /** 原订单金额(分) */
    @Excel(name = "原订单金额(分)")
    private BigDecimal totalFee;

    /** 退款金额(分) */
    @Excel(name = "退款金额(分)")
    private BigDecimal refund;

    /** 退款原因 */
    @Excel(name = "退款原因")
    private String reason;

    /** 退款状态(0 - 退款成功,1 - 退款关闭,2 - 退款处理中,3 - 退款异常) */
    @Excel(name = "退款状态(0 - 退款成功,1 - 退款关闭,2 - 退款处理中,3 - 退款异常)")
    private Integer refundStatus;

    /** 申请退款返回参数 */
    @Excel(name = "申请退款返回参数")
    private String contentReturn;

    /** 退款结果通知参数 */
    @Excel(name = "退款结果通知参数")
    private String contentNotify;

    public void setId(String id) 
    {
        this.id = id;
    }

    public String getId() 
    {
        return id;
    }
    public void setOrderNo(String orderNo) 
    {
        this.orderNo = orderNo;
    }

    public String getOrderNo() 
    {
        return orderNo;
    }
    public void setRefundNo(String refundNo) 
    {
        this.refundNo = refundNo;
    }

    public String getRefundNo() 
    {
        return refundNo;
    }
    public void setRefundId(String refundId) 
    {
        this.refundId = refundId;
    }

    public String getRefundId() 
    {
        return refundId;
    }
    public void setTotalFee(BigDecimal totalFee) 
    {
        this.totalFee = totalFee;
    }

    public BigDecimal getTotalFee() 
    {
        return totalFee;
    }
    public void setRefund(BigDecimal refund) 
    {
        this.refund = refund;
    }

    public BigDecimal getRefund() 
    {
        return refund;
    }
    public void setReason(String reason) 
    {
        this.reason = reason;
    }

    public String getReason() 
    {
        return reason;
    }
    public void setRefundStatus(Integer refundStatus) 
    {
        this.refundStatus = refundStatus;
    }

    public Integer getRefundStatus() 
    {
        return refundStatus;
    }
    public void setContentReturn(String contentReturn) 
    {
        this.contentReturn = contentReturn;
    }

    public String getContentReturn() 
    {
        return contentReturn;
    }
    public void setContentNotify(String contentNotify) 
    {
        this.contentNotify = contentNotify;
    }

    public String getContentNotify() 
    {
        return contentNotify;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNo", getOrderNo())
            .append("refundNo", getRefundNo())
            .append("refundId", getRefundId())
            .append("totalFee", getTotalFee())
            .append("refund", getRefund())
            .append("reason", getReason())
            .append("refundStatus", getRefundStatus())
            .append("contentReturn", getContentReturn())
            .append("contentNotify", getContentNotify())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
