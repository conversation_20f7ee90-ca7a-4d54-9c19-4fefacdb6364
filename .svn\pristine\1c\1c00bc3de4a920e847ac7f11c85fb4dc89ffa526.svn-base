package com.dataxai.web.websocket;

import com.alibaba.fastjson2.JSONObject;
import com.dataxai.common.core.redis.RedisCache;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.entity.PushMessage;
import com.dataxai.web.service.PushMessageService;
import com.dataxai.web.utils.MyDateUtils;
import com.dataxai.web.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;
import com.dataxai.web.Constants.Constants;
/**
 * <AUTHOR>
 * @description
 */
@Component
//@ServerEndpoint("/webSocket/{userId}")
@Slf4j
public class WebSocketServer {

    /**
     *  记录有多少个用户建立了连接
     */
    private static LongAdder longAdder = new LongAdder();

    /**
     * 保存建立了webSocket连接的会话信息。
     * key：用户id  value：session
     */
    private static ConcurrentHashMap<String,Session> sessionPool = new ConcurrentHashMap<>();

    private RedisCache redisCache;

    // onopen
    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId")String userId){
        if(null == redisCache){
            redisCache = SpringUtil.getBean(RedisCache.class);
        }
        // 检查是否已经有相同用户的活动连接
        if (sessionPool.containsKey(userId)) {
            //如果会话ID已存在，则处理重复连接的情况
            Session existingSession = sessionPool.get(userId);
            try {
                if(existingSession.isOpen()){
                    existingSession.close();
                }
            } catch (IOException e) {
                // 处理关闭连接时可能出现的异常
                log.error("处理关闭连接时可能出现的异常      "+e.getMessage());
                e.printStackTrace();
            }
        }
        // 如果没有，将新会话添加到map中
        // 保存Session
        log.info("建立用户WebSocket连接成功 userId = " + userId);
        sessionPool.put(userId,session);
        longAdder.increment();
        redisCache.setCacheObject("socket:"+userId,session.getId()+"    "+ MyDateUtils.parseDateToStr(DateUtils.getNowDate()));


//        //1、根据用户id查询push_message表中的未推送消息的数据
//        PushMessageService pushMessageService = SpringUtil.getBean(PushMessageService.class);
//        List<PushMessage> list = pushMessageService.findByUserIdAndPushWait(Long.parseLong(userId));
//
//        //2、如果没有，直接结束
//        if(list == null || list.size() == 0){
//            // 没有未推送的消息
//            return ;
//        }
//
//        //3、如果有，遍历推送
//        for (PushMessage pushMessage : list) {
//            sendMsg(pushMessage.getUserId() + "",pushMessage.getContent());
//        }
//
//        //4、修改这些消息的状态
//        pushMessageService.updatePushSuccessByUserId(userId);
    }

    // onmessage
    @OnMessage
    public void onMessage(String message,@PathParam(value = "userId")String userId){
        log.info("用户标识为：" + userId + "，接收到消息：" + message);
        if(Constants.PING.equals(message)){
//            this.sendMsg(userId,Constants.PONG);
//            {
//                "message":{
//                  "heart":"pong"
//                },
//                "pushType":5,
//                "userId":10
//            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("userId",userId);
            jsonObject.put("pushType",Constants.PUSH_EVENT_TYPE_HEARTBEAT);
            JSONObject jsonObjectMessage = new JSONObject();
            jsonObjectMessage.put("heart",Constants.PONG);
            jsonObject.put("message",jsonObjectMessage);
            this.sendMsg(userId,jsonObject.toString());
        }else {
            this.sendMsg(userId,"服务方已经接收到消息了！");
        }
    }

    /**
     *  发送消息功能
     * @param userId   获取用户的Session
     * @param msg  发送的具体消息
     * @return  发送成功，返回true
     */
    public boolean sendMsg(String userId,String msg){
        //1、基于userId获取Session
        Session session = sessionPool.get(userId);

        //2、做非空判断
        if(session == null){
            // 用户没有建立连接
            return false;
        }

        //3、直接发送消息
        synchronized (session){
            try {
                session.getBasicRemote().sendText(msg);
            } catch (IOException e) {
                e.printStackTrace();
                return false;
            }
        }
        //4、返回结果
        return true;
    }

    // onerror
    @OnError
    public void onError(Throwable throwable){
        log.error("onError     "+throwable.getMessage());
        throwable.printStackTrace();
    }

    // onclose
    @OnClose
    public void onClose(@PathParam(value = "userId")String userId){
        // 移除用户会话信息
        log.info("移除用户Session信息 userId = " + userId);
        sessionPool.remove(userId);
        longAdder.decrement();
        redisCache.deleteObject("socket:"+userId);
    }

    /**
     * 主动断开连接
     */
    public void close(String userId) {
        //主动断开连接
        log.info("主动断开连接      "+userId);
        if(sessionPool.containsKey(userId)){
            Session session = sessionPool.get(userId);
            try {
                if(session.isOpen()){
                    log.info("session.close() 主动断开连接      "+userId);
                    session.close();
                }
            } catch (IOException e) {
                log.error("主动断开连接出现异常      "+e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }
}

