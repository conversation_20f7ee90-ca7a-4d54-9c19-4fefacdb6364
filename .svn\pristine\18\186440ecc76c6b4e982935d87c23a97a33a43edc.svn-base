package com.dataxai.web.service.impl;

import java.util.List;

import com.dataxai.web.utils.SnowFlakeUtils;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.AdminRefuelingBagMapper;
import com.dataxai.web.domain.AdminRefuelingBag;
import com.dataxai.web.service.IAdminRefuelingBagService;

/**
 * 加油包Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
public class AdminRefuelingBagServiceImpl implements IAdminRefuelingBagService 
{
    @Autowired
    private AdminRefuelingBagMapper adminRefuelingBagMapper;

    /**
     * 查询加油包
     * 
     * @param refuelingBagId 加油包主键
     * @return 加油包
     */
    @Override
    public AdminRefuelingBag selectAdminRefuelingBagByRefuelingBagId(String refuelingBagId)
    {
        return adminRefuelingBagMapper.selectAdminRefuelingBagByRefuelingBagId(refuelingBagId);
    }

    /**
     * 查询加油包列表
     * 
     * @param adminRefuelingBag 加油包
     * @return 加油包
     */
    @Override
    public List<AdminRefuelingBag> selectAdminRefuelingBagList(AdminRefuelingBag adminRefuelingBag)
    {
        return adminRefuelingBagMapper.selectAdminRefuelingBagList(adminRefuelingBag);
    }

    /**
     * 新增加油包
     * 
     * @param adminRefuelingBag 加油包
     * @return 结果
     */
    @Override
    public int insertAdminRefuelingBag(AdminRefuelingBag adminRefuelingBag)
    {
        adminRefuelingBag.setRefuelingBagId(SnowFlakeUtils.nextIdStr());
        if(adminRefuelingBag.getOnSale() == 1){
            List<AdminRefuelingBag> adminRefuelingBags = adminRefuelingBagMapper.selectOnSaleList();
            if(CollectionUtil.isNotEmpty(adminRefuelingBags) && adminRefuelingBags.size() > 3){
                return -1;
            }
        }
        return adminRefuelingBagMapper.insertAdminRefuelingBag(adminRefuelingBag);
    }

    /**
     * 修改加油包
     * 
     * @param adminRefuelingBag 加油包
     * @return 结果
     */
    @Override
    public int updateAdminRefuelingBag(AdminRefuelingBag adminRefuelingBag)
    {
        if(adminRefuelingBag.getOnSale() ==1){
            List<AdminRefuelingBag> adminRefuelingBags = adminRefuelingBagMapper.selectOnSaleList();
            String refuelingBagId = adminRefuelingBag.getRefuelingBagId();
            List<AdminRefuelingBag> collect = adminRefuelingBags.stream().filter(obj -> obj.getRefuelingBagId().equals(refuelingBagId)).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
                if(CollectionUtil.isNotEmpty(adminRefuelingBags) && adminRefuelingBags.size() > 4){
                    return -1;
                }
            }else {
                if(CollectionUtil.isNotEmpty(adminRefuelingBags) && adminRefuelingBags.size() > 3){
                    return -1;
                }
            }
        }
        return adminRefuelingBagMapper.updateAdminRefuelingBag(adminRefuelingBag);
    }

    /**
     * 批量删除加油包
     * 
     * @param refuelingBagIds 需要删除的加油包主键
     * @return 结果
     */
    @Override
    public int deleteAdminRefuelingBagByRefuelingBagIds(String[] refuelingBagIds)
    {
        return adminRefuelingBagMapper.deleteAdminRefuelingBagByRefuelingBagIds(refuelingBagIds);
    }

    /**
     * 删除加油包信息
     * 
     * @param refuelingBagId 加油包主键
     * @return 结果
     */
    @Override
    public int deleteAdminRefuelingBagByRefuelingBagId(String refuelingBagId)
    {
        return adminRefuelingBagMapper.deleteAdminRefuelingBagByRefuelingBagId(refuelingBagId);
    }
}
