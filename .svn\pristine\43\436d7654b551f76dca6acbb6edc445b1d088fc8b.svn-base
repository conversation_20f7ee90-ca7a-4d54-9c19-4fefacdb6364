# 项目相关配置
ruoyi:
    # 名称
    name: AiPhoto
    # 版本
    version: 3.8.7
    # 版权年份
    copyrightYear: 2023
    # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: /home/<USER>/dev/uploadPath
    # 获取ip地址开关
    addressEnabled: false
    # 验证码类型 math 数字计算 char 字符验证
    captchaType: math

# 开发环境配置
server:
    # 服务器的HTTP端口，默认为8080
    port: 8080
    servlet:
        # 应用的访问路径
        context-path:
    tomcat:
        # tomcat的URI编码
        uri-encoding: UTF-8
        # 连接数满后的排队数，默认为100
        accept-count: 1000
        threads:
            # tomcat最大线程数，默认为200
            max: 800
            # Tomcat启动初始化的线程数，默认值10
            min-spare: 100
        # 服务器在任何给定时间接受和处理的最大连接数。一旦达到限制，操作系统仍然可以接受基于“acceptCount”属性的连接。
        max-connections: 58192
#        不作限制
        max-http-form-post-size: -1
# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: ********************************************************************************************************************************************************
                username: dev-ai-photo
                password: JgddgObK$@&M@0nxeP
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: ruoyi
                login-password: 123456
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: false
                wall:
                    config:
                        multi-statement-allow: true
    servlet:
        # 应用的访问路径
        context-path: /

mybatis-plus:
    global-config:
        #    work-id: ${random.int(1,31)}
        #    datacenter-id: ${random.int(1,31)}
        work-id: 1
        datacenter-id: 3

#腾讯云api秘钥id
tencent:
    yun:
        secretid: AKIDDFHGJOLB0MlryJWUI1vtp4MqT7naPwLl
        secretkey: X9sDM1lO2yx1TnAg5ZBnQ9nLsWEys5DI
        region: ap-guangzhou
        Bucket: ai-photo-task-1303206685

# 微信开放平台 appid
wx:
    open:
        app_id: wx7f34ed9324d8439e
        # 微信开放平台 appsecret
        app_secret: 3732d11182d9a3beae57a5eb72bf2337
        # 微信开放平台重定向url，即扫码登录后回调的后端api，中间的ip地址是内网穿透的
        redirect_url:  http://fashion.dataxai.com.cn/wechat/callback
# 阿里云内网 发测试环境用这个 http://oss-cn-huhehaote-internal.aliyuncs.com
#阿里云公网  本地测试用 http://oss-cn-huhehaote.aliyuncs.com
aliyun:
    endpoint: http://oss-cn-huhehaote-internal.aliyuncs.com
    accessKeyId: LTAI5tMV98xMHPV9QbmLRu1R
    accessKeySecret: ******************************
    bucketName: ai-photo-task-1303206685
    region: cn-huhehaote
    green_endpoint: green-cip.cn-beijing.aliyuncs.com
    sms:
        sign: 星汇数智
        code: SMS_464500702
        region: cn-beijing
        ak: LTAI5tMKyxoMuQP4UT3LVg8G
        sk: ******************************
mydesgin:
#    上测试环境改定制器线上
    url: https://desgin.the2016.com/api/xiaoaishop/api/image_task/store
    sign: j7kH9nP2xL5vR8cT4w06aZLyY1HB0Hk4aJ8fG5pH9hW3cX3vT2yU7nE4qA0bS5mD
python:
    #    调用python 的识图转文接口
    gpu_read_pictures: http://*************:8004/pod_ai_i2t/
