package com.dataxai.web.utils.redis;

/**
 * @version 1.0
 * @Author:xg
 * @Date:2023-04-21 14:04
 * @Desciption:com.xg.internalcommon.util
 */
public class RedisPrefixUtils {
    public static final String VERIFICATIONCODEPREFIX = "verification-code:";
    public static final String TOKENPREFIX = "token:";
    // 黑名单设备号
    public static String BLACKDEVICECODEPREFIX = "black-device:";

    public static String generateRedisTokenKey(String phone,String identity,String tokeType){
        return TOKENPREFIX+phone+"-"+identity+"-"+tokeType;
    }

    public static String generateRedisVerificationCodeKey(String phone,String identity){
        return VERIFICATIONCODEPREFIX+identity+"-"+phone;
    }

    public static String generateRedisBlackDeviceCodeKey(String deviceCode){
        return BLAC<PERSON><PERSON>VICECO<PERSON>PREFIX+deviceCode;
    }

}
