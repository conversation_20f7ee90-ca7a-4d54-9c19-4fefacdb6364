package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.TTariffPackageSub;

/**
 * 子套餐Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface TTariffPackageSubMapper 
{
    /**
     * 查询子套餐
     * 
     * @param tariffPackageSubId 子套餐主键
     * @return 子套餐
     */
    public TTariffPackageSub selectTTariffPackageSubByTariffPackageSubId(String tariffPackageSubId);

    /**
     * 查询子套餐列表
     * 
     * @param tTariffPackageSub 子套餐
     * @return 子套餐集合
     */
    public List<TTariffPackageSub> selectTTariffPackageSubList(TTariffPackageSub tTariffPackageSub);

    /**
     * 新增子套餐
     * 
     * @param tTariffPackageSub 子套餐
     * @return 结果
     */
    public int insertTTariffPackageSub(TTariffPackageSub tTariffPackageSub);

    /**
     * 修改子套餐
     * 
     * @param tTariffPackageSub 子套餐
     * @return 结果
     */
    public int updateTTariffPackageSub(TTariffPackageSub tTariffPackageSub);

    /**
     * 删除子套餐
     * 
     * @param tariffPackageSubId 子套餐主键
     * @return 结果
     */
    public int deleteTTariffPackageSubByTariffPackageSubId(String tariffPackageSubId);

    int deleteTTariffPackageSubByTariffPackageId(String parentId);

    /**
     * 批量删除子套餐
     * 
     * @param tariffPackageSubIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTTariffPackageSubByTariffPackageSubIds(String[] tariffPackageSubIds);
}
