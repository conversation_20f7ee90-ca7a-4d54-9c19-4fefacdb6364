package com.dataxai.web.task;

import com.dataxai.domain.ProductImageUploadTask;
import com.dataxai.service.IProductImageUploadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;

/**
 * 产品图片上传任务处理器
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class ProductImageUploadProcessor {

    private final RedisTemplate<String, ProductImageUploadTask> redisTemplate;
    private final IProductImageUploadService productImageUploadService;
    
    private static final String PRODUCT_IMAGE_UPLOAD_QUEUE = "product:image:upload:queue";

    private final ExecutorService productImageExecutor = new ThreadPoolExecutor(
            2,
            5,
            60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(50),
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    @Autowired
    public ProductImageUploadProcessor(@Qualifier("productImageUploadRedisTemplate") RedisTemplate<String, ProductImageUploadTask> redisTemplate,
                                     IProductImageUploadService productImageUploadService) {
        this.redisTemplate = redisTemplate;
        this.productImageUploadService = productImageUploadService;
        startConsumer();
    }

    /**
     * 启动消费者线程
     */
    private void startConsumer() {
        productImageExecutor.submit(this::processProductImageUploadTasks);
    }

    /**
     * 处理产品图片上传任务
     */
    private void processProductImageUploadTasks() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                ProductImageUploadTask task = redisTemplate.opsForList().rightPop(PRODUCT_IMAGE_UPLOAD_QUEUE);
                if (task != null) {
                    log.info("开始处理产品图片上传任务：{}", task);
                    productImageUploadService.processImageUploadTask(task);
                } else {
                    // 队列为空时短暂休眠
                    Thread.sleep(1000);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("产品图片上传任务处理器被中断");
            } catch (Exception e) {
                log.error("处理产品图片上传任务异常: {}", e.getMessage(), e);
                try {
                    Thread.sleep(5000); // 异常后稍作休眠
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    /**
     * 获取队列状态
     */
    public String getQueueStatus() {
        try {
            Long queueSize = redisTemplate.opsForList().size(PRODUCT_IMAGE_UPLOAD_QUEUE);
            return String.format("产品图片上传队列长度: %d", queueSize != null ? queueSize : 0);
        } catch (Exception e) {
            return "获取队列状态失败: " + e.getMessage();
        }
    }

    /**
     * 关闭处理器
     */
    public void shutdown() {
        productImageExecutor.shutdown();
        try {
            if (!productImageExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                productImageExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            productImageExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
