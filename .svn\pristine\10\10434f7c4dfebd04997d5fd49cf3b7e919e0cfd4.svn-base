package com.dataxai.web.domain;

import java.io.Serializable;
import java.util.List;
import java.util.Date;
import java.util.concurrent.atomic.AtomicLong;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 任务对象 t_task
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@Data
public class Task implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 任务id */
    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "批次ID")
    private String batchId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    @ApiModelProperty(value = "任务名称",hidden = true)
    private String taskName;

    /** 任务类型(0-真人图，1-人台图，2-商品图，3-配饰图) */
    @Excel(name = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)")
    @ApiModelProperty(value = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)",required = true)
    private Long type;

    /** 任务所属用户id */
    @Excel(name = "任务所属用户id")
    @ApiModelProperty(value = "任务所属用户id",hidden = true)
    private Long userId;

    /** 相关任务id */
    @Excel(name = "相关任务id")
    @ApiModelProperty(value = "相关任务id,通过复制创意方式创建任务时，需要传递该参数")
    private String referedTaskId;

    /** 任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中) */
    @Excel(name = "任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中)")
    @ApiModelProperty(value = "任务执行状态(0-编辑中，1-成功，2-失败，3-执行中，4-排队中,5-准备中)",hidden = true)
    private Integer status;

    /** 任务执行 时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "任务执行 时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ApiModelProperty(hidden = true)
    private Date executeTime;

    /** 是否删除(0-未删除，1-已删除) */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private Integer delFlag;

    //图片生成描述进度 图片裁剪进度 未裁剪0 裁剪中1 裁剪结束2
    private Integer descStatus;

    //图片生成描述
    private String description;

    @ApiModelProperty(value = "缩略图")
    private String thumbnailUrl;

    // 裁剪框坐标信息
    private String frameCoordinates;

    //处理过后的图片地址
    private String markImgUrl;

    @ApiModelProperty(value = "是否上传过设计器 0：未上传，1：已上传")
    private boolean hasUploaded;



    /** 任务次数信息 */
    @ApiModelProperty(hidden = true)
    private List<TaskOrdinal> taskOrdinalList;

    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskId()
    {
        return taskId;
    }
    public void setTaskName(String taskName)
    {
        this.taskName = taskName;
    }

    public String getTaskName()
    {
        return taskName;
    }
    public void setType(Long type)
    {
        this.type = type;
    }

    public Long getType()
    {
        return type;
    }
    public void setUserId(Long userId)
    {
        this.userId = userId;
    }

    public Long getUserId()
    {
        return userId;
    }
    public void setReferedTaskId(String referedTaskId)
    {
        this.referedTaskId = referedTaskId;
    }

    public String getReferedTaskId()
    {
        return referedTaskId;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }
    public void setExecuteTime(Date executeTime)
    {
        this.executeTime = executeTime;
    }

    public Date getExecuteTime()
    {
        return executeTime;
    }
    public void setDelFlag(Integer delFlag)
    {
        this.delFlag = delFlag;
    }

    public Integer getDelFlag()
    {
        return delFlag;
    }

    public List<TaskOrdinal> getTaskOrdinalList()
    {
        return taskOrdinalList;
    }

    public void setTaskOrdinalList(List<TaskOrdinal> taskOrdinalList)
    {
        this.taskOrdinalList = taskOrdinalList;
    }

    /** 创建者 */
    @ApiModelProperty(hidden = true)
    private String createBy;

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间",hidden = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    @ApiModelProperty(hidden = true)
    private String updateBy;

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间",hidden = true)
    private Date updateTime;

    public String getOriginalUrl() {
        return originalUrl;
    }

    public void setOriginalUrl(String originalUrl) {
        this.originalUrl = originalUrl;
    }

    private String originalUrl;

    public String getSegData() {
        return segData;
    }

    public void setSegData(String segData) {
        this.segData = segData;
    }

    @ApiModelProperty(value = "图片切割信息")
    private String segData;

    public Double getProceing() {
        return proceing;
    }

    public void setProceing(Double proceing) {
        this.proceing = proceing;
    }

    @ApiModelProperty(value = "图片切割进度")
    private Double proceing;

//    public Integer getFourK() {
//        return fourK;
//    }
//
//    public void setFourK(Integer fourK) {
//        this.fourK = fourK;
//    }

    public Integer getFourK() {
        return fourK;
    }

    public void setFourK(Integer fourK) {
        this.fourK = fourK;
    }

    @ApiModelProperty(value = "4k图任务,0-非4k图任务，1-是4k图任务",hidden = true)
    @JsonIgnore
    private Integer fourK = 0;

    private int waitTime;
    private String pushId;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("taskName", getTaskName())
            .append("type", getType())
            .append("userId", getUserId())
            .append("referedTaskId", getReferedTaskId())
            .append("status", getStatus())
            .append("executeTime", getExecuteTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("delFlag", getDelFlag())
            .append("originalUrl", getOriginalUrl())
            .append("taskOrdinalList", getTaskOrdinalList())
            .toString();
    }
}
