package com.dataxai.web.utils;

import cn.hutool.http.HttpUtil;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.framework.tencent.ConstantOssPropertiesUtils;
import com.dataxai.web.Constants.Constants;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.BasicCOSCredentials;
import com.qcloud.cos.auth.COSCredentials;
import com.qcloud.cos.exception.CosClientException;
import com.qcloud.cos.exception.CosServiceException;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.http.HttpProtocol;
import com.qcloud.cos.model.*;
import com.qcloud.cos.model.ciModel.auditing.*;
import com.qcloud.cos.model.ciModel.persistence.CIObject;
import com.qcloud.cos.model.ciModel.persistence.CIUploadResult;
import com.qcloud.cos.model.ciModel.persistence.ProcessResults;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.transfer.Download;
import com.qcloud.cos.transfer.TransferManager;
import com.qcloud.cos.transfer.TransferManagerConfiguration;
import com.qcloud.cos.transfer.Upload;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Slf4j
public class CosUtils {
 
//    private static String secretId = "**************";
//    private static String secretKey = "**************";
//    private static String bucketName = "zs-1259422979";//bucket名称
//    private static String regionName = "ap-guangzhou";  //地区
//    private static String baseUrl = "https://zs-1259422979.cos.ap-guangzhou.myqcloud.com"; //cos基本地址
//    private static String appId = "*****";
//    private static String secretId;
//    private static String secretKey;
//    private static String bucketName;//bucket名称
//    private static String regionName;  //地区
//    private static String appId = "*****";

    private static String secretId = "AKIDDFHGJOLB0MlryJWUI1vtp4MqT7naPwLl";

    private static String secretKey = "X9sDM1lO2yx1TnAg5ZBnQ9nLsWEys5DI";
    private static String bucket = "ai-photo-task-1303206685";
    private static String regionStr = "ap-guangzhou";
 
    static TransferManager transferManager = null;
 
    //获取cosClient
    public static COSClient getCosClient() {
//        String baseUrl = "https://"+bucketName+".cos."+regionName+".myqcloud.com"; //cos基本地址
//        System.out.println(baseUrl);
//        // 1 初始化用户身份信息（secretId, secretKey）。
//        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
//        // 2 设置 bucket 的区域, COS 地域的简称请参照 https://cloud.tencent.com/document/product/436/6224
//        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
//        Region region = new Region(regionName);
//        ClientConfig clientConfig = new ClientConfig(region);
//        // 3 生成 cos 客户端。
//        COSClient cosClient = new COSClient(cred, clientConfig);
//        // 指定要上传到 COS 上的路径
//        ExecutorService threadPool = Executors.newFixedThreadPool(32);
//        // 传入一个 threadpool, 若不传入线程池, 默认 TransferManager 中会生成一个单线程的线程池。
//        transferManager = new TransferManager(cosClient, threadPool);
//        return cosClient;
        // 1 初始化用户身份信息（secretId, secretKey）。
        // SECRETID和SECRETKEY请登录访问管理控制台 https://console.cloud.tencent.com/cam/capi 进行查看和管理


        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置 bucket 的地域, COS 地域的简称请参照 https://cloud.tencent.com/document/product/436/6224
        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
        Region region = new Region(regionStr);
        ClientConfig clientConfig = new ClientConfig(region);

        // 这里建议设置使用 https 协议
        // 从 5.6.54 版本开始，默认使用了 https
        clientConfig.setHttpProtocol(HttpProtocol.https);

        // 3 生成 cos 客户端。
        COSClient cosClient = new COSClient(cred, clientConfig);
        // 指定要上传到 COS 上的路径
        ExecutorService threadPool = Executors.newFixedThreadPool(32);
        // 传入一个 threadpool, 若不传入线程池, 默认 TransferManager 中会生成一个单线程的线程池。
        transferManager = new TransferManager(cosClient, threadPool);
        return cosClient;
    }
 
    //1.普通上传
    public static String  uploadCos(File localFile ,String key){
        //获取客户端
        COSClient cosClient =getCosClient();
        // 指定要上传到 COS 的文件路径
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, localFile);
        PutObjectResult putObjectResult = cosClient.putObject(putObjectRequest);
        CIUploadResult ciUploadResult = putObjectResult.getCiUploadResult();
        if(null != ciUploadResult){
            ProcessResults processResults = ciUploadResult.getProcessResults();
            List<CIObject> objectList = processResults.getObjectList();
            for (CIObject ciObject : objectList) {
                String location = ciObject.getLocation();
                System.out.println(location);
            }
            System.out.println(ciUploadResult);
        }

        Date expiration = new Date(new Date().getTime() + 5 * 60 * 10000);

        GeneratePresignedUrlRequest req =
                new GeneratePresignedUrlRequest(bucket, key, HttpMethodName.GET);
        // 设置签名过期时间(可选), 若未进行设置, 则默认使用 ClientConfig 中的签名过期时间(1小时)
        // 可以设置任意一个未来的时间，推荐是设置 10 分钟到 3 天的过期时间
        req.setExpiration(expiration);

        URL url = cosClient.generatePresignedUrl(req);

        //关闭客户端
        cosClient.shutdown();
//        String baseUrl = "https://"+bucket+".cos."+regionStr+".myqcloud.com"; //cos基本地址
//        return baseUrl+"/"+key;

        //拼接文件地址
        StringBuffer stringBuffer = new StringBuffer()
                .append(url.getProtocol())
                .append("://")
                .append(url.getHost())
                .append(url.getPath());

        return stringBuffer.toString();

    }

    //2.高级上传
    public static String  uploadCosTransfer(File localFile ,String key){
        // 使用高级接口必须先保证本进程存在一个 TransferManager 实例，如果没有则创建
// 详细代码参见本页：高级接口 -> 创建 TransferManager
        TransferManager transferManager = createTransferManager();

// 存储桶的命名格式为 BucketName-APPID，此处填写的存储桶名称必须为此格式
//        String bucketName = "examplebucket-1250000000";
// 对象键(Key)是对象在存储桶中的唯一标识。
//        String key = "exampleobject";
// 本地文件路径
//        String localFilePath = "/path/to/localFile";
//        File localFile = new File(localFilePath);

        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, localFile);

//// 设置存储类型（如有需要，不需要请忽略此行代码）, 默认是标准(Standard), 低频(standard_ia)
//// 更多存储类型请参见 https://cloud.tencent.com/document/product/436/33417
//        putObjectRequest.setStorageClass(StorageClass.Standard_IA);

//若需要设置对象的自定义 Headers 可参照下列代码,若不需要可省略下面这几行,对象自定义 Headers 的详细信息可参考 https://cloud.tencent.com/document/product/436/13361
//        ObjectMetadata objectMetadata = new ObjectMetadata();
//
////若设置 Content-Type、Cache-Control、Content-Disposition、Content-Encoding、Expires 这五个字自定义 Headers，推荐采用 objectMetadata.setHeader()
//        objectMetadata.setHeader(key, value);
////若要设置 “x-cos-meta-[自定义后缀]” 这样的自定义 Header，推荐采用
//        Map<String, String> userMeta = new HashMap<String, String>();
//        userMeta.put("x-cos-meta-[自定义后缀]", "value");
//        objectMetadata.setUserMetadata(userMeta);
//
//        putObjectRequest.withMetadata(objectMetadata);

        try {
            // 高级接口会返回一个异步结果Upload
            // 可同步地调用 waitForUploadResult 方法等待上传完成，成功返回 UploadResult, 失败抛出异常
            Upload upload = transferManager.upload(putObjectRequest);
            UploadResult uploadResult = upload.waitForUploadResult();
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }


        Date expiration = new Date(new Date().getTime() + 5 * 60 * 10000);

        GeneratePresignedUrlRequest req =
                new GeneratePresignedUrlRequest(bucket, key, HttpMethodName.GET);
        // 设置签名过期时间(可选), 若未进行设置, 则默认使用 ClientConfig 中的签名过期时间(1小时)
        // 可以设置任意一个未来的时间，推荐是设置 10 分钟到 3 天的过期时间
        req.setExpiration(expiration);

        URL url = getCosClient().generatePresignedUrl(req);

        // 确定本进程不再使用 transferManager 实例之后，关闭即可
// 详细代码参见本页：高级接口 -> 关闭 TransferManager
        shutdownTransferManager(transferManager);
        //拼接文件地址
        StringBuffer stringBuffer = new StringBuffer()
                .append(url.getProtocol())
                .append("://")
                .append(url.getHost())
                .append(url.getPath());

        return stringBuffer.toString();
    }

    //2.高级上传
    public static String  uploadCosTransfer(String localFilePath ,String key){
        // 使用高级接口必须先保证本进程存在一个 TransferManager 实例，如果没有则创建
// 详细代码参见本页：高级接口 -> 创建 TransferManager
        TransferManager transferManager = createTransferManager();

// 存储桶的命名格式为 BucketName-APPID，此处填写的存储桶名称必须为此格式
//        String bucketName = "examplebucket-1250000000";
// 对象键(Key)是对象在存储桶中的唯一标识。
//        String key = "exampleobject";
// 本地文件路径
//        String localFilePath = "/path/to/localFile";
        File localFile = new File(localFilePath);

        PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, key, localFile);

//// 设置存储类型（如有需要，不需要请忽略此行代码）, 默认是标准(Standard), 低频(standard_ia)
//// 更多存储类型请参见 https://cloud.tencent.com/document/product/436/33417
//        putObjectRequest.setStorageClass(StorageClass.Standard_IA);

//若需要设置对象的自定义 Headers 可参照下列代码,若不需要可省略下面这几行,对象自定义 Headers 的详细信息可参考 https://cloud.tencent.com/document/product/436/13361
//        ObjectMetadata objectMetadata = new ObjectMetadata();
//
////若设置 Content-Type、Cache-Control、Content-Disposition、Content-Encoding、Expires 这五个字自定义 Headers，推荐采用 objectMetadata.setHeader()
//        objectMetadata.setHeader(key, value);
////若要设置 “x-cos-meta-[自定义后缀]” 这样的自定义 Header，推荐采用
//        Map<String, String> userMeta = new HashMap<String, String>();
//        userMeta.put("x-cos-meta-[自定义后缀]", "value");
//        objectMetadata.setUserMetadata(userMeta);
//
//        putObjectRequest.withMetadata(objectMetadata);
        try {
            // 高级接口会返回一个异步结果Upload
            // 可同步地调用 waitForUploadResult 方法等待上传完成，成功返回 UploadResult, 失败抛出异常
            Upload upload = transferManager.upload(putObjectRequest);
            UploadResult uploadResult  = upload.waitForUploadResult();
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

// 确定本进程不再使用 transferManager 实例之后，关闭即可
// 详细代码参见本页：高级接口 -> 关闭 TransferManager
        shutdownTransferManager(transferManager);
        Date expiration = new Date(new Date().getTime() + 5 * 60 * 10000);

        GeneratePresignedUrlRequest req =
                new GeneratePresignedUrlRequest(bucket, key, HttpMethodName.GET);
        // 设置签名过期时间(可选), 若未进行设置, 则默认使用 ClientConfig 中的签名过期时间(1小时)
        // 可以设置任意一个未来的时间，推荐是设置 10 分钟到 3 天的过期时间
        req.setExpiration(expiration);

        URL url = getCosClient().generatePresignedUrl(req);
        //拼接文件地址
        StringBuffer stringBuffer = new StringBuffer()
                .append(url.getProtocol())
                .append("://")
                .append(url.getHost())
                .append(url.getPath());

        return stringBuffer.toString();
    }

    // 创建 TransferManager 实例，这个实例用来后续调用高级接口
    public static TransferManager createTransferManager() {
        // 创建一个 COSClient 实例，这是访问 COS 服务的基础实例。
        // 详细代码参见本页: 简单操作 -> 创建 COSClient
        COSClient cosClient = getCosClient();

        // 自定义线程池大小，建议在客户端与 COS 网络充足（例如使用腾讯云的 CVM，同地域上传 COS）的情况下，设置成16或32即可，可较充分的利用网络资源
        // 对于使用公网传输且网络带宽质量不高的情况，建议减小该值，避免因网速过慢，造成请求超时。
        ExecutorService threadPool = Executors.newFixedThreadPool(32);

        // 传入一个 threadpool, 若不传入线程池，默认 TransferManager 中会生成一个单线程的线程池。
        TransferManager transferManager = new TransferManager(cosClient, threadPool);

        // 设置高级接口的配置项
        // 分块上传阈值和分块大小分别为 5MB 和 1MB
        TransferManagerConfiguration transferManagerConfiguration = new TransferManagerConfiguration();
        transferManagerConfiguration.setMultipartUploadThreshold(5*1024*1024);
        transferManagerConfiguration.setMinimumUploadPartSize(1*1024*1024);
        transferManager.setConfiguration(transferManagerConfiguration);

        return transferManager;
    }

    public static void shutdownTransferManager(TransferManager transferManager) {
        // 指定参数为 true, 则同时会关闭 transferManager 内部的 COSClient 实例。
        // 指定参数为 false, 则不会关闭 transferManager 内部的 COSClient 实例。
        transferManager.shutdownNow(true);
    }

    //2.普通下载
    public static void downLoadFile(String key,String downLoadPath) {
        // 1 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(regionStr));
        // 3 生成cos客户端
        COSClient cosclient = new COSClient(cred, clientConfig);
        File downloadFile = new File(downLoadPath);
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucket, key);
        ObjectMetadata downObjectMeta = cosclient.getObject(getObjectRequest, downloadFile);
        cosclient.shutdown();
    }

    //3.高级下载
    public static void downLoadFileTransfer(String key,String downLoadFilePath) {
        // 1 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region(regionStr));
        // 3 生成cos客户端
        COSClient cosclient = new COSClient(cred, clientConfig);
        ExecutorService threadPool = Executors.newFixedThreadPool(32);
        // 传入一个threadpool, 若不传入线程池, 默认TransferManager中会生成一个单线程的线程池。
        TransferManager transferManager = new TransferManager(cosclient, threadPool);
        File downloadFile = new File(downLoadFilePath);
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucket, key);
        try {
            // 返回一个异步结果copy, 可同步的调用waitForCompletion等待download结束, 成功返回void, 失败抛出异常.
            Download download = transferManager.download(getObjectRequest, downloadFile);
            download.waitForCompletion();
        } catch (CosServiceException e) {
            e.printStackTrace();
        } catch (CosClientException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        transferManager.shutdownNow();
        cosclient.shutdown();
    }

    /**
     * 获取流文件
     * @param ins
     * @param file
     */
    private static void inputStreamToFile(InputStream ins, File file) {
        try {
            OutputStream os = new FileOutputStream(file);
            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
            ins.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 此方法将multipartFile转为file
     *
     * @param file
     * @return java.io.File
     * <AUTHOR>
     * @date 2022-03-24 15:10:03
     **/
    private File multipartFileToFile(MultipartFile file)  {

        File toFile = null;
        if (file.equals("") || file.getSize() <= 0) {
            file = null;
        } else {

            try {
                InputStream ins = null;
                ins = file.getInputStream();
                toFile = new File(file.getOriginalFilename());
                inputStreamToFile(ins, toFile);
                ins.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return toFile;
    }

    public static void delteTempFile(File file) {
        if (file != null) {
            File del = new File(file.toURI());
            del.delete();
        }
    }

    public static void main(String[] args) {
        // 指定要上传的文件
//        File localFile = new File("C:\\Users\\<USER>\\Desktop\\直播AI工具\\071120191492.jpg");
//        String key = "cjrh";
//        String url = uploadCos(localFile,key);
//       System.out.println(url);

//
//        String downKey = "2024-01-09/d7fadacb716943f0b1b36c02e715bffe_1695541217516.png";
//       String downLoadFilePath = "C:\\Users\\<USER>\\Desktop\\直播AI工具\\9.jpg";
//       downLoadFile(downKey,downLoadFilePath);
//       downLoadFileTransfer(downKey,downLoadFilePath);

//        testTransferUpLoadAndDown();

////        String inputUrl = "https://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/2024-04-22/1782299834402082817/1782299834402082817_1_0.jpg";
//        String inputUrl = "https://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/2024-04-22/1782312161322864641/1782312161322864641_1_0.jpg";
//        imagesAuditCheck(inputUrl);
//        String inputUrl1 = "https://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/2024-04-22/1782301942211547138/1782301942211547138_1_0.jpg";
//        imagesAuditCheck(inputUrl1);
//        String inputUrl2 = "http://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/2024-04-17/1780548111413153793/1780548111413153793_1_2.jpg";
//        imagesAuditCheck(inputUrl2);

    }

    public static void testTransferUpLoadAndDown(){
        // 指定要上传的文件
        File localFile = new File("C:\\Users\\<USER>\\Desktop\\直播AI工具\\071120191492.jpg");
        String key = "111111111";
        String urlTransferResult = uploadCosTransfer(localFile,key);
        System.out.println(urlTransferResult);
        String downLoadFilePath = "C:\\Users\\<USER>\\Desktop\\直播AI工具\\10.jpg";
        downLoadFileTransfer(key,downLoadFilePath);
    }

    public static boolean imagesAuditCheck(String inputUrl) {
        // 初始化用户身份信息(secretId, secretKey)
        COSCredentials cred = new BasicCOSCredentials(secretId,secretKey);
        // 设置bucket的区域, COS地域的简称请参照 https://www.qcloud.com/document/product/436/6224
        ClientConfig clientConfig = new ClientConfig(new Region("ap-guangzhou"));
        // 生成cos客户端
        COSClient cosClient = new COSClient(cred, clientConfig);
        //1.创建任务请求对象
        BatchImageAuditingRequest request = new BatchImageAuditingRequest();
        //2.添加请求参数 参数详情请见 API 接口文档
        //2.1设置请求bucket
        request.setBucketName("ai-photo-task-1303206685");
        //2.2添加请求内容
        List<BatchImageAuditingInputObject> inputList = request.getInputList();
        BatchImageAuditingInputObject input = new BatchImageAuditingInputObject();
        input.setUrl(inputUrl);
        input.setDataId("DataId1");
        inputList.add(input);

        //2.2设置审核类型
        request.getConf().setDetectType("all");
        //3.调用接口,获取任务响应对象
        BatchImageAuditingResponse response = cosClient.batchImageAuditing(request);
        List<BatchImageJobDetail> jobList = response.getJobList();
//        System.out.println("jobList = " + jobList);
        if(null != jobList){
//            log.info("jobList.size() = "+jobList.size());
            for (BatchImageJobDetail item : jobList) {
//                log.info("item = " + item);
//                String code = item.getCode();
//                log.info("code = " + code);
//                AdsInfo adsInfo = item.getAdsInfo();
//                log.info("adsInfo = " + adsInfo);
//                TerroristInfo terroristInfo = item.getTerroristInfo();
//                log.info("terroristInfo = "+terroristInfo);
//                String forbidState = item.getForbidState();
//                log.info("forbidState = " + forbidState);
                String result = item.getResult();
//                System.out.println("result = " + result+"     "+"0".equals(result));
                if(Constants.SUCCESS_STR.equals(item.getState())){
                    boolean equals = "0".equals(result);
                    String resultStr = equals?"图片合规":"图片违规";
                    log.info("审核成功"+    resultStr+"     "+inputUrl);
                    return equals;
                }else {
                    log.error("审核失败   "+inputUrl);
                }
            }
        }
        return false;
    }


}