package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.AdminTariffPackage;

/**
 * 套餐Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface IAdminTariffPackageService 
{
    /**
     * 查询套餐
     * 
     * @param tariffPackageId 套餐主键
     * @return 套餐
     */
    public AdminTariffPackage selectAdminTariffPackageByTariffPackageId(String tariffPackageId);

    /**
     * 查询套餐列表
     * 
     * @param adminTariffPackage 套餐
     * @return 套餐集合
     */
    public List<AdminTariffPackage> selectAdminTariffPackageList(AdminTariffPackage adminTariffPackage);

    /**
     * 新增套餐
     * 
     * @param adminTariffPackage 套餐
     * @return 结果
     */
    public int insertAdminTariffPackage(AdminTariffPackage adminTariffPackage);

    /**
     * 修改套餐
     * 
     * @param adminTariffPackage 套餐
     * @return 结果
     */
    public int updateAdminTariffPackage(AdminTariffPackage adminTariffPackage);

    /**
     * 批量删除套餐
     * 
     * @param tariffPackageIds 需要删除的套餐主键集合
     * @return 结果
     */
    public int deleteAdminTariffPackageByTariffPackageIds(String[] tariffPackageIds);

    /**
     * 删除套餐信息
     * 
     * @param tariffPackageId 套餐主键
     * @return 结果
     */
    public int deleteAdminTariffPackageByTariffPackageId(String tariffPackageId);
}
