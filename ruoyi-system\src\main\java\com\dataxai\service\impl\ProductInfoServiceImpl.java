package com.dataxai.service.impl;

import java.util.List;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.event.ProductImageUploadEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import com.dataxai.mapper.ProductInfoMapper;
import com.dataxai.domain.ProductInfo;
import com.dataxai.service.IProductInfoService;

/**
 * 产品信息表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class ProductInfoServiceImpl implements IProductInfoService
{
    @Autowired
    private ProductInfoMapper productInfoMapper;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    /**
     * 查询产品信息表
     * 
     * @param id 产品信息表主键
     * @return 产品信息表
     */
    @Override
    public ProductInfo selectProductInfoById(Long id)
    {
        return productInfoMapper.selectProductInfoById(id);
    }

    /**
     * 查询产品信息表列表
     * 
     * @param productInfo 产品信息表
     * @return 产品信息表
     */
    @Override
    public List<ProductInfo> selectProductInfoList(ProductInfo productInfo)
    {
        return productInfoMapper.selectProductInfoList(productInfo);
    }

    /**
     * 新增产品信息表
     *
     * @param productInfo 产品信息表
     * @return 结果
     */
    @Override
    public int insertProductInfo(ProductInfo productInfo)
    {
        productInfo.setCreateTime(DateUtils.getNowDate());

        // 将产品图片地址保存到original_image_url字段
        if (StringUtils.isNotEmpty(productInfo.getProductImageUrl())) {
            productInfo.setOriginalImageUrl(productInfo.getProductImageUrl());
        }

        // 设置默认状态为1
        if (productInfo.getStatus() == null) {
            productInfo.setStatus("1");
        }

        int result = productInfoMapper.insertProductInfo(productInfo);

        // 如果插入成功且有原始图片URL，则发布图片上传事件
        if (result > 0 && StringUtils.isNotEmpty(productInfo.getOriginalImageUrl())) {
            try {
                Long userId = SecurityUtils.getUserId();
                eventPublisher.publishEvent(new ProductImageUploadEvent(this,
                    productInfo.getId(), productInfo.getOriginalImageUrl(), userId));
            } catch (Exception e) {
                // 如果获取用户ID失败，使用默认值或记录日志
                eventPublisher.publishEvent(new ProductImageUploadEvent(this,
                    productInfo.getId(), productInfo.getOriginalImageUrl(), null));
            }
        }

        return result;
    }

    /**
     * 修改产品信息表
     * 
     * @param productInfo 产品信息表
     * @return 结果
     */
    @Override
    public int updateProductInfo(ProductInfo productInfo)
    {
        productInfo.setUpdateTime(DateUtils.getNowDate());
        return productInfoMapper.updateProductInfo(productInfo);
    }

    /**
     * 批量删除产品信息表
     * 
     * @param ids 需要删除的产品信息表主键
     * @return 结果
     */
    @Override
    public int deleteProductInfoByIds(Long[] ids)
    {
        return productInfoMapper.deleteProductInfoByIds(ids);
    }

    /**
     * 删除产品信息表信息
     * 
     * @param id 产品信息表主键
     * @return 结果
     */
    @Override
    public int deleteProductInfoById(Long id)
    {
        return productInfoMapper.deleteProductInfoById(id);
    }
}
