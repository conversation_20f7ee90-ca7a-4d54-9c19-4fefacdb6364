package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.OrdinalImgResult;

/**
 * 每次任务执行后生成的图片Service接口
 * 
 * <AUTHOR>
 * @date 2024-01-14
 */
public interface IOrdinalImgResultService 
{
    /**
     * 查询每次任务执行后生成的图片
     * 
     * @param imageId 每次任务执行后生成的图片主键
     * @return 每次任务执行后生成的图片
     */
    public OrdinalImgResult selectOrdinalImgResultByImageId(String imageId);

    /**
     * 查询每次任务执行后生成的图片列表
     * 
     * @param ordinalImgResult 每次任务执行后生成的图片
     * @return 每次任务执行后生成的图片集合
     */
    public List<OrdinalImgResult> selectOrdinalImgResultList(OrdinalImgResult ordinalImgResult);

    /**
     * 新增每次任务执行后生成的图片
     * 
     * @param ordinalImgResult 每次任务执行后生成的图片
     * @return 结果
     */
    public int insertOrdinalImgResult(OrdinalImgResult ordinalImgResult);

    /**
     * 修改每次任务执行后生成的图片
     * 
     * @param ordinalImgResult 每次任务执行后生成的图片
     * @return 结果
     */
    public int updateOrdinalImgResult(OrdinalImgResult ordinalImgResult);

    /**
     * 批量删除每次任务执行后生成的图片
     * 
     * @param imageIds 需要删除的每次任务执行后生成的图片主键集合
     * @return 结果
     */
    public int deleteOrdinalImgResultByImageIds(String[] imageIds);

    /**
     * 删除每次任务执行后生成的图片信息
     * 
     * @param imageId 每次任务执行后生成的图片主键
     * @return 结果
     */
    public int deleteOrdinalImgResultByImageId(String imageId);

    List<OrdinalImgResult> listByUserIdTypes(OrdinalImgResult ordinalImgResult, List<Long> types);

}
