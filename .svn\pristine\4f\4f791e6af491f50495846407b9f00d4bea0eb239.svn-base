package com.dataxai.web.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.domain.AdminEquityDescription;
import com.dataxai.web.domain.TTariffPackageSub;
import com.dataxai.web.mapper.AdminEquityDescriptionMapper;
import com.dataxai.web.mapper.TTariffPackageSubMapper;
import com.dataxai.web.mapper.TariffPackageSubMapper;
import com.dataxai.web.utils.SnowFlakeUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.AdminTariffPackageMapper;
import com.dataxai.web.domain.AdminTariffPackage;
import com.dataxai.web.service.IAdminTariffPackageService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 套餐Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
public class AdminTariffPackageServiceImpl implements IAdminTariffPackageService 
{
    @Resource
    private AdminTariffPackageMapper adminTariffPackageMapper;

    @Resource
    private AdminEquityDescriptionMapper adminEquityDescriptionMapper;

    @Resource
    private TTariffPackageSubMapper tTariffPackageSubMapper;

    /**
     * 查询套餐
     * 
     * @param tariffPackageId 套餐主键
     * @return 套餐
     */
    @Override
    public AdminTariffPackage selectAdminTariffPackageByTariffPackageId(String tariffPackageId)
    {
        AdminTariffPackage adminTariffPackage = adminTariffPackageMapper.selectAdminTariffPackageByTariffPackageId(tariffPackageId);
        //查询描述
        AdminEquityDescription adminEquityDescription = new AdminEquityDescription();
        adminEquityDescription.setTariffId(tariffPackageId);
        List<AdminEquityDescription> adminEquityDescriptions = adminEquityDescriptionMapper.selectAdminEquityDescriptionList(adminEquityDescription);
        adminTariffPackage.setEquityDescriptionList(adminEquityDescriptions);
        //查询权益
        TTariffPackageSub tTariffPackageSub = new TTariffPackageSub();
        tTariffPackageSub.setParentId(tariffPackageId);
        List<TTariffPackageSub> tariffPackageSubList = tTariffPackageSubMapper.selectTTariffPackageSubList(tTariffPackageSub);
        adminTariffPackage.setTariffPackageSubList(tariffPackageSubList);
        return adminTariffPackage;
    }

    /**
     * 查询套餐列表
     * 
     * @param adminTariffPackage 套餐
     * @return 套餐
     */
    @Override
    public List<AdminTariffPackage> selectAdminTariffPackageList(AdminTariffPackage adminTariffPackage)
    {
        return adminTariffPackageMapper.selectAdminTariffPackageList(adminTariffPackage);
    }

    /**
     * 新增套餐
     * 
     * @param adminTariffPackage 套餐
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertAdminTariffPackage(AdminTariffPackage adminTariffPackage)
    {
        //校验默认套餐只能有一个
        Integer x = getDefaultNum(adminTariffPackage);
        if (x != null) return x;
        //上架的套餐一共智能有4个
        if(adminTariffPackage.getOnSale() ==1){
            List<AdminTariffPackage> adminTariffPackages = adminTariffPackageMapper.selectOnSaleList();
            if(CollectionUtil.isNotEmpty(adminTariffPackages) && adminTariffPackages.size() > 3){
                return -2;
            }
        }

        Date nowDate = DateUtils.getNowDate();
        adminTariffPackage.setCreateTime(nowDate);
        //套餐ID
        String tariffPackageId = SnowFlakeUtils.nextIdStr();
        //保存描述和 权益
        //描述
        List<AdminEquityDescription> equityDescriptionList = adminTariffPackage.getEquityDescriptionList();
        if(CollectionUtil.isNotEmpty(equityDescriptionList)){
            equityDescriptionList.stream().forEach(item ->{
                item.setTariffId(tariffPackageId);
                item.setCreateTime(nowDate);
                adminEquityDescriptionMapper.insertAdminEquityDescription(item);
            });
        }
        // 权益
        List<TTariffPackageSub> tariffPackageSubList = adminTariffPackage.getTariffPackageSubList();
        if(CollectionUtil.isNotEmpty(tariffPackageSubList)){
            tariffPackageSubList.stream().forEach(item ->{
                item.setParentId(tariffPackageId);
                item.setCreateTime(nowDate);
                item.setTariffPackageSubId(SnowFlakeUtils.nextIdStr());
                tTariffPackageSubMapper.insertTTariffPackageSub(item);
            });
        }
        adminTariffPackage.setTariffPackageId(tariffPackageId);
        return adminTariffPackageMapper.insertAdminTariffPackage(adminTariffPackage);
    }

    @Nullable
    private Integer getDefaultNum(AdminTariffPackage adminTariffPackage) {
        Long isDefault = adminTariffPackage.getIsDefault();
        //新增
        if(1== isDefault){
            List<AdminTariffPackage> adminTariffPackages = adminTariffPackageMapper.selectIsDefaultCount();
            if(CollectionUtil.isNotEmpty(adminTariffPackages)){
                //新增
                if(adminTariffPackage.getTariffPackageId() == null){
                    if(adminTariffPackages.size() >0 && adminTariffPackage.getOnSale() == 1){
                        return -1;
                    }
                }
                //更新
                else {
                    //需要更新的套餐是默认套餐，并且准备更新为上架状态,并且需要更新的套餐不是当前已经上架的套餐，
                    // 则不允许将需要更新的默认套餐更新为上架状态，
                    // 因为已经存在了一个上架的默认套餐了
                    AdminTariffPackage adminTariffPackage1 = adminTariffPackages.get(0);
                    if(!adminTariffPackage1.getTariffPackageId().equals(adminTariffPackage.getTariffPackageId())){
                        if(adminTariffPackage.getOnSale() == 1 ){
                            return -1;
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 修改套餐
     * 
     * @param adminTariffPackage 套餐
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateAdminTariffPackage(AdminTariffPackage adminTariffPackage)
    {
        //校验默认套餐只能有一个
        Integer num = getDefaultNum(adminTariffPackage);
        if (num != null){
            return num;
        }
        //上架的套餐一共智能有4个
        if(adminTariffPackage.getOnSale() ==1){
            List<AdminTariffPackage> adminTariffPackages = adminTariffPackageMapper.selectOnSaleList();
            //需要更新的套餐ID
            String tariffPackageId = adminTariffPackage.getTariffPackageId();
            List<AdminTariffPackage> collect = adminTariffPackages.stream().filter(obj -> obj.getTariffPackageId().equals(tariffPackageId)).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(collect)){
                if(CollectionUtil.isNotEmpty(adminTariffPackages) && adminTariffPackages.size() > 4){
                    return -2;
                }
            }else {
                if(CollectionUtil.isNotEmpty(adminTariffPackages) && adminTariffPackages.size() > 3){
                    return -2;
                }
            }
        }

        Date nowDate = DateUtils.getNowDate();
        adminTariffPackage.setUpdateTime(nowDate);
        //更新描述和权益
        List<AdminEquityDescription> equityDescriptionList = adminTariffPackage.getEquityDescriptionList();
        if(CollectionUtil.isNotEmpty(equityDescriptionList)){
            equityDescriptionList.stream().forEach(item ->{
                //更新的
                if(StringUtils.isNotEmpty(item.getTariffId())){
                    item.setUpdateTime(nowDate);
                    adminEquityDescriptionMapper.updateAdminEquityDescription(item);
                }
                //插入的
                else {
                    item.setCreateTime(nowDate);
                    item.setTariffId(adminTariffPackage.getTariffPackageId());
                    adminEquityDescriptionMapper.insertAdminEquityDescription(item);
                }
            });
        }
        List<TTariffPackageSub> tariffPackageSubList = adminTariffPackage.getTariffPackageSubList();
        if(CollectionUtil.isNotEmpty(tariffPackageSubList)){
            tariffPackageSubList.stream().forEach(item ->{
                //更新的
                if(StringUtils.isNotEmpty(item.getParentId())){
                    item.setUpdateTime(nowDate);
                    tTariffPackageSubMapper.updateTTariffPackageSub(item);
                }
                //插入的
                else {
                    item.setTariffPackageSubId(SnowFlakeUtils.nextIdStr());
                    item.setCreateTime(nowDate);
                    item.setParentId(adminTariffPackage.getTariffPackageId());
                    tTariffPackageSubMapper.insertTTariffPackageSub(item);
                }

            });
        }
        return adminTariffPackageMapper.updateAdminTariffPackage(adminTariffPackage);
    }

    /**
     * 批量删除套餐
     * 
     * @param tariffPackageIds 需要删除的套餐主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAdminTariffPackageByTariffPackageIds(String[] tariffPackageIds)
    {
        //先删除 描述和权益
        for (String id: tariffPackageIds){
            adminEquityDescriptionMapper.deleteAdminEquityDescriptionByTariffId(id);
            tTariffPackageSubMapper.deleteTTariffPackageSubByTariffPackageId(id);
        }
        return adminTariffPackageMapper.deleteAdminTariffPackageByTariffPackageIds(tariffPackageIds);
    }

    /**
     * 删除套餐信息
     * 
     * @param tariffPackageId 套餐主键
     * @return 结果
     */
    @Override
    public int deleteAdminTariffPackageByTariffPackageId(String tariffPackageId)
    {
        return adminTariffPackageMapper.deleteAdminTariffPackageByTariffPackageId(tariffPackageId);
    }
}
