package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.WebGoodsDic;

/**
 * 商品的咒语词典Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
public interface IWebGoodsDicService 
{
    /**
     * 查询商品的咒语词典
     * 
     * @param id 商品的咒语词典主键
     * @return 商品的咒语词典
     */
    public WebGoodsDic selectWebGoodsDicById(Long id);

    /**
     * 查询商品的咒语词典列表
     * 
     * @param webGoodsDic 商品的咒语词典
     * @return 商品的咒语词典集合
     */
    public List<WebGoodsDic> selectWebGoodsDicList(WebGoodsDic webGoodsDic);

    /**
     * 查询商品的咒语词典分类列表
     *
     * @param webGoodsDic 商品的咒语词典
     * @return 商品的咒语词典集合
     */
    public List<WebGoodsDic> selectWebGoodsCategoryDicList(WebGoodsDic webGoodsDic);

    /**
     * 新增商品的咒语词典
     * 
     * @param webGoodsDic 商品的咒语词典
     * @return 结果
     */
    public int insertWebGoodsDic(WebGoodsDic webGoodsDic);

    /**
     * 修改商品的咒语词典
     * 
     * @param webGoodsDic 商品的咒语词典
     * @return 结果
     */
    public int updateWebGoodsDic(WebGoodsDic webGoodsDic);

    /**
     * 批量删除商品的咒语词典
     * 
     * @param ids 需要删除的商品的咒语词典主键集合
     * @return 结果
     */
    public int deleteWebGoodsDicByIds(Long[] ids);

    /**
     * 删除商品的咒语词典信息
     * 
     * @param id 商品的咒语词典主键
     * @return 结果
     */
    public int deleteWebGoodsDicById(Long id);
}
