package com.dataxai.web.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.domain.*;
import com.dataxai.web.mapper.TTagMapper;
import org.jetbrains.annotations.TestOnly;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.TArticleMapper;
import com.dataxai.web.service.ITArticleService;

/**
 * 文章Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@Service
public class TArticleServiceImpl implements ITArticleService
{
    @Autowired
    private TArticleMapper tArticleMapper;

    @Autowired
    private TTagMapper tTagMapper;

    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    @Override
    public TArticle selectTArticleById(Long id)
    {
        TArticle tArticle = tArticleMapper.selectTArticleById(id);
        String tagIds = tArticle.getTagIds();
        String[] split = tagIds.split(",");
        List<TTagDto> tTagList = new ArrayList<>();
        if(split.length >0){
            for (int i = 0; i < split.length; i++) {
                TTag tTag = tTagMapper.selectTTagById(Long.valueOf(split[i]));
                TTagDto tTagDto = new TTagDto();
                tTagDto.setId(tTag.getId());
                tTagDto.setTagName(tTag.getTagName());
                tTagList.add(tTagDto);
            }
            tArticle.setTagList(tTagList);
        }
        return tArticle;
    }

    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    @Override
    public TArticleInfoDto selectWebTArticleById(Long id)
    {
        TArticle tArticle = tArticleMapper.selectTArticleById(id);
        TArticleInfoDto tArticleDto = new TArticleInfoDto();
        BeanUtils.copyProperties(tArticle,tArticleDto);

        String tagIds = tArticleDto.getTagIds();
        String[] split = tagIds.split(",");
        List<TTagDto> tTagList = new ArrayList<>();
        if(split.length >0){
            for (int i = 0; i < split.length; i++) {
                TTag tTag = tTagMapper.selectTTagById(Long.valueOf(split[i]));
                if(null != tTag){
                    TTagDto tTagDto = new TTagDto();
                    tTagDto.setId(tTag.getId());
                    tTagDto.setTagName(tTag.getTagName());
                    tTagList.add(tTagDto);
                }
            }
            tArticleDto.setTagList(tTagList);
        }
        return tArticleDto;
    }

    /**
     * 查询文章列表
     *
     * @param tArticle 文章
     * @return 文章
     */
    @Override
    public List<TArticle> selectTArticleList(TArticle tArticle)
    {
        return tArticleMapper.selectTArticleList(tArticle);
    }

    @Override
    public List<TArticleDto> selectWebTArticleList(TArticle tArticle)
    {
        List<TArticleDto> tArticleDtos = tArticleMapper.selectWebTArticleList(tArticle);
        tArticleDtos.forEach(tArticleDto -> {
            String tagIds = tArticleDto.getTagIds();
            String[] split = tagIds.split(",");
            List<TTagDto> tTagList = new ArrayList<>();
            if(split.length >0){
                for (int i = 0; i < split.length; i++) {
                    TTag tTag = tTagMapper.selectTTagById(Long.valueOf(split[i]));
                    if(null != tTag){
                        TTagDto tTagDto = new TTagDto();
                        tTagDto.setId(tTag.getId());
                        tTagDto.setTagName(tTag.getTagName());
                        tTagList.add(tTagDto);
                    }
                }
                tArticleDto.setTagList(tTagList);
            }
        });
        return tArticleDtos;
    }


    /**
     * 新增文章
     *
     * @param tArticle 文章
     * @return 结果
     */
    @Override
    public int insertTArticle(TArticle tArticle)
    {
        tArticle.setCreateTime(DateUtils.getNowDate());
        return tArticleMapper.insertTArticle(tArticle);
    }

    /**
     * 修改文章
     *
     * @param tArticle 文章
     * @return 结果
     */
    @Override
    public int updateTArticle(TArticle tArticle)
    {
        tArticle.setUpdateTime(DateUtils.getNowDate());
        return tArticleMapper.updateTArticle(tArticle);
    }

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的文章主键
     * @return 结果
     */
    @Override
    public int deleteTArticleByIds(Long[] ids)
    {
        return tArticleMapper.deleteTArticleByIds(ids);
    }

    /**
     * 删除文章信息
     *
     * @param id 文章主键
     * @return 结果
     */
    @Override
    public int deleteTArticleById(Long id)
    {
        return tArticleMapper.deleteTArticleById(id);
    }
}
