package com.dataxai.web.utils;

import org.springframework.web.multipart.MultipartFile;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import net.coobird.thumbnailator.Thumbnails;
public class ThumbnailUtils {
    public static File generateThumbnail(MultipartFile originalFile) throws IOException {
        BufferedImage originalImage = ImageIO.read(originalFile.getInputStream());
        // 创建临时文件用于存储缩略图
        File thumbnailFile = File.createTempFile("thumbnail", ".webp");
        Thumbnails.of(originalImage)
                .size(200, 200)
                .toFile(thumbnailFile);
        return thumbnailFile;
    }

    public static File generateThumbnail(MultipartFile originalFile,Integer width,Integer height) throws IOException {
        BufferedImage originalImage = ImageIO.read(originalFile.getInputStream());
        // 创建临时文件用于存储缩略图
        File thumbnailFile = File.createTempFile("thumbnail", ".webp");
        Thumbnails.of(originalImage)
                .size(width, height)
                .toFile(thumbnailFile);
        return thumbnailFile;
    }

    /**
     *
     * @param fileBytes 字节
     * @param size  缩略图尺寸
     * @return
     * @throws IOException
     */
    public static File generateThumbnail(byte[] fileBytes,Integer size) throws IOException {
        // 将字节数组转换为 BufferedImage
        BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(fileBytes));

        // 创建临时文件用于存储缩略图
        File thumbnailFile = File.createTempFile("thumbnail", ".webp");

        // 使用 Thumbnailator 生成缩略图并保存到临时文件
        Thumbnails.of(originalImage)
                .size(size, size) // 设置缩略图大小
                .outputFormat("webp") // 输出格式为 WebP
                .toFile(thumbnailFile);

        return thumbnailFile;
    }
}
