<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.OriResizeImgMapper">
    
    <resultMap type="OriResizeImg" id="OriResizeImgResult">
        <result property="oriUrl"    column="ori_url"    />
        <result property="resizeUrl"    column="resize_url"    />
    </resultMap>

    <sql id="selectOriResizeImgVo">
        select ori_url, resize_url from t_ori_resize_img
    </sql>

    <select id="selectOriResizeImgList" parameterType="OriResizeImg" resultMap="OriResizeImgResult">
        <include refid="selectOriResizeImgVo"/>
        <where>  
            <if test="resizeUrl != null  and resizeUrl != ''"> and resize_url = #{resizeUrl}</if>
        </where>
    </select>
    
    <select id="selectOriResizeImgByOriUrl" parameterType="String" resultMap="OriResizeImgResult">
        <include refid="selectOriResizeImgVo"/>
        where ori_url = #{oriUrl}
    </select>
        
    <insert id="insertOriResizeImg" parameterType="OriResizeImg">
        insert into t_ori_resize_img
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="oriUrl != null">ori_url,</if>
            <if test="resizeUrl != null">resize_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="oriUrl != null">#{oriUrl},</if>
            <if test="resizeUrl != null">#{resizeUrl},</if>
         </trim>
    </insert>

    <update id="updateOriResizeImg" parameterType="OriResizeImg">
        update t_ori_resize_img
        <trim prefix="SET" suffixOverrides=",">
            <if test="resizeUrl != null">resize_url = #{resizeUrl},</if>
        </trim>
        where ori_url = #{oriUrl}
    </update>

    <delete id="deleteOriResizeImgByOriUrl" parameterType="String">
        delete from t_ori_resize_img where ori_url = #{oriUrl}
    </delete>

    <delete id="deleteOriResizeImgByOriUrls" parameterType="String">
        delete from t_ori_resize_img where ori_url in 
        <foreach item="oriUrl" collection="array" open="(" separator="," close=")">
            #{oriUrl}
        </foreach>
    </delete>
</mapper>