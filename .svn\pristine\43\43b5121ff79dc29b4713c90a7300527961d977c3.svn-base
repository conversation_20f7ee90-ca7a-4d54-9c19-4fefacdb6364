package com.dataxai.web.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 商品场景信息对象 Web_scen_goods_category_sub
 * 
 * <AUTHOR>
 * @date 2024-01-19
 */
public class WebScenGoodsCategorySub extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    @ApiModelProperty(value = "场景子类id")
    private Long id;

    /** 大类 */
    @Excel(name = "大类")
    @ApiModelProperty(value = "场景大类")
    private String category;

    /** 小类 */
    @Excel(name = "小类")
    @ApiModelProperty(value = "场景小类")
    private String categorySub;

    /** 正向关键词 */
    @Excel(name = "正向关键词")
    @ApiModelProperty(value = "场景小类所代表的信息")
    private String prompt;

    /** 图片练接 */
    @Excel(name = "图片练接")
    @ApiModelProperty(value = "场景小类图片练接")
    private String image;

    /** 排序字段 */
    @Excel(name = "排序字段")
    private Integer sort;

    /** 是否有效 */
    @Excel(name = "是否有效")
    @JsonIgnore
    private Integer isVaild;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    public void setCategorySub(String categorySub) 
    {
        this.categorySub = categorySub;
    }

    public String getCategorySub() 
    {
        return categorySub;
    }
    public void setPrompt(String prompt) 
    {
        this.prompt = prompt;
    }

    public String getPrompt() 
    {
        return prompt;
    }
    public void setImage(String image) 
    {
        this.image = image;
    }

    public String getImage() 
    {
        return image;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    public void setIsVaild(Integer isVaild) 
    {
        this.isVaild = isVaild;
    }

    public Integer getIsVaild() 
    {
        return isVaild;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("category", getCategory())
            .append("categorySub", getCategorySub())
            .append("prompt", getPrompt())
            .append("image", getImage())
            .append("isVaild", getIsVaild())
            .toString();
    }
}
