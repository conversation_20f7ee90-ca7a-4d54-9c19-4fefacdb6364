import request from '@/utils/request'

// 查询订单列表
export function listAdminorder(query) {
  return request({
    url: '/adminorder/adminorder/list',
    method: 'get',
    params: query
  })
}

// 查询订单详细
export function getAdminorder(orderId) {
  return request({
    url: '/adminorder/adminorder/' + orderId,
    method: 'get'
  })
}

// 新增订单
export function addAdminorder(data) {
  return request({
    url: '/adminorder/adminorder',
    method: 'post',
    data: data
  })
}

// 修改订单
export function updateAdminorder(data) {
  return request({
    url: '/adminorder/adminorder',
    method: 'put',
    data: data
  })
}

// 删除订单
export function delAdminorder(orderId) {
  return request({
    url: '/adminorder/adminorder/' + orderId,
    method: 'delete'
  })
}
