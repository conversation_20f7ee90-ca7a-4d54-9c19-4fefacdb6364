package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.WebModelDic;

/**
 * 模特的咒语词典Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
public interface WebModelDicMapper 
{
    /**
     * 查询模特的咒语词典
     * 
     * @param id 模特的咒语词典主键
     * @return 模特的咒语词典
     */
    public WebModelDic selectWebModelDicById(Long id);

    /**
     * 查询模特的咒语词典列表
     * 
     * @param webModelDic 模特的咒语词典
     * @return 模特的咒语词典集合
     */
    public List<WebModelDic> selectWebModelDicList(WebModelDic webModelDic);

    /**
     * 查询模特的咒语词典分类列表
     *
     * @param webModelDic 模特的咒语词典
     * @return 模特的咒语词典集合
     */
    public List<WebModelDic> selectWebModelCategoryDicList(WebModelDic webModelDic);

    /**
     * 新增模特的咒语词典
     * 
     * @param webModelDic 模特的咒语词典
     * @return 结果
     */
    public int insertWebModelDic(WebModelDic webModelDic);

    /**
     * 修改模特的咒语词典
     * 
     * @param webModelDic 模特的咒语词典
     * @return 结果
     */
    public int updateWebModelDic(WebModelDic webModelDic);

    /**
     * 删除模特的咒语词典
     * 
     * @param id 模特的咒语词典主键
     * @return 结果
     */
    public int deleteWebModelDicById(Long id);

    /**
     * 批量删除模特的咒语词典
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWebModelDicByIds(Long[] ids);
}
