package com.dataxai.web.controller.admincontroller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.AdminRefuelingBag;
import com.dataxai.web.service.IAdminRefuelingBagService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 加油包Controller
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@RestController
@RequestMapping("/adminrefueling/adminbag")
public class AdminRefuelingBagController extends BaseController
{
    @Autowired
    private IAdminRefuelingBagService adminRefuelingBagService;

    /**
     * 查询加油包列表
     */
    @PreAuthorize("@ss.hasPermi('adminrefueling:adminbag:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdminRefuelingBag adminRefuelingBag)
    {
        startPage();
        List<AdminRefuelingBag> list = adminRefuelingBagService.selectAdminRefuelingBagList(adminRefuelingBag);
        return getDataTable(list);
    }

    /**
     * 导出加油包列表
     */
    @PreAuthorize("@ss.hasPermi('adminrefueling:adminbag:export')")
    @Log(title = "加油包", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdminRefuelingBag adminRefuelingBag)
    {
        List<AdminRefuelingBag> list = adminRefuelingBagService.selectAdminRefuelingBagList(adminRefuelingBag);
        ExcelUtil<AdminRefuelingBag> util = new ExcelUtil<AdminRefuelingBag>(AdminRefuelingBag.class);
        util.exportExcel(response, list, "加油包数据");
    }

    /**
     * 获取加油包详细信息
     */
    @PreAuthorize("@ss.hasPermi('adminrefueling:adminbag:query')")
    @GetMapping(value = "/{refuelingBagId}")
    public AjaxResult getInfo(@PathVariable("refuelingBagId") String refuelingBagId)
    {
        return success(adminRefuelingBagService.selectAdminRefuelingBagByRefuelingBagId(refuelingBagId));
    }

    /**
     * 新增加油包
     */
    @PreAuthorize("@ss.hasPermi('adminrefueling:adminbag:add')")
    @Log(title = "加油包", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AdminRefuelingBag adminRefuelingBag)
    {
        int result = adminRefuelingBagService.insertAdminRefuelingBag(adminRefuelingBag);
        if(result == -1){
            return AjaxResult.error("加油包新增失败，上架的加油包只能有4个");
        }
        return toAjax(result);
    }

    /**
     * 修改加油包
     */
    @PreAuthorize("@ss.hasPermi('adminrefueling:adminbag:edit')")
    @Log(title = "加油包", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AdminRefuelingBag adminRefuelingBag)
    {
        int result = adminRefuelingBagService.updateAdminRefuelingBag(adminRefuelingBag);
        if(result == -1){
            return AjaxResult.error("加油包更新失败，上架的加油包只能有4个");
        }
        return toAjax(result);
    }

    /**
     * 删除加油包
     */
    @PreAuthorize("@ss.hasPermi('adminrefueling:adminbag:remove')")
    @Log(title = "加油包", businessType = BusinessType.DELETE)
	@DeleteMapping("/{refuelingBagIds}")
    public AjaxResult remove(@PathVariable String[] refuelingBagIds)
    {
        return toAjax(adminRefuelingBagService.deleteAdminRefuelingBagByRefuelingBagIds(refuelingBagIds));
    }
}
