/**
 * Copyright &copy; 2021-2026 <a href="http://www.jeeplus.org/">JeePlus</a> All rights reserved.
 */
package com.dataxai;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.dataxai.common.utils.html.EscapeUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.net.InetAddress;

@Component
public class AppInit implements CommandLineRunner {
    @Value("${server.port}")
    private String port;

    @Value("${server.servlet.context-path}")
    private String path;

    @Value("${spring.application.name}")
    private String application;

    @Value("${mybatis-plus.global-config.work-id}")
    private Long workId;

    @Value("${mybatis-plus.global-config.datacenter-id}")
    private Long datacenterId;

    @Override
    public void run(String... args) throws Exception {
        System.out.println(">>>>>>>>>>>>>>>" + application + " 启动成功<<<<<<<<<<<<<");
        String ip = InetAddress.getLocalHost().getHostAddress();
        System.out.println(application + " running at:");
        System.out.println("- Local: http://localhost:" + port + path + (path.endsWith("/") ? "" : "/"));
        System.out.println("- Network: http://" + ip + ":" + port + path + (path.endsWith("/") ? "" : "/"));
        System.out.println("- swagger: http://" + ip + ":" + port + path + (path.endsWith("/") ? "" : "/") + "swagger-ui/index.html");
        System.out.println("----------------------------------------------------------");

        System.out.println("workId = "+workId+"  datacenterId = "+datacenterId);
        IdWorker.initSequence(workId,datacenterId);
    }


}

