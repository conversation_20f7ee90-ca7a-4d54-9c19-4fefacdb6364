package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.UserRefueligBag;
import org.apache.ibatis.annotations.Param;

/**
 * 用户购买的加油包相关信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-23
 */
public interface UserRefueligBagMapper 
{
    /**
     * 查询用户购买的加油包相关信息
     * 
     * @param userRefuelingBagId 用户购买的加油包相关信息主键
     * @return 用户购买的加油包相关信息
     */
    public UserRefueligBag selectUserRefueligBagByUserRefuelingBagId(String userRefuelingBagId);

    /**
     * 查询用户购买的加油包相关信息列表
     * 
     * @param userRefueligBag 用户购买的加油包相关信息
     * @return 用户购买的加油包相关信息集合
     */
    public List<UserRefueligBag> selectUserRefueligBagList(UserRefueligBag userRefueligBag);

    /**
     * 根据userID查询
     * @param userRefueligBag
     * @return
     */
    public List<UserRefueligBag> selectUserRefueligBagListByUserId(UserRefueligBag userRefueligBag);

    /**
     * 新增用户购买的加油包相关信息
     * 
     * @param userRefueligBag 用户购买的加油包相关信息
     * @return 结果
     */
    public int insertUserRefueligBag(UserRefueligBag userRefueligBag);

    /**
     * 扣减积分
     * @param refuelingBagScore 扣减的积分数量
     * @param userRefuelingBagId 用户、加油包关系表ID
     * @return
     */
    int updateUserRefueligBagScore(@Param("refuelingBagScore") long refuelingBagScore, @Param("userRefuelingBagId") String userRefuelingBagId);

    /**
     * 修改用户购买的加油包相关信息
     * 
     * @param userRefueligBag 用户购买的加油包相关信息
     * @return 结果
     */
    public int updateUserRefueligBag(UserRefueligBag userRefueligBag);

    /**
     * 删除用户购买的加油包相关信息
     * 
     * @param userRefuelingBagId 用户购买的加油包相关信息主键
     * @return 结果
     */
    public int deleteUserRefueligBagByUserRefuelingBagId(String userRefuelingBagId);

    /**
     * 批量删除用户购买的加油包相关信息
     * 
     * @param userRefuelingBagIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserRefueligBagByUserRefuelingBagIds(String[] userRefuelingBagIds);
}
