package com.dataxai.web.controller.ImageController;


import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.PageDomain;
import com.dataxai.common.core.page.TableSupport;
import com.dataxai.common.utils.sql.SqlUtil;
import com.dataxai.web.domain.CustomImage;
import com.dataxai.web.domain.Style;
import com.dataxai.web.service.StyleService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/image/style")
@Api(tags = { "自定义图片相关接口" })
public class StyleController {


    @Autowired
    private StyleService styleService;

    /**
     * 根据风格设置分页
     */
    @PostMapping("/byPage")
    @ApiOperation("根据type查询风格图片分页")
    public R<HashMap<String,Object>> byPage(@RequestBody Style dto) {
        //startPage();
        PageDomain pageDomain = TableSupport.buildPageRequest();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        Integer pageNumber = dto.getPageNumber();
        Integer pageSize = dto.getPageSize();
        PageHelper.startPage(pageNumber, pageSize, orderBy).setReasonable(reasonable);
        List<Style> list =  styleService.selectByUserId(dto);
        HashMap<String,Object> dataMap = new HashMap<>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total",total);
        long maxPageSize = total /dto.getPageSize() + (total % dto.getPageSize()== 0 ? 0 : 1);
        if(dto.getPageNumber() > maxPageSize){
            list.clear();
        }
        dataMap.put("data",list);
        //根据type查询默认风格
        Style defStyle = styleService.selectDefault(dto.getType());
        dataMap.put("default",defStyle);

        return R.ok(dataMap);
    }

    /**
     * 智能联想
     */
    @GetMapping("/association")
    @ApiOperation("智能联想")
    public R<String> association(@RequestParam("description") String description,@RequestParam("type")Integer type) {

        String string = styleService.association(description,type);

        return R.ok(string);
    }

    /**
     * 识图转文
     */
    @GetMapping("/readPicture")
    @ApiOperation("识图转文")
    public R<String> readPicture(@RequestParam("url") String url,@RequestParam("type")Integer type) {
        return R.ok(styleService.readPicture(url,type));
    }



}
