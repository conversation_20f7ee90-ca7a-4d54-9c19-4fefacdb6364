package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 预设模板-模特信息分类对象 Web_character_model
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel
public class WebCharacterModelCategory implements Serializable
{
    private static final long serialVersionUID = 1L;

    public List<String> getGender() {
        return gender;
    }

    public void setGender(List<String> gender) {
        this.gender = gender;
    }

    public List<String> getAge() {
        return age;
    }

    public void setAge(List<String> age) {
        this.age = age;
    }

    public List<String> getSkin() {
        return skin;
    }

    public void setSkin(List<String> skin) {
        this.skin = skin;
    }

    private List<String> gender;
   private List<String> age;
   private List<String> skin;

    public List<String> getExpression() {
        return expression;
    }

    public void setExpression(List<String> expression) {
        this.expression = expression;
    }

    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private List<String> expression;

    public List<String> getBigSize() {
        return bigSize;
    }

    public void setBigSize(List<String> bigSize) {
        this.bigSize = bigSize;
    }

    public List<String> getHairStyles() {
        return hairStyles;
    }

    public void setHairStyles(List<String> hairStyles) {
        this.hairStyles = hairStyles;
    }
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private List<String> bigSize;
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private List<String> hairStyles;
}
