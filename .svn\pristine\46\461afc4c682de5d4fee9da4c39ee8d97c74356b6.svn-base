package com.dataxai.web.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Data
public class BatchDTO {

    /**
     * 原始图片地址
     */
    @ApiModelProperty(value = "原始图片地址逗号拼接")
    private String imgUrl;

//    /**
//     * 原始图片文件 类型为上传图片 15时操作
//     */
//    @ApiModelProperty(value = "原始图片文件 类型=15时操作")
//    private List<MultipartFile> imgFile;

    @ApiModelProperty(value = "type")
    private Long type;

//    @ApiModelProperty(value = "excel表格地址")
//    private MultipartFile table;

    /**
     * 备注信息
     */
    @ApiModelProperty(  value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "其他参数使用Json格式保存")
    private String taskParam;

}
