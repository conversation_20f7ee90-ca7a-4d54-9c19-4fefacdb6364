package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.WebScenRealHuman;
import com.dataxai.web.domain.WebScenRealHumanCategory;
import org.apache.ibatis.annotations.Param;

/**
 * 真人-人台场景信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public interface WebScenRealHumanMapper 
{
    /**
     * 查询真人-人台场景信息
     * 
     * @param id 真人-人台场景信息主键
     * @return 真人-人台场景信息
     */
    public WebScenRealHuman selectWebScenRealHumanById(Long id);

    /**
     * 查询真人-人台场景信息列表
     * 
     * @param webScenRealHuman 真人-人台场景信息
     * @return 真人-人台场景信息集合
     */
    public List<WebScenRealHuman> selectWebScenRealHumanList(WebScenRealHuman webScenRealHuman);

    /**
     * 新增真人-人台场景信息
     * 
     * @param webScenRealHuman 真人-人台场景信息
     * @return 结果
     */
    public int insertWebScenRealHuman(WebScenRealHuman webScenRealHuman);

    /**
     * 修改真人-人台场景信息
     * 
     * @param webScenRealHuman 真人-人台场景信息
     * @return 结果
     */
    public int updateWebScenRealHuman(WebScenRealHuman webScenRealHuman);

    /**
     * 删除真人-人台场景信息
     * 
     * @param id 真人-人台场景信息主键
     * @return 结果
     */
    public int deleteWebScenRealHumanById(Long id);

    /**
     * 批量删除真人-人台场景信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWebScenRealHumanByIds(Long[] ids);
    List<String> selectWebScenRealHumanCategoryLarge();

    int selectTotalCountBelow(@Param("largeCategory") String largeCategory, @Param("id") Long id);
}
