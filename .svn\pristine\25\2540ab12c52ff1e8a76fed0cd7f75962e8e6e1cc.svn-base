package com.dataxai.web.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.WebModelCharacterControlMapper;
import com.dataxai.web.domain.WebModelCharacterControl;
import com.dataxai.web.service.IWebModelCharacterControlService;

/**
 * 模特特征的可选项Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-11
 */
@Service
public class WebModelCharacterControlServiceImpl implements IWebModelCharacterControlService 
{
    @Autowired
    private WebModelCharacterControlMapper webModelCharacterControlMapper;

    /**
     * 查询模特特征的可选项
     * 
     * @param id 模特特征的可选项主键
     * @return 模特特征的可选项
     */
    @Override
    public WebModelCharacterControl selectWebModelCharacterControlById(Long id)
    {
        return webModelCharacterControlMapper.selectWebModelCharacterControlById(id);
    }

    /**
     * 查询模特特征的可选项列表
     * 
     * @param webModelCharacterControl 模特特征的可选项
     * @return 模特特征的可选项
     */
    @Override
    public List<WebModelCharacterControl> selectWebModelCharacterControlList(WebModelCharacterControl webModelCharacterControl)
    {
        return webModelCharacterControlMapper.selectWebModelCharacterControlList(webModelCharacterControl);
    }

    /**
     * 新增模特特征的可选项
     * 
     * @param webModelCharacterControl 模特特征的可选项
     * @return 结果
     */
    @Override
    public int insertWebModelCharacterControl(WebModelCharacterControl webModelCharacterControl)
    {
        return webModelCharacterControlMapper.insertWebModelCharacterControl(webModelCharacterControl);
    }

    /**
     * 修改模特特征的可选项
     * 
     * @param webModelCharacterControl 模特特征的可选项
     * @return 结果
     */
    @Override
    public int updateWebModelCharacterControl(WebModelCharacterControl webModelCharacterControl)
    {
        return webModelCharacterControlMapper.updateWebModelCharacterControl(webModelCharacterControl);
    }

    /**
     * 批量删除模特特征的可选项
     * 
     * @param ids 需要删除的模特特征的可选项主键
     * @return 结果
     */
    @Override
    public int deleteWebModelCharacterControlByIds(Long[] ids)
    {
        return webModelCharacterControlMapper.deleteWebModelCharacterControlByIds(ids);
    }

    /**
     * 删除模特特征的可选项信息
     * 
     * @param id 模特特征的可选项主键
     * @return 结果
     */
    @Override
    public int deleteWebModelCharacterControlById(Long id)
    {
        return webModelCharacterControlMapper.deleteWebModelCharacterControlById(id);
    }

    @Override
    public List<WebModelCharacterControl> selectWebModelCharacterControlDistinctList()
    {
        return webModelCharacterControlMapper.selectWebModelCharacterControlDistinctList();
    }
}
