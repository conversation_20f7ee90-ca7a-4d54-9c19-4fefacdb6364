package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.WebSegProcess;

/**
 * 切割图的进程Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-20
 */
public interface WebSegProcessMapper 
{
    /**
     * 查询切割图的进程
     * 
     * @param taskId 切割图的进程主键
     * @return 切割图的进程
     */
    public WebSegProcess selectWebSegProcessByTaskId(Long taskId);

    /**
     * 查询切割图的进程列表
     * 
     * @param webSegProcess 切割图的进程
     * @return 切割图的进程集合
     */
    public List<WebSegProcess> selectWebSegProcessList(WebSegProcess webSegProcess);

    /**
     * 新增切割图的进程
     * 
     * @param webSegProcess 切割图的进程
     * @return 结果
     */
    public int insertWebSegProcess(WebSegProcess webSegProcess);

    /**
     * 修改切割图的进程
     * 
     * @param webSegProcess 切割图的进程
     * @return 结果
     */
    public int updateWebSegProcess(WebSegProcess webSegProcess);

    /**
     * 删除切割图的进程
     * 
     * @param taskId 切割图的进程主键
     * @return 结果
     */
    public int deleteWebSegProcessByTaskId(Long taskId);

    /**
     * 批量删除切割图的进程
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWebSegProcessByTaskIds(Long[] taskIds);
}
