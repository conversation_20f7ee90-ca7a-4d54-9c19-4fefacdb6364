package com.dataxai.web.controller.front;

import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.model.RegisterBody;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.framework.web.service.SysRegisterService;
import com.dataxai.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 前端注册验证
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/front")
public class RegisterController extends BaseController
{
    @Autowired
    private SysRegisterService registerService;

    @Autowired
    private ISysConfigService configService;

    @PostMapping("/register")
    public AjaxResult register(@RequestBody RegisterBody user)
    {
        String msg = registerService.register(user);
        return StringUtils.isEmpty(msg) ? success() : error(msg);
    }
}
