package com.dataxai.web.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.WebShortcutGoods;
import com.dataxai.web.mapper.WebShortcutGoodsMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.WebShortcutRecomMapper;
import com.dataxai.web.domain.WebShortcutRecom;
import com.dataxai.web.service.IWebShortcutRecomService;

/**
 * 快捷模板-推荐信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-13
 */
@Service
public class WebShortcutRecomServiceImpl implements IWebShortcutRecomService 
{
    @Autowired
    private WebShortcutRecomMapper webShortcutRecomMapper;

    @Autowired
    private WebShortcutGoodsMapper webShortcutGoodsMapper;

    /**
     * 查询快捷模板-推荐信息
     * 
     * @param id 快捷模板-推荐信息主键
     * @return 快捷模板-推荐信息
     */
    @Override
    public WebShortcutRecom selectWebShortcutRecomById(Long id)
    {
        return webShortcutRecomMapper.selectWebShortcutRecomById(id);
    }

    /**
     * 查询快捷模板-推荐信息列表
     *
     * @return 快捷模板-推荐信息
     */
    @Override
    public List<WebShortcutRecom> selectWebShortcutRecomListByFresh(int type) {
        if(type == Constants.TASK_TYPE_GOODS){
            List<WebShortcutGoods> webShortcutGoods = webShortcutGoodsMapper.selectWebShortcutGoodsListByFresh();
            return webShortcutGoods.stream().map(item->{
                WebShortcutRecom webShortcutRecom = new WebShortcutRecom();
                BeanUtils.copyProperties(item,webShortcutRecom);
                return webShortcutRecom;
            }).collect(Collectors.toList());
        }
        return webShortcutRecomMapper.selectWebShortcutRecomListByFresh();
    }

    /**
     * 查询快捷模板-推荐信息列表
     * 
     * @param webShortcutRecom 快捷模板-推荐信息
     * @return 快捷模板-推荐信息
     */
    @Override
    public List<WebShortcutRecom> selectWebShortcutRecomList(WebShortcutRecom webShortcutRecom)
    {
        return webShortcutRecomMapper.selectWebShortcutRecomList(webShortcutRecom);
    }

    /**
     * 新增快捷模板-推荐信息
     * 
     * @param webShortcutRecom 快捷模板-推荐信息
     * @return 结果
     */
    @Override
    public int insertWebShortcutRecom(WebShortcutRecom webShortcutRecom)
    {
        return webShortcutRecomMapper.insertWebShortcutRecom(webShortcutRecom);
    }

    /**
     * 修改快捷模板-推荐信息
     * 
     * @param webShortcutRecom 快捷模板-推荐信息
     * @return 结果
     */
    @Override
    public int updateWebShortcutRecom(WebShortcutRecom webShortcutRecom)
    {
        return webShortcutRecomMapper.updateWebShortcutRecom(webShortcutRecom);
    }

    /**
     * 批量删除快捷模板-推荐信息
     * 
     * @param ids 需要删除的快捷模板-推荐信息主键
     * @return 结果
     */
    @Override
    public int deleteWebShortcutRecomByIds(Long[] ids)
    {
        return webShortcutRecomMapper.deleteWebShortcutRecomByIds(ids);
    }

    /**
     * 删除快捷模板-推荐信息信息
     * 
     * @param id 快捷模板-推荐信息主键
     * @return 结果
     */
    @Override
    public int deleteWebShortcutRecomById(Long id)
    {
        return webShortcutRecomMapper.deleteWebShortcutRecomById(id);
    }

}
