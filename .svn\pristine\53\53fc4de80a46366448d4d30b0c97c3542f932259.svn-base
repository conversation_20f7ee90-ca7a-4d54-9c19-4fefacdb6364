package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.UserPackage;

/**
 * 用户购买的套餐和加油包相关信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-22
 */
public interface IUserPackageService 
{
    /**
     * 查询用户购买的套餐和加油包相关信息
     * 
     * @param userPackageId 用户购买的套餐和加油包相关信息主键
     * @return 用户购买的套餐和加油包相关信息
     */
    public UserPackage selectUserPackageByUserPackageId(String userPackageId);

    /**
     * 查询用户购买的套餐和加油包相关信息列表
     * 
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 用户购买的套餐和加油包相关信息集合
     */
    public List<UserPackage> selectUserPackageList(UserPackage userPackage);

    /**
     * 新增用户购买的套餐和加油包相关信息
     * 
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 结果
     */
    public int insertUserPackage(UserPackage userPackage);

    /**
     * 修改用户购买的套餐和加油包相关信息
     * 
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 结果
     */
    public int updateUserPackage(UserPackage userPackage);

    /**
     * 批量删除用户购买的套餐和加油包相关信息
     * 
     * @param userPackageIds 需要删除的用户购买的套餐和加油包相关信息主键集合
     * @return 结果
     */
    public int deleteUserPackageByUserPackageIds(String[] userPackageIds);

    /**
     * 删除用户购买的套餐和加油包相关信息信息
     * 
     * @param userPackageId 用户购买的套餐和加油包相关信息主键
     * @return 结果
     */
    public int deleteUserPackageByUserPackageId(String userPackageId);

    /**
     * 修改用户购买的套餐和加油包相关信息
     *
     * @param userPackage 用户购买的套餐和加油包相关信息
     * @return 结果
     */
    public int updateUserPackageByUserId(UserPackage userPackage);
}
