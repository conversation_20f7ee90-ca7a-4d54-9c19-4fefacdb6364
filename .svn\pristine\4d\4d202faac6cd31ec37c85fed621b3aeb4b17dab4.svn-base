<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item
				label="类型"
				prop="name"
			>
				<el-input
					v-model="queryParams.name"
					placeholder="请输入类型"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item>
				<el-button
					type="primary"
					icon="el-icon-search"
					size="mini"
					@click="handleQuery"
					>搜索</el-button
				>
				<el-button
					icon="el-icon-refresh"
					size="mini"
					@click="resetQuery"
					>重置</el-button
				>
			</el-form-item>
		</el-form>

		<el-row
			:gutter="10"
			class="mb8"
		>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['baseinfo:manage:add']"
					type="primary"
					plain
					icon="el-icon-plus"
					size="mini"
					@click="handleAdd"
					>新增</el-button
				>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['baseinfo:manage:edit']"
					type="success"
					plain
					icon="el-icon-edit"
					size="mini"
					:disabled="single"
					@click="handleUpdate"
					>修改</el-button
				>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['baseinfo:manage:remove']"
					type="danger"
					plain
					icon="el-icon-delete"
					size="mini"
					:disabled="multiple"
					@click="handleDelete"
					>删除</el-button
				>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['baseinfo:manage:export']"
					type="warning"
					plain
					icon="el-icon-download"
					size="mini"
					@click="handleExport"
					>导出</el-button
				>
			</el-col>
			<right-toolbar
				:show-search.sync="showSearch"
				@queryTable="getList"
			></right-toolbar>
		</el-row>

		<el-table
			v-loading="loading"
			:data="manageList"
			@selection-change="handleSelectionChange"
		>
			<el-table-column
				type="selection"
				width="55"
				align="center"
			/>
			<el-table-column
				label="id"
				align="center"
				prop="id"
			/>
			<el-table-column
				label="类型"
				align="center"
				prop="name"
			/>
			<el-table-column
				label="详情"
				align="center"
				prop="data"
			/>
			<el-table-column
				label="操作"
				align="center"
				class-name="small-padding fixed-width"
			>
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['baseinfo:manage:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
						>修改</el-button
					>
					<el-button
						v-hasPermi="['baseinfo:manage:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					>
				</template>
			</el-table-column>
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改公司基础信息管理对话框 -->
		<el-dialog
			:title="title"
			:visible.sync="open"
			width="500px"
			append-to-body
		>
			<el-form
				ref="form"
				:model="form"
				:rules="rules"
				label-width="80px"
			>
				<el-form-item
					label="类型"
					prop="name"
				>
					<el-input
						v-model="form.name"
						placeholder="请输入类型"
					/>
				</el-form-item>
				<el-form-item
					label="详情"
					prop="data"
				>
					<el-input
						v-model="form.data"
						placeholder="请输入详情"
					/>
				</el-form-item>
			</el-form>
			<div
				slot="footer"
				class="dialog-footer"
			>
				<el-button
					type="primary"
					@click="submitForm"
					>确 定</el-button
				>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	listManage,
	getManage,
	delManage,
	addManage,
	updateManage
} from '@/api/baseinfo/manage'

export default {
	name: 'Manage',
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 公司基础信息管理表格数据
			manageList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				name: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {
				name: [{ required: true, message: '类型不能为空', trigger: 'blur' }],
				data: [{ required: true, message: '详情不能为空', trigger: 'blur' }]
			}
		}
	},
	created() {
		this.getList()
	},
	methods: {
		/** 查询公司基础信息管理列表 */
		getList() {
			this.loading = true
			listManage(this.queryParams).then((response) => {
				this.manageList = response.data
				this.total = response.total
				this.loading = false
			})
		},
		// 取消按钮
		cancel() {
			this.open = false
			this.reset()
		},
		// 表单重置
		reset() {
			this.form = {
				id: null,
				name: null,
				data: null
			}
			this.resetForm('form')
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.resetForm('queryForm')
			this.handleQuery()
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.id)
			this.single = selection.length !== 1
			this.multiple = !selection.length
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset()
			this.open = true
			this.title = '添加公司基础信息管理'
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const id = row.id || this.ids
			getManage(id).then((response) => {
				this.form = response.data
				this.open = true
				this.title = '修改公司基础信息管理'
			})
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.form.id != null) {
						updateManage(this.form).then((response) => {
							this.$modal.msgSuccess('修改成功')
							this.open = false
							this.getList()
						})
					} else {
						addManage(this.form).then((response) => {
							this.$modal.msgSuccess('新增成功')
							this.open = false
							this.getList()
						})
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const ids = row.id || this.ids
			this.$modal
				.confirm('是否确认删除公司基础信息管理编号为"' + ids + '"的数据项？')
				.then(function () {
					return delManage(ids)
				})
				.then(() => {
					this.getList()
					this.$modal.msgSuccess('删除成功')
				})
				.catch(() => {})
		},
		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'baseinfo/manage/export',
				{
					...this.queryParams
				},
				`manage_${new Date().getTime()}.xlsx`
			)
		}
	}
}
</script>
