package com.dataxai.web.controller.front;

import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.PageDomain;
import com.dataxai.common.core.page.TableSupport;
import com.dataxai.common.utils.sql.SqlUtil;
import com.dataxai.web.domain.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.service.IWebCharacterModelService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 预设模板-模特信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/character/model")
@Api(tags = "预设模板-模特信息")
public class WebCharacterModelController extends BaseController
{
    @Autowired
    private IWebCharacterModelService webCharacterModelService;

    /**
     * 查询预设模板-模特信息分类列表
     */
    @PostMapping("/pageNum")
    @ApiOperation(value = "根据模特特征信息，查询该条数据在第几页")
    public AjaxResult getPageNumByCharacterId(@RequestBody WebCharacterModelDTO webCharacterModelDTO)
    {
        int pageNum = webCharacterModelService.selectPageNumByCharacterId(webCharacterModelDTO);
        return success(pageNum);
    }


    /**
     * 查询预设模板-模特信息分类列表
     */
//    @PreAuthorize("@ss.hasPermi('character:model:list')")
    @GetMapping("/list/category")
    @ApiOperation(value = "查询预设模板-模特信息分类列表")
    public AjaxResult listCategory()
    {
        List<CharacterModelCategory> characterModelCategoryList = webCharacterModelService.selectWebCharacterModelCategoryList();
        return success(characterModelCategoryList);
    }

    /**
     * 查询预设模板-模特信息列表
     */
//    @PreAuthorize("@ss.hasPermi('character:model:list')")
    @PostMapping("/list")
    @ApiOperation(value = "查询预设模板-模特信息列表(换一批)")
    public R<HashMap<String,Object>> list(@RequestBody WebCharacterModel webCharacterModel)
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        int pageNum = webCharacterModel.getPageNum();
        int pageSize = webCharacterModel.getPageSize();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        webCharacterModel.setIsVaild(1L);
        List<WebCharacterModel> list = webCharacterModelService.selectWebCharacterModelList(webCharacterModel);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total", total);
        long maxPageSize = total/pageSize + (total%pageSize==0?0:1);
        if(pageNum>maxPageSize){
            list.clear();
        }
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    /**
     * 导出预设模板-模特信息列表
     */
//    @PreAuthorize("@ss.hasPermi('character:model:export')")
    @Log(title = "预设模板-模特信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WebCharacterModel webCharacterModel)
    {
        List<WebCharacterModel> list = webCharacterModelService.selectWebCharacterModelList(webCharacterModel);
        ExcelUtil<WebCharacterModel> util = new ExcelUtil<WebCharacterModel>(WebCharacterModel.class);
        util.exportExcel(response, list, "预设模板-模特信息数据");
    }

    /**
     * 获取预设模板-模特信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('character:model:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(webCharacterModelService.selectWebCharacterModelById(id));
    }

    /**
     * 新增预设模板-模特信息
     */
//    @PreAuthorize("@ss.hasPermi('character:model:add')")
    @Log(title = "预设模板-模特信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WebCharacterModel webCharacterModel)
    {
        return toAjax(webCharacterModelService.insertWebCharacterModel(webCharacterModel));
    }

    /**
     * 修改预设模板-模特信息
     */
//    @PreAuthorize("@ss.hasPermi('character:model:edit')")
    @Log(title = "预设模板-模特信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WebCharacterModel webCharacterModel)
    {
        return toAjax(webCharacterModelService.updateWebCharacterModel(webCharacterModel));
    }

    /**
     * 删除预设模板-模特信息
     */
//    @PreAuthorize("@ss.hasPermi('character:model:remove')")
    @Log(title = "预设模板-模特信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(webCharacterModelService.deleteWebCharacterModelByIds(ids));
    }
}
