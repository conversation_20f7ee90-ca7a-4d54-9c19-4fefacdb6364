package com.dataxai.web.domain;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 文章对象 t_article
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@Data
public class TArticleInfoDto
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 封面图 */
    private String img;

    /** 名称 */
    private String name;

    /** 简介 */
    private String summary;

    /** 标签 */
    private String tagIds;

    private List<TTagDto> tagList;

    /** 精选 */
    private Boolean selected;

    private Date createTime;

    private String content;
}
