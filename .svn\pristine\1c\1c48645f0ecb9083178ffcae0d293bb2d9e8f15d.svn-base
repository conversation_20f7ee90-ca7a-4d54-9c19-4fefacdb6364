package com.dataxai.web.mapper;

import java.util.Date;
import java.util.List;
import com.dataxai.web.domain.Order;
import org.apache.ibatis.annotations.Param;

/**
 * 订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-16
 */
public interface OrderMapper 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public Order selectOrderByOrderId(String orderId);
    /**
     * 查询订单列表
     * 
     * @param order 订单
     * @return 订单集合
     */
    public List<Order> selectOrderList(Order order);

    /**
     * 新增订单
     * 
     * @param order 订单
     * @return 结果
     */
    public int insertOrder(Order order);

    /**
     * 修改订单
     * 
     * @param order 订单
     * @return 结果
     */
    public int updateOrder(Order order);

    /**
     * 删除订单
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteOrderByOrderId(String orderId);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOrderByOrderIds(String[] orderIds);

    Order selectOrderByOrderNo(String orderNo);

    int selectOrderStatusByOrderNo(String orderNo);

    int updateOrderByOrderNo(@Param("orderNo") String orderNo, @Param("status") int status,@Param("updateTime") Date updateTime);
}
