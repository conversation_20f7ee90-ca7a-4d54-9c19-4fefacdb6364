package com.dataxai.web.service.impl;

import java.util.List;
import com.dataxai.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.AdminEquityDescriptionMapper;
import com.dataxai.web.domain.AdminEquityDescription;
import com.dataxai.web.service.IAdminEquityDescriptionService;

/**
 * 权益Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
public class AdminEquityDescriptionServiceImpl implements IAdminEquityDescriptionService 
{
    @Autowired
    private AdminEquityDescriptionMapper adminEquityDescriptionMapper;

    /**
     * 查询权益
     * 
     * @param id 权益主键
     * @return 权益
     */
    @Override
    public AdminEquityDescription selectAdminEquityDescriptionById(Long id)
    {
        return adminEquityDescriptionMapper.selectAdminEquityDescriptionById(id);
    }

    /**
     * 查询权益列表
     * 
     * @param adminEquityDescription 权益
     * @return 权益
     */
    @Override
    public List<AdminEquityDescription> selectAdminEquityDescriptionList(AdminEquityDescription adminEquityDescription)
    {
        return adminEquityDescriptionMapper.selectAdminEquityDescriptionList(adminEquityDescription);
    }

    /**
     * 新增权益
     * 
     * @param adminEquityDescription 权益
     * @return 结果
     */
    @Override
    public int insertAdminEquityDescription(AdminEquityDescription adminEquityDescription)
    {
        adminEquityDescription.setCreateTime(DateUtils.getNowDate());
        return adminEquityDescriptionMapper.insertAdminEquityDescription(adminEquityDescription);
    }

    /**
     * 修改权益
     * 
     * @param adminEquityDescription 权益
     * @return 结果
     */
    @Override
    public int updateAdminEquityDescription(AdminEquityDescription adminEquityDescription)
    {
        adminEquityDescription.setUpdateTime(DateUtils.getNowDate());
        return adminEquityDescriptionMapper.updateAdminEquityDescription(adminEquityDescription);
    }

    /**
     * 批量删除权益
     * 
     * @param ids 需要删除的权益主键
     * @return 结果
     */
    @Override
    public int deleteAdminEquityDescriptionByIds(Long[] ids)
    {
        return adminEquityDescriptionMapper.deleteAdminEquityDescriptionByIds(ids);
    }

    /**
     * 删除权益信息
     * 
     * @param id 权益主键
     * @return 结果
     */
    @Override
    public int deleteAdminEquityDescriptionById(Long id)
    {
        return adminEquityDescriptionMapper.deleteAdminEquityDescriptionById(id);
    }
}
