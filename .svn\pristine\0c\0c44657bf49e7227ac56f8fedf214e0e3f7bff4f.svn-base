package com.dataxai.web.controller.front;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.PaymentInfo;
import com.dataxai.web.service.IPaymentInfoService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 支付日志Controller
 * 
 * <AUTHOR>
 * @date 2024-02-25
 */
@RestController
@RequestMapping("/payment/info")
public class PaymentInfoController extends BaseController
{
    @Autowired
    private IPaymentInfoService paymentInfoService;

    /**
     * 查询支付日志列表
     */
    @PreAuthorize("@ss.hasPermi('payment:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(PaymentInfo paymentInfo)
    {
        startPage();
        List<PaymentInfo> list = paymentInfoService.selectPaymentInfoList(paymentInfo);
        return getDataTable(list);
    }

    /**
     * 导出支付日志列表
     */
    @PreAuthorize("@ss.hasPermi('payment:info:export')")
    @Log(title = "支付日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PaymentInfo paymentInfo)
    {
        List<PaymentInfo> list = paymentInfoService.selectPaymentInfoList(paymentInfo);
        ExcelUtil<PaymentInfo> util = new ExcelUtil<PaymentInfo>(PaymentInfo.class);
        util.exportExcel(response, list, "支付日志数据");
    }

    /**
     * 获取支付日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('payment:info:query')")
    @GetMapping(value = "/{paymentId}")
    public AjaxResult getInfo(@PathVariable("paymentId") String paymentId)
    {
        return success(paymentInfoService.selectPaymentInfoByPaymentId(paymentId));
    }

    /**
     * 新增支付日志
     */
    @PreAuthorize("@ss.hasPermi('payment:info:add')")
    @Log(title = "支付日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PaymentInfo paymentInfo)
    {
        return toAjax(paymentInfoService.insertPaymentInfo(paymentInfo));
    }

    /**
     * 修改支付日志
     */
    @PreAuthorize("@ss.hasPermi('payment:info:edit')")
    @Log(title = "支付日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PaymentInfo paymentInfo)
    {
        return toAjax(paymentInfoService.updatePaymentInfo(paymentInfo));
    }

    /**
     * 删除支付日志
     */
    @PreAuthorize("@ss.hasPermi('payment:info:remove')")
    @Log(title = "支付日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{paymentIds}")
    public AjaxResult remove(@PathVariable String[] paymentIds)
    {
        return toAjax(paymentInfoService.deletePaymentInfoByPaymentIds(paymentIds));
    }
}
