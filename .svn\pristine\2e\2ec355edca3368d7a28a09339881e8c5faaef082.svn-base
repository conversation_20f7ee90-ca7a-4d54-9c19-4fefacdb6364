package com.dataxai.web.mapper;

import com.dataxai.web.domain.TUserComment;

import java.util.List;

/**
 * 用户评论Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface TUserCommentMapper 
{
    /**
     * 查询用户评论
     * 
     * @param commentId 用户评论主键
     * @return 用户评论
     */
    public TUserComment selectTUserCommentByCommentId(String commentId);

    /**
     * 查询用户评论列表
     * 
     * @param tUserComment 用户评论
     * @return 用户评论集合
     */
    public List<TUserComment> selectTUserCommentList(TUserComment tUserComment);

    /**
     * 新增用户评论
     * 
     * @param tUserComment 用户评论
     * @return 结果
     */
    public int insertTUserComment(TUserComment tUserComment);

    /**
     * 修改用户评论
     * 
     * @param tUserComment 用户评论
     * @return 结果
     */
    public int updateTUserComment(TUserComment tUserComment);

    /**
     * 删除用户评论
     * 
     * @param commentId 用户评论主键
     * @return 结果
     */
    public int deleteTUserCommentByCommentId(String commentId);

    /**
     * 批量删除用户评论
     * 
     * @param commentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTUserCommentByCommentIds(String[] commentIds);
}
