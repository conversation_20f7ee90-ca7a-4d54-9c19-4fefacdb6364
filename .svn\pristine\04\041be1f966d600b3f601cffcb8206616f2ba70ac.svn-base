package com.dataxai.web.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 联系我们对象 t_baseinfo_contactus
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public class TBaseinfoContactus extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 联系我们 */
    @Excel(name = "联系我们")
    private String name;

    /** 图片地址 */
    @Excel(name = "图片地址")
    private String content;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("content", getContent())
            .toString();
    }
}
