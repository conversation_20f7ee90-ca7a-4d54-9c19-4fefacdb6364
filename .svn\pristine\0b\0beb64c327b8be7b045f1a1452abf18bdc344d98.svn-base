package com.dataxai.web.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.domain.Order;
import com.dataxai.web.service.IOrderService;
import com.dataxai.web.utils.OrderNoUtils;
import com.google.gson.Gson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.RefundInfoMapper;
import com.dataxai.web.domain.RefundInfo;
import com.dataxai.web.service.IRefundInfoService;

import javax.annotation.Resource;

/**
 * 退款记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-25
 */
@Service
public class RefundInfoServiceImpl implements IRefundInfoService 
{
    @Resource
    private IOrderService orderInfoService;
    @Autowired
    private RefundInfoMapper refundInfoMapper;

    /**
     * 查询退款记录
     * 
     * @param id 退款记录主键
     * @return 退款记录
     */
    @Override
    public RefundInfo selectRefundInfoById(String id)
    {
        return refundInfoMapper.selectRefundInfoById(id);
    }

    /**
     * 查询退款记录列表
     * 
     * @param refundInfo 退款记录
     * @return 退款记录
     */
    @Override
    public List<RefundInfo> selectRefundInfoList(RefundInfo refundInfo)
    {
        return refundInfoMapper.selectRefundInfoList(refundInfo);
    }

    /**
     * 新增退款记录
     * 
     * @param refundInfo 退款记录
     * @return 结果
     */
    @Override
    public int insertRefundInfo(RefundInfo refundInfo)
    {
        refundInfo.setCreateTime(DateUtils.getNowDate());
        return refundInfoMapper.insertRefundInfo(refundInfo);
    }

    /**
     * 修改退款记录
     * 
     * @param refundInfo 退款记录
     * @return 结果
     */
    @Override
    public int updateRefundInfo(RefundInfo refundInfo)
    {
        refundInfo.setUpdateTime(DateUtils.getNowDate());
        return refundInfoMapper.updateRefundInfo(refundInfo);
    }

    /**
     * 批量删除退款记录
     * 
     * @param ids 需要删除的退款记录主键
     * @return 结果
     */
    @Override
    public int deleteRefundInfoByIds(String[] ids)
    {
        return refundInfoMapper.deleteRefundInfoByIds(ids);
    }

    /**
     * 删除退款记录信息
     * 
     * @param id 退款记录主键
     * @return 结果
     */
    @Override
    public int deleteRefundInfoById(String id)
    {
        return refundInfoMapper.deleteRefundInfoById(id);
    }

    @Override
    public RefundInfo createRefundByOrderNo(String orderNo, String reason) {
        //根据订单号获取订单信息
        Order orderInfo = orderInfoService.getOrderByOrderNo(orderNo);

        //根据订单号生成退款订单
        RefundInfo refundInfo = new RefundInfo();
        refundInfo.setOrderNo(orderNo);//订单编号
        refundInfo.setRefundNo(OrderNoUtils.getRefundNo());//退款单编号
        refundInfo.setTotalFee(orderInfo.getMoney());//原订单金额(分)
        refundInfo.setRefund(orderInfo.getMoney());//退款金额(分)
        refundInfo.setReason(reason);//退款原因

        //保存退款订单
        refundInfoMapper.insertRefundInfo(refundInfo);

        return refundInfo;
    }

    @Override
    public void updateRefund(String content) {
        //将json字符串转换成Map
        Gson gson = new Gson();
        Map<String, String> resultMap = gson.fromJson(content, HashMap.class);

        //根据退款单编号修改退款单

        //设置要修改的字段
        RefundInfo refundInfo = new RefundInfo();

        refundInfo.setRefundNo(resultMap.get("out_refund_no"));

        refundInfo.setRefundId(resultMap.get("refund_id"));//微信支付退款单号

        //查询退款和申请退款中的返回参数
        if(resultMap.get("status") != null){
            refundInfo.setRefundStatus(Integer.parseInt(resultMap.get("status")));//退款状态
            refundInfo.setContentReturn(content);//将全部响应结果存入数据库的content字段
        }
        //退款回调中的回调参数
        if(resultMap.get("refund_status") != null){
            refundInfo.setRefundStatus(Integer.parseInt(resultMap.get("refund_status")));//退款状态
            refundInfo.setContentNotify(content);//将全部响应结果存入数据库的content字段
        }

        //更新退款单
        refundInfoMapper.updateRefundInfoByRefundNo(refundInfo);
    }
}
