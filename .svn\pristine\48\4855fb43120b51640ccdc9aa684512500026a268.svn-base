<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item
				label="手机号"
				prop="userPhone"
			>
				<el-input
					v-model="queryParams.userPhone"
					placeholder="请输入用户手机号"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item
				label="变更类型"
				prop="type"
			>
				<el-select
					v-model="queryParams.type"
					placeholder="请选择变更类型"
					clearable
				>
					<el-option
						v-for="dict in dict.type.score_type"
						:key="dict.value"
						:label="dict.label"
						:value="dict.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="变更时间">
				<el-date-picker
					v-model="daterangeCreateTime"
					style="width: 240px"
					value-format="yyyy-MM-dd"
					type="daterange"
					range-separator="-"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
				/>
			</el-form-item>
			<el-form-item>
				<el-button
					type="primary"
					icon="el-icon-search"
					size="mini"
					@click="handleQuery"
					>搜索</el-button
				>
				<el-button
					icon="el-icon-refresh"
					size="mini"
					@click="resetQuery"
					>重置</el-button
				>
			</el-form-item>
		</el-form>

		<el-row
			:gutter="10"
			class="mb8"
		>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['score:adminhistory:export']"
					type="warning"
					plain
					icon="el-icon-download"
					size="mini"
					@click="handleExport"
					>导出</el-button
				>
			</el-col>
			<right-toolbar
				:show-search.sync="showSearch"
				@queryTable="getList"
			/>
		</el-row>

		<el-table
			v-loading="loading"
			:data="adminhistoryList"
			@selection-change="handleSelectionChange"
		>
			<el-table-column
				type="selection"
				width="55"
				align="center"
			/>

			<el-table-column
				label="用户id"
				align="center"
				prop="userId"
			/>

			<el-table-column
				label="用户手机号"
				align="center"
				prop="userPhone"
			/>
			<el-table-column
				label="积分变更"
				align="center"
				prop="score"
			/>
			<el-table-column
				label="变更类型"
				align="center"
				prop="type"
			>
				<template slot-scope="scope">
					<dict-tag
						:options="dict.type.score_type"
						:value="scope.row.type"
					/>
				</template>
			</el-table-column>
			<el-table-column
				label="剩余积分"
				align="center"
				prop="remainScore"
			/>
			<el-table-column
				label="变更时间"
				align="center"
				prop="updateTime"
				width="180"
			>
				<template slot-scope="scope">
					<span>{{ scope.row.updateTime }}</span>
				</template>
			</el-table-column>
			<!-- <el-table-column
				label="操作"
				align="center"
				class-name="small-padding fixed-width"
			>
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['score:adminhistory:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
						>修改</el-button
					>
					<el-button
						v-hasPermi="['score:adminhistory:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					>
				</template>
			</el-table-column> -->
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改基本变更历史记录对话框 -->
		<el-dialog
			:title="title"
			:visible.sync="open"
			width="500px"
			append-to-body
		>
			<el-form
				ref="form"
				:model="form"
				:rules="rules"
				label-width="80px"
			>
				<el-form-item
					label="用户id"
					prop="userId"
				>
					<el-input
						v-model="form.userId"
						placeholder="请输入用户id"
					/>
				</el-form-item>
				<el-form-item
					label="用户昵称"
					prop="userNickName"
				>
					<el-input
						v-model="form.userNickName"
						placeholder="请输入用户昵称"
					/>
				</el-form-item>
				<el-form-item
					label="用户手机号"
					prop="userPhone"
				>
					<el-input
						v-model="form.userPhone"
						placeholder="请输入用户手机号"
					/>
				</el-form-item>
				<el-form-item
					label="积分变更"
					prop="score"
				>
					<el-input
						v-model="form.score"
						placeholder="请输入积分变更"
					/>
				</el-form-item>
				<el-form-item
					label="变更类型"
					prop="type"
				>
					<el-select
						v-model="form.type"
						placeholder="请选择变更类型"
					>
						<el-option
							v-for="dict in dict.type.score_type"
							:key="dict.value"
							:label="dict.label"
							:value="parseInt(dict.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="当前增加或减剩余积分"
					prop="remainScore"
				>
					<el-input
						v-model="form.remainScore"
						placeholder="请输入当前增加或减剩余积分"
					/>
				</el-form-item>
			</el-form>
			<div
				slot="footer"
				class="dialog-footer"
			>
				<el-button
					type="primary"
					@click="submitForm"
					>确 定</el-button
				>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	listAdminhistory,
	getAdminhistory,
	delAdminhistory,
	addAdminhistory,
	updateAdminhistory
} from '@/api/score/adminhistory'

export default {
	name: 'Adminhistory',
	dicts: ['score_type'],
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 基本变更历史记录表格数据
			adminhistoryList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				userId: null,
				userNickName: null,
				userPhone: null,
				type: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {},
			daterangeCreateTime: []
		}
	},
	created() {
		this.getList()
	},
	methods: {
		/** 查询基本变更历史记录列表 */
		getList() {
			this.loading = true
			this.queryParams.params = {}
			if (this.daterangeCreateTime != null && this.daterangeCreateTime != '') {
				this.queryParams.params['beginCreateTime'] = this.daterangeCreateTime[0]
				this.queryParams.params['endCreateTime'] = this.daterangeCreateTime[1]
			}
			listAdminhistory(this.queryParams).then((response) => {
				this.adminhistoryList = response.data
				this.total = response.total
				this.loading = false
			})
		},
		// 取消按钮
		cancel() {
			this.open = false
			this.reset()
		},
		// 表单重置
		reset() {
			this.form = {
				id: null,
				userId: null,
				userNickName: null,
				userPhone: null,
				score: null,
				type: null,
				remainScore: null,
				updateTime: null
			}
			this.resetForm('form')
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.daterangeCreateTime = []
			this.resetForm('queryForm')
			this.handleQuery()
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.id)
			this.single = selection.length !== 1
			this.multiple = !selection.length
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset()
			this.open = true
			this.title = '添加基本变更历史记录'
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const id = row.id || this.ids
			getAdminhistory(id).then((response) => {
				this.form = response.data
				this.open = true
				this.title = '修改基本变更历史记录'
			})
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.form.id != null) {
						updateAdminhistory(this.form).then((response) => {
							this.$modal.msgSuccess('修改成功')
							this.open = false
							this.getList()
						})
					} else {
						addAdminhistory(this.form).then((response) => {
							this.$modal.msgSuccess('新增成功')
							this.open = false
							this.getList()
						})
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const ids = row.id || this.ids
			this.$modal
				.confirm('是否确认删除基本变更历史记录编号为"' + ids + '"的数据项？')
				.then(function () {
					return delAdminhistory(ids)
				})
				.then(() => {
					this.getList()
					this.$modal.msgSuccess('删除成功')
				})
				.catch(() => {})
		},
		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'score/adminhistory/export',
				{
					...this.queryParams
				},
				`adminhistory_${new Date().getTime()}.xlsx`
			)
		}
	}
}
</script>
