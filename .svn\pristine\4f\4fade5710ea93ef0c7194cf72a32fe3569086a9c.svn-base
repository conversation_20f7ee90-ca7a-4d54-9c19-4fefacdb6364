package com.dataxai.web.controller.front;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import com.dataxai.common.core.domain.R;
import com.dataxai.web.domain.WebModelDicDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.WebModelDic;
import com.dataxai.web.service.IWebModelDicService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 模特的咒语词典Controller
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
@RestController
@RequestMapping("/model/character/dic")
@Api(tags = "模特咒语词典")
public class WebModelDicController extends BaseController
{
    @Autowired
    private IWebModelDicService webModelDicService;

    /**
     * 查询模特的咒语词典分类列表
     */
//    @PreAuthorize("@ss.hasPermi('model/character:dic:list')")
    @GetMapping("/list/category")
//    @ApiOperation("模特咒语分类")
    public R<HashMap<String,Object>> listCategory()
    {
//        startPage();
        List<WebModelDic> list = webModelDicService.selectWebModelCategoryDicList(null);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total", total);
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    /**
     * 查询模特的咒语词典列表
     */
//    @PreAuthorize("@ss.hasPermi('model/character:dic:list')")
    @PostMapping("/list")
//    @ApiOperation("模特咒语分类下的具体咒语信息")
    @NotNull
    public R<HashMap<String,Object>> list(WebModelDic webModelDic)
    {
//        startPage();
        List<WebModelDic> list = webModelDicService.selectWebModelDicList(webModelDic);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total", total);
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    /**
     * 查询模特的咒语词典列表
     */
//    @PreAuthorize("@ss.hasPermi('model/character:dic:list')")
    @GetMapping("/listAll")
    @ApiOperation("获取所有模特咒语信息")
    @NotNull
    public R<HashMap<String,Object>> listAll()
    {
//        startPage();
        List<WebModelDic> listCategory = webModelDicService.selectWebModelCategoryDicList(null);
        List<Object> collect = listCategory.stream().map(item -> {
            WebModelDicDTO webModelDicDTO = new WebModelDicDTO();
            List<WebModelDic> list = webModelDicService.selectWebModelDicList(item);
            webModelDicDTO.setCharacteristic(item.getCharacteristic());
            webModelDicDTO.setData(list);
            return webModelDicDTO;
        }).collect(Collectors.toList());

        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(collect).getTotal();
        dataMap.put("total", total);
        dataMap.put("data",collect);
        return R.ok(dataMap);
    }

    /**
     * 导出模特的咒语词典列表
     */
    @PreAuthorize("@ss.hasPermi('model/character:dic:export')")
    @Log(title = "模特的咒语词典", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WebModelDic webModelDic)
    {
        List<WebModelDic> list = webModelDicService.selectWebModelDicList(webModelDic);
        ExcelUtil<WebModelDic> util = new ExcelUtil<WebModelDic>(WebModelDic.class);
        util.exportExcel(response, list, "模特的咒语词典数据");
    }

    /**
     * 获取模特的咒语词典详细信息
     */
//    @PreAuthorize("@ss.hasPermi('model/character:dic:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "根据咒语信息id查询具体的咒语信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(webModelDicService.selectWebModelDicById(id));
    }

    /**
     * 新增模特的咒语词典
     */
    @PreAuthorize("@ss.hasPermi('model/character:dic:add')")
    @Log(title = "模特的咒语词典", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WebModelDic webModelDic)
    {
        return toAjax(webModelDicService.insertWebModelDic(webModelDic));
    }

    /**
     * 修改模特的咒语词典
     */
    @PreAuthorize("@ss.hasPermi('model/character:dic:edit')")
    @Log(title = "模特的咒语词典", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WebModelDic webModelDic)
    {
        return toAjax(webModelDicService.updateWebModelDic(webModelDic));
    }

    /**
     * 删除模特的咒语词典
     */
    @PreAuthorize("@ss.hasPermi('model/character:dic:remove')")
    @Log(title = "模特的咒语词典", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(webModelDicService.deleteWebModelDicByIds(ids));
    }
}
