package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 模特特征的可选项对象 web_model_character_control
 * 
 * <AUTHOR>
 * @date 2024-04-11
 */
public class WebModelCharacterControlDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 特征名称 */
    @Excel(name = "特征名称")
    private String characteristic;

    private List<WebModelCharacterControl> data;

    public List<WebModelCharacterControl> getData() {
        return data;
    }

    public void setData(List<WebModelCharacterControl> data) {
        this.data = data;
    }

    public void setCharacteristic(String characteristic)
    {
        this.characteristic = characteristic;
    }

    public String getCharacteristic() 
    {
        return characteristic;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("characteristic", getCharacteristic())
            .append("data", getData())
            .toString();
    }
}
