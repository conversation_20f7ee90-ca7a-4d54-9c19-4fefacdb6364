package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.AdminTask;

/**
 * 任务Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
public interface AdminTaskMapper 
{
    /**
     * 查询任务
     * 
     * @param taskId 任务主键
     * @return 任务
     */
    public AdminTask selectAdminTaskByTaskId(String taskId);

    /**
     * 查询任务列表
     * 
     * @param adminTask 任务
     * @return 任务集合
     */
    public List<AdminTask> selectAdminTaskList(AdminTask adminTask);

    /**
     * 新增任务
     * 
     * @param adminTask 任务
     * @return 结果
     */
    public int insertAdminTask(AdminTask adminTask);

    /**
     * 修改任务
     * 
     * @param adminTask 任务
     * @return 结果
     */
    public int updateAdminTask(AdminTask adminTask);

    /**
     * 删除任务
     * 
     * @param taskId 任务主键
     * @return 结果
     */
    public int deleteAdminTaskByTaskId(String taskId);

    /**
     * 批量删除任务
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAdminTaskByTaskIds(String[] taskIds);
}
