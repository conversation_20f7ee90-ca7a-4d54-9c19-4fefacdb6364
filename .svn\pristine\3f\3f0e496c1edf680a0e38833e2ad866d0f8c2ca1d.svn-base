package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.TArticle;
import com.dataxai.web.domain.TArticleDto;

/**
 * 文章Mapper接口
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
public interface TArticleMapper
{
    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    public TArticle selectTArticleById(Long id);

    /**
     * 查询文章
     *
     * @param id 文章主键
     * @return 文章
     */
    public TArticleDto selectWebTArticleById(Long id);

    /**
     * 查询文章列表
     *
     * @param tArticle 文章
     * @return 文章集合
     */
    public List<TArticle> selectTArticleList(TArticle tArticle);

    public List<TArticleDto> selectWebTArticleList(TArticle tArticle);

    /**
     * 新增文章
     *
     * @param tArticle 文章
     * @return 结果
     */
    public int insertTArticle(TArticle tArticle);

    /**
     * 修改文章
     *
     * @param tArticle 文章
     * @return 结果
     */
    public int updateTArticle(TArticle tArticle);

    /**
     * 删除文章
     *
     * @param id 文章主键
     * @return 结果
     */
    public int deleteTArticleById(Long id);

    /**
     * 批量删除文章
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTArticleByIds(Long[] ids);
}
