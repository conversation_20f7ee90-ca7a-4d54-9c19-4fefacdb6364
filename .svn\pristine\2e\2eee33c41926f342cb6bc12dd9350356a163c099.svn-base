package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.AdminOrder;
import com.dataxai.web.domain.AdminUser;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface IAdminOrderService 
{
    /**
     * 查询订单
     * 
     * @param orderId 订单主键
     * @return 订单
     */
    public AdminOrder selectAdminOrderByOrderId(String orderId);

    /**
     * 查询订单列表
     * 
     * @param adminOrder 订单
     * @return 订单集合
     */
    public List<AdminOrder> selectAdminOrderList(AdminOrder adminOrder);

    /**
     * 新增订单
     * 
     * @param adminOrder 订单
     * @return 结果
     */
    public int insertAdminOrder(AdminOrder adminOrder);

    /**
     * 修改订单
     * 
     * @param adminOrder 订单
     * @return 结果
     */
    public int updateAdminOrder(AdminOrder adminOrder);

    /**
     * 批量删除订单
     * 
     * @param orderIds 需要删除的订单主键集合
     * @return 结果
     */
    public int deleteAdminOrderByOrderIds(String[] orderIds);

    /**
     * 删除订单信息
     * 
     * @param orderId 订单主键
     * @return 结果
     */
    public int deleteAdminOrderByOrderId(String orderId);

    List<AdminOrder> selectNewlyAddAdminUserList(AdminOrder adminOrderParam);

}
