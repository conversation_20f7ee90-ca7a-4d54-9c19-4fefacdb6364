package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 用户协议 和 隐私政策 对象
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public class ProtocolInfo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 用户协议名称 */
    @Excel(name = "名称")
    private String title;

    /**  协议内容 */
    @Excel(name = " 协议内容")
    private String content;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
