package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.PaymentInfo;

/**
 * 支付日志Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-25
 */
public interface IPaymentInfoService 
{
    /**
     * 查询支付日志
     * 
     * @param paymentId 支付日志主键
     * @return 支付日志
     */
    public PaymentInfo selectPaymentInfoByPaymentId(String paymentId);

    /**
     * 查询支付日志列表
     * 
     * @param paymentInfo 支付日志
     * @return 支付日志集合
     */
    public List<PaymentInfo> selectPaymentInfoList(PaymentInfo paymentInfo);

    /**
     * 新增支付日志
     * 
     * @param paymentInfo 支付日志
     * @return 结果
     */
    public int insertPaymentInfo(PaymentInfo paymentInfo);

    /**
     * 修改支付日志
     * 
     * @param paymentInfo 支付日志
     * @return 结果
     */
    public int updatePaymentInfo(PaymentInfo paymentInfo);

    /**
     * 批量删除支付日志
     * 
     * @param paymentIds 需要删除的支付日志主键集合
     * @return 结果
     */
    public int deletePaymentInfoByPaymentIds(String[] paymentIds);

    /**
     * 删除支付日志信息
     * 
     * @param paymentId 支付日志主键
     * @return 结果
     */
    public int deletePaymentInfoByPaymentId(String paymentId);

    void createPaymentInfo(String plainText);
}
