package com.dataxai.web.utils;

import com.alibaba.fastjson2.JSONObject;
import com.dataxai.common.utils.DateUtils;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.Task;
import com.dataxai.web.domain.TaskOrdinal;
import com.dataxai.web.mapper.TaskMapper;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.COSEncryptionClient;
import com.qcloud.cos.ClientConfig;
import com.qcloud.cos.auth.*;
import com.qcloud.cos.exception.*;
import com.qcloud.cos.model.*;
import com.qcloud.cos.internal.crypto.*;
import com.qcloud.cos.region.Region;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.transfer.*;
import com.qcloud.cos.model.lifecycle.*;
import com.qcloud.cos.model.inventory.*;
import com.qcloud.cos.model.inventory.InventoryFrequency;
import org.springframework.beans.factory.annotation.Autowired;


import java.io.*;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;
import java.net.URL;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.KeyPair;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;

public class TransferDownloadObject {

    private COSClient cosClient;
    private TransferManager transferManager;

    private String uploadId;
    private List<PartETag> partETags;
    private String localFilePath = "C:\\Users\\<USER>\\Desktop\\直播AI工具\\4.jpg";

    /**
     * 高级接口下载对象
     */
    public void transferDownloadObject() throws InterruptedException, IOException, NoSuchAlgorithmException {
        //.cssg-snippet-body-start:[transfer-download-object]
        // Bucket 的命名格式为 BucketName-APPID ，此处填写的存储桶名称必须为此格式
        String bucketName = "ai-photo-task-1303206685";
        String key = "/cjrh/img/1111.png";
        File localDownFile = new File(localFilePath);
        GetObjectRequest getObjectRequest = new GetObjectRequest(bucketName, key);
        // 限流使用的单位是bit/s, 这里设置下载带宽限制为 10MB/s
        getObjectRequest.setTrafficLimit(10*1024*1024);
//        // 下载文件
//        Download download = transferManager.download(getObjectRequest, localDownFile);
//        // 等待传输结束（如果想同步的等待上传结束，则调用 waitForCompletion）
//        download.waitForCompletion();
        
        //.cssg-snippet-body-end
        cosClient.shutdown();
    }

    // .cssg-methods-pragma

    private void initClient() {
        String secretId = "AKIDDFHGJOLB0MlryJWUI1vtp4MqT7naPwLl";
        String secretKey = "X9sDM1lO2yx1TnAg5ZBnQ9nLsWEys5DI";
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        // 2 设置 bucket 的区域, COS 地域的简称请参照 https://cloud.tencent.com/document/product/436/6224
        // clientConfig 中包含了设置 region, https(默认 http), 超时, 代理等 set 方法, 使用可参见源码或者常见问题 Java SDK 部分。
        Region region = new Region("ap-guangzhou");
        ClientConfig clientConfig = new ClientConfig(region);
        // 3 生成 cos 客户端。
        this.cosClient = new COSClient(cred, clientConfig);

        // 高级接口传输类
        // 线程池大小，建议在客户端与 COS 网络充足（例如使用腾讯云的 CVM，同地域上传 COS）的情况下，设置成16或32即可，可较充分的利用网络资源
        // 对于使用公网传输且网络带宽质量不高的情况，建议减小该值，避免因网速过慢，造成请求超时。
        ExecutorService threadPool = Executors.newFixedThreadPool(32);
        // 传入一个 threadpool, 若不传入线程池，默认 TransferManager 中会生成一个单线程的线程池。
        transferManager = new TransferManager(cosClient, threadPool);
        // 设置高级接口的分块上传阈值和分块大小为10MB
        TransferManagerConfiguration transferManagerConfiguration = new TransferManagerConfiguration();
        transferManagerConfiguration.setMultipartUploadThreshold(10 * 1024 * 1024);
        transferManagerConfiguration.setMinimumUploadPartSize(10 * 1024 * 1024);
        transferManager.setConfiguration(transferManagerConfiguration);
    }


//    public static void main(String[] args) {
////        TransferDownloadObject example = new TransferDownloadObject();
////        example.initClient();
////
////        // 高级接口下载对象
////        try {
////            example.transferDownloadObject();
////        } catch (InterruptedException e) {
////            e.printStackTrace();
////            throw new RuntimeException(e);
////        } catch (IOException e) {
////            e.printStackTrace();
////            throw new RuntimeException(e);
////        } catch (NoSuchAlgorithmException e) {
////            e.printStackTrace();
////            throw new RuntimeException(e);
////        }
////
////        // .cssg-methods-pragma
////
////        // 使用完成之后销毁 Client，建议 Client 保持为单例
////        example.cosClient.shutdown();
//
////        TaskOrdinal taskOrdinal = new TaskOrdinal();
////        taskOrdinal.setTaskId(1L);
////        taskOrdinal.setDesType("shortcut");
////        taskOrdinal.setOriginImgUrl("https://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/20240110%2F%E5%BC%A0%E4%B8%89%2Ftask111%2Ftask111.jpg");
////        taskOrdinal.setMarkImgUrl("https://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/20240110%2F%E5%BC%A0%E4%B8%89%2Ftask111%2Ftask111-mask.png");
////
////        JSONObject jsonObject = JSONObject.from(taskOrdinal);
////        String param = jsonObject.toString();
////        System.out.println(param);
//
//    }

    public static void main(String args[]) {
//// 待加密内容
//        String str = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJwYXNzd29yZCI6IjEyMyIsImV4cCI6MTY1MTY2NjgxMiwidXNlcm5hbWUiOiJ6aGFuZ3NhbiJ9.ufQzpVE0scbG1xznt1WdZvCiZgCkyNEfRzk_jpeFMw4";
//// 密码，长度要是8的倍数
//        String password = "95880288";
//        System.out.println(str.length());
//        String encryptData = DesUtil.getEncryptData(str);
//        System.out.println(encryptData.length());

        String originalUrl = "https://ai-photo-task-1303206685.cos.ap-guangzhou.myqcloud.com/2024-01-10/bf02b435045f462e9a52f68d471f32c2_e1b7eee2ed59cd77de86c54c700a3fc6.jpg";
//        String replace = originalUrl.replace(Constants.OOS_URL_PREFIX, "");
        String replace = CommonUtils.subCosPrefix(originalUrl);
        System.out.println(replace);

        double a = 0.0;
        double b = 0.00;
        System.out.println(a==b);

//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("extra","{\"ratio\":1.2}");
//        System.out.println(jsonObject.toString());
//        String objectExtraStr = jsonObject.getString("extra");
//        System.out.println(objectExtraStr);
//        JSONObject objectExtra = JSONObject.parseObject(objectExtraStr);
//        Object object = objectExtra.get("ratio");
//        System.out.println(object);


//        List<String> set = new ArrayList<>();
//        set.add("男");
//        set.add("女");
//        Collections.reverse(set);
//        System.out.println(set);
    }
}