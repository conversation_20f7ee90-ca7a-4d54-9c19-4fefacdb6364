package com.dataxai.web.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 套餐对象 t_tariff_package
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */

@Data
public class AdminTariffPackage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 套餐id */
    private String tariffPackageId;

    /** 套餐名称 */
    @Excel(name = "套餐名称")
    private String name;

    /** 套餐金额 */
    @Excel(name = "套餐金额")
    private Double money;

    /** 同时执行最大任务数量 */
    @Excel(name = "同时执行最大任务数量")
    private Long taskMaxNum;

    /** 套餐是否包含最多执行任务 */
    @Excel(name = "套餐是否包含最多执行任务")
    private Long taskMaxNumExists;

    /** 加油包折扣 */
    @Excel(name = "加油包折扣")
    private Long discount;

    /** 套餐是否包含折扣权益 */
    @Excel(name = "套餐是否包含折扣权益")
    private Long discountExists;

    /** 套餐包含的图片的最大分辨率 */
    @Excel(name = "套餐包含的图片的最大分辨率")
    private Long picMaxSize;

    /** 套餐是否包含生成图片最大分辨率 */
    @Excel(name = "套餐是否包含生成图片最大分辨率")
    private Long picMaxSizeExists;

    /** 资深ai工程师1v1服务支持 */
    @Excel(name = "资深ai工程师1v1服务支持")
    private String vip;

    /** 套餐是否包含1对1服务 */
    @Excel(name = "套餐是否包含1对1服务")
    private Long vipExists;

    /** 是否上架 */
    @Excel(name = "是否上架")
    private Long onSale;

    /** 描述 */
    @Excel(name = "描述")
    private String describe;

    /** 是否默认 （1是，2否）*/
    @Excel(name = "是否默认")
    private Long isDefault;

    /** 是否推荐（1是，2否） */
    @Excel(name = "是否推荐", readConverterExp = "1=是，2否")
    private Long isRecommend;

    /** (0-不包含4k下载，1-包含4k下载) */
    @Excel(name = "(0-不包含4k下载，1-包含4k下载)", readConverterExp = "1=不包含，2包含")
    private Integer fourKDown;

    /** 购买权益**/
    private List<TTariffPackageSub> tariffPackageSubList;

    /** 描述 **/
    private List<AdminEquityDescription> equityDescriptionList;

}
