package com.dataxai.web.utils;

import com.aliyun.core.utils.IOUtils;
import com.dataxai.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.stream.Stream;

@Slf4j
public class CustomMultipartFile implements MultipartFile {

    private final ByteArrayResource resource;
    private final String originalFilename;
    private final long size;
    private final String contentType;

    public CustomMultipartFile(byte[] content, String filename, String contentType) {
        this.resource = new ByteArrayResource(content);
        this.originalFilename = filename;
        this.size = content.length;
        this.contentType = contentType;
    }

    public static MultipartFile createResizedMultipartFile(MultipartFile original, BufferedImage originalImage,int oriWidth, int oriHeight,int maxSize) throws IOException {
        // 调整图片尺寸逻辑
        byte[] resizedImageData = resizeImageWithThumbnailator(original, originalImage,oriWidth, oriHeight,maxSize);

        // 创建新的MultipartFile实例
        return new CustomMultipartFile(
                resizedImageData,
                original.getOriginalFilename(),
                original.getContentType()
        );
    }

    public static byte[] resizeImage(MultipartFile multipartFile,BufferedImage originalImage, int ori_width,int ori_height, int maxSize) throws IOException {
        double ratio  = (double)maxSize / Math.max(ori_width, ori_height);
        log.info("ratio = "+ratio);
        int targetWidth = (int) Math.round(ratio * ori_width);
        int targetHeight = (int) Math.round(ratio * ori_height);
        log.info("调整后尺寸，targetWidth  "+targetWidth +"       targetHeight  "+targetHeight);
        // 调整尺寸
        int imageType = BufferedImage.TYPE_INT_RGB;
        // 判断是否包含透明度信息
        boolean hasAlpha = originalImage.getColorModel().hasAlpha();
        if(hasAlpha){
            imageType = BufferedImage.TYPE_INT_ARGB;
        }
        BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, imageType);
        Graphics2D g = resizedImage.createGraphics();
        g.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
        g.dispose();

        // 将调整后的图片转换为字节数组
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        String fileSuffix = getFileSuffix(multipartFile);
//        ImageIO.write(resizedImage, fileSuffix, outputStream); // 根据实际情况选择合适的格式，这里是JPEG
//        byte[] resizedImageData = outputStream.toByteArray();

        byte[] resizedImageData = null;
        String fileSuffix = getFileSuffix(multipartFile);
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            if (!ImageIO.write(resizedImage, fileSuffix, outputStream)) {
                log.error("Failed to write resized image to output stream.");
                // 处理这种情况，可能是由于格式不支持或其他问题
            } else {
                resizedImageData = outputStream.toByteArray();
            }
        } catch (IOException e) {
            log.error("Error writing resized image to output stream: " + e.getMessage());
            throw e; // 或者适当地处理此异常
        }

        return resizedImageData;
    }
    public static byte[] resizeImageWithThumbnailator(MultipartFile multipartFile,BufferedImage originalImage, int ori_width,int ori_height, int maxSize) throws IOException {
        double ratio  = (double)maxSize / Math.max(ori_width, ori_height);
        log.info("ratio = "+ratio);
        int targetWidth = (int) Math.round(ratio * ori_width);
        int targetHeight = (int) Math.round(ratio * ori_height);
        log.info("调整后尺寸，targetWidth  "+targetWidth +"       targetHeight  "+targetHeight);
        InputStream inputStream = multipartFile.getInputStream();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        Thumbnails.of(inputStream)
                .size(targetWidth, targetHeight)
                // 尽可能保持图片质量
                .outputQuality(1.0f)
                .toOutputStream(outputStream);

        return outputStream.toByteArray();
    }

    private static String getFileSuffix(MultipartFile multipartFile){
        String contentType = multipartFile.getContentType();
        String extension;
        switch (contentType) {
            case "image/jpeg":
                extension = "jpg";
                break;
            case "image/png":
                extension = "png";
                break;
            case "image/gif":
                extension = "gif";
                break;
            case "image/webp":
                extension = "webp";
                break;
            case "image/tiff":
                extension = "tiff";
                break;
            case "image/bmp":
            case "image/x-ms-bmp":
                extension = "bmp";
                break;
            case "image/svg+xml":
                extension = "svg";
                break;
            case "image/vnd.microsoft.icon":
                extension = "ico";
                break;
            default:
                // 不明类型或者不是图片的情况
                extension = "";
                break;
        }
        return extension;
    }

    @Override
    public String getName() {
        return this.originalFilename;
    }

    @Override
    public String getOriginalFilename() {
        return this.originalFilename;
    }

    @Override
    public String getContentType() {
        return this.contentType;
    }

    @Override
    public boolean isEmpty() {
        return size == 0;
    }

    @Override
    public long getSize() {
        return this.size;
    }

    @Override
    public byte[] getBytes() throws IOException {
        return IOUtils.toByteArray(resource.getInputStream());
    }

    @Override
    public InputStream getInputStream() throws IOException {
        return resource.getInputStream();
    }

    @Override
    public void transferTo(File dest) throws IOException, IllegalStateException {
        try (OutputStream os = new FileOutputStream(dest)) {
            transferTo(os);
        }
    }

    public void transferTo(OutputStream dest) throws IOException, IllegalStateException {
        IOUtils.copy(getInputStream(), dest);
    }

}