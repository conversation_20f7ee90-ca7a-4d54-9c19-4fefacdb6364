package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 模特的咒语词典对象 web_model_dic
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
public class WebModelDicDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 特征名称 */
    @Excel(name = "特征名称")
    private String characteristic;

    public List<WebModelDic> getData() {
        return data;
    }

    public void setData(List<WebModelDic> data) {
        this.data = data;
    }

    private List<WebModelDic> data;


    public void setCharacteristic(String characteristic) 
    {
        this.characteristic = characteristic;
    }

    public String getCharacteristic() 
    {
        return characteristic;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("characteristic", getCharacteristic())
            .append("data", getData())
            .toString();
    }
}
