D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\service\GenTableColumnServiceImpl.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\mapper\GenTableMapper.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\domain\GenTableColumn.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\service\GenTableServiceImpl.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\mapper\GenTableColumnMapper.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\domain\GenTable.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\service\IGenTableService.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\util\VelocityUtils.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\config\GenConfig.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\service\IGenTableColumnService.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\util\GenUtils.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\util\VelocityInitializer.java
D:\AI\server-java\ruoyi-generator\src\main\java\com\dataxai\generator\controller\GenController.java
