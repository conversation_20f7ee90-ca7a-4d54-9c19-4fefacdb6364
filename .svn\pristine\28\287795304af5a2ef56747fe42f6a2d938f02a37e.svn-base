import request from '@/utils/request'

// 查询权益列表
export function listAdmindescription(query) {
  return request({
    url: '/adminequity/admindescription/list',
    method: 'get',
    params: query
  })
}

// 查询权益详细
export function getAdmindescription(id) {
  return request({
    url: '/adminequity/admindescription/' + id,
    method: 'get'
  })
}

// 新增权益
export function addAdmindescription(data) {
  return request({
    url: '/adminequity/admindescription',
    method: 'post',
    data: data
  })
}

// 修改权益
export function updateAdmindescription(data) {
  return request({
    url: '/adminequity/admindescription',
    method: 'put',
    data: data
  })
}

// 删除权益
export function delAdmindescription(id) {
  return request({
    url: '/adminequity/admindescription/' + id,
    method: 'delete'
  })
}
