package com.dataxai.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品图片上传任务对象
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class ProductImageUploadTask implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 产品ID */
    private Long productId;

    /** 原始图片URL */
    private String originalImageUrl;

    /** 用户ID */
    private Long userId;

    /** 任务状态 (0-待处理，1-处理中，2-成功，3-失败) */
    private Integer status;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 错误信息 */
    private String errorMessage;

    public ProductImageUploadTask() {
    }

    public ProductImageUploadTask(Long productId, String originalImageUrl, Long userId) {
        this.productId = productId;
        this.originalImageUrl = originalImageUrl;
        this.userId = userId;
        this.status = 0; // 默认待处理
        this.createTime = new Date();
        this.updateTime = new Date();
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getOriginalImageUrl() {
        return originalImageUrl;
    }

    public void setOriginalImageUrl(String originalImageUrl) {
        this.originalImageUrl = originalImageUrl;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @Override
    public String toString() {
        return "ProductImageUploadTask{" +
                "productId=" + productId +
                ", originalImageUrl='" + originalImageUrl + '\'' +
                ", userId=" + userId +
                ", status=" + status +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
