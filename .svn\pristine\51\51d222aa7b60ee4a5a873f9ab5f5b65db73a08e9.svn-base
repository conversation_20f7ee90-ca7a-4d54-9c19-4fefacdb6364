package com.dataxai.web.controller.admincontroller;

import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.TableDataInfo;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.web.domain.TTag;
import com.dataxai.web.domain.TTagDto;
import com.dataxai.web.service.ITTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * 标签Controller
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@RestController
@RequestMapping("/system/tag")
@Api(tags = "标签信息")
public class TTagController extends BaseController
{
    @Autowired
    private ITTagService tTagService;

    /**
     * 查询标签分页
     */
    @ApiOperation("查询标签分页")
    @GetMapping("/list")
    public TableDataInfo list(TTag tTag)
    {
        startPage();
        List<TTag> list = tTagService.selectTTagList(tTag);
        return getDataTable(list);
    }

    @GetMapping("/listAll")
    @ApiOperation("查询标签列表")
    public R<List<TTag>> listAll()
    {
        TTag tTag = new TTag();
        List<TTag> list = tTagService.selectTTagList(tTag);
        return R.ok(list);
    }

    @GetMapping("/front/taglist")
    @ApiOperation("用户端查询所有tag列表")
    public R<HashMap<String,Object>> taglist(TTag tTag)
    {
        List<TTagDto> tTagDtos = tTagService.selectWebTTagList(tTag);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        dataMap.put("total",tTagDtos.size());
        dataMap.put("data",tTagDtos);
        return R.ok(dataMap);
    }

    /**
     * 导出标签列表
     */
    @ApiOperation("导出标签列表")
    @PreAuthorize("@ss.hasPermi('system:tag:export')")
    @Log(title = "标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TTag tTag)
    {
        List<TTag> list = tTagService.selectTTagList(tTag);
        ExcelUtil<TTag> util = new ExcelUtil<TTag>(TTag.class);
        util.exportExcel(response, list, "标签数据");
    }

    /**
     * 获取标签详细信息
     */
    @ApiOperation("获取标签详细信息")
    @PreAuthorize("@ss.hasPermi('system:tag:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tTagService.selectTTagById(id));
    }

    /**
     * 新增标签
     */
    @ApiOperation("新增标签")
    @PreAuthorize("@ss.hasPermi('system:tag:add')")
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TTag tTag)
    {
        return toAjax(tTagService.insertTTag(tTag));
    }

    /**
     * 修改标签
     */
    @ApiOperation("修改标签")
    @PreAuthorize("@ss.hasPermi('system:tag:edit')")
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TTag tTag)
    {
        return toAjax(tTagService.updateTTag(tTag));
    }

    /**
     * 删除标签
     */
    @ApiOperation("删除标签")
    @PreAuthorize("@ss.hasPermi('system:tag:remove')")
    @Log(title = "标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tTagService.deleteTTagByIds(ids));
    }
}
