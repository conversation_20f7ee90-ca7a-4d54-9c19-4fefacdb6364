package com.dataxai.web.service;

import com.dataxai.web.domain.TUserComment;

import java.util.List;

/**
 * 用户评论Service接口
 * 
 * <AUTHOR>
 * @date 2024-06-26
 */
public interface ITUserCommentService 
{
    /**
     * 查询用户评论
     * 
     * @param commentId 用户评论主键
     * @return 用户评论
     */
    public TUserComment selectTUserCommentByCommentId(String commentId);

    /**
     * 查询用户评论列表
     * 
     * @param tUserComment 用户评论
     * @return 用户评论集合
     */
    public List<TUserComment> selectTUserCommentList(TUserComment tUserComment);

    /**
     * 新增用户评论
     * 
     * @param tUserComment 用户评论
     * @return 结果
     */
    public int insertTUserComment(TUserComment tUserComment);

    /**
     * 修改用户评论
     * 
     * @param tUserComment 用户评论
     * @return 结果
     */
    public int updateTUserComment(TUserComment tUserComment);

    /**
     * 批量删除用户评论
     * 
     * @param commentIds 需要删除的用户评论主键集合
     * @return 结果
     */
    public int deleteTUserCommentByCommentIds(String[] commentIds);

    /**
     * 删除用户评论信息
     * 
     * @param commentId 用户评论主键
     * @return 结果
     */
    public int deleteTUserCommentByCommentId(String commentId);
}
