<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item
				label="订单编号"
				prop="orderNo"
			>
				<el-input
					v-model="queryParams.orderNo"
					placeholder="请输入订单编号"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<!-- <el-form-item label="关联套餐id" prop="productParentId">
        <el-input
          v-model="queryParams.productParentId"
          placeholder="请输入关联套餐id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
			<!-- <el-form-item label="商品id" prop="productId">
        <el-input
          v-model="queryParams.productId"
          placeholder="请输入商品id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
			<!-- <el-form-item
				label="订单标题"
				prop="title"
			>
				<el-input
					v-model="queryParams.title"
					placeholder="请输入订单标题"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item> -->
			<el-form-item
				label="订单类型"
				prop="type"
			>
				<el-select
					v-model="queryParams.type"
					placeholder="请选择订单类型"
					clearable
				>
					<el-option
						v-for="dict in dict.type.order_type"
						:key="dict.value"
						:label="dict.label"
						:value="dict.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item
				label="订单金额"
				prop="money"
			>
				<el-input
					v-model="queryParams.money"
					placeholder="请输入订单金额"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item label="创建时间">
				<el-date-picker
					v-model="daterangeCreateTime"
					style="width: 240px"
					value-format="yyyy-MM-dd"
					type="daterange"
					range-separator="-"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
				/>
			</el-form-item>
			<el-form-item
				label="订单状态"
				prop="status"
			>
				<el-select
					v-model="queryParams.status"
					placeholder="请选择订单状态"
					clearable
				>
					<el-option
						v-for="dict in dict.type.order_status"
						:key="dict.value"
						:label="dict.label"
						:value="dict.value"
					/>
				</el-select>
			</el-form-item>
			<!-- <el-form-item label="是否删除" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择是否删除" clearable>
          <el-option
            v-for="dict in dict.type.isdel"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
			<el-form-item>
				<el-button
					type="primary"
					icon="el-icon-search"
					size="mini"
					@click="handleQuery"
					>搜索</el-button
				>
				<el-button
					icon="el-icon-refresh"
					size="mini"
					@click="resetQuery"
					>重置</el-button
				>
			</el-form-item>
		</el-form>

		<el-row
			:gutter="10"
			class="mb8"
		>
			<el-col :span="1.5">
				<!-- <el-button
					v-hasPermi="['adminorder:adminorder:add']"
					type="primary"
					plain
					icon="el-icon-plus"
					size="mini"
					@click="handleAdd"
					>新增</el-button
				>
			</el-col> -->
				<!-- <el-col :span="1.5">
				<el-button
					v-hasPermi="['adminorder:adminorder:edit']"
					type="success"
					plain
					icon="el-icon-edit"
					size="mini"
					:disabled="single"
					@click="handleUpdate"
					>修改</el-button
				>
			</el-col>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['adminorder:adminorder:remove']"
					type="danger"
					plain
					icon="el-icon-delete"
					size="mini"
					:disabled="multiple"
					@click="handleDelete"
					>删除</el-button
				>
			</el-col> -->
				<el-col :span="1.5">
					<el-button
						v-hasPermi="['adminorder:adminorder:export']"
						type="warning"
						plain
						icon="el-icon-download"
						size="mini"
						@click="handleExport"
						>导出</el-button
					>
				</el-col>
				<right-toolbar
					:show-search.sync="showSearch"
					@queryTable="getList"
				/> </el-col
		></el-row>

		<el-table
			v-loading="loading"
			:data="adminorderList"
			@selection-change="handleSelectionChange"
		>
			<el-table-column
				type="selection"
				width="55"
				align="center"
			/>

			<el-table-column
				label="订单编号"
				align="center"
				prop="orderNo"
			/>
			<el-table-column
				label="套餐"
				align="center"
				prop="tariffPackageName"
			/>
			<el-table-column
				label="子套餐"
				align="center"
				prop="tariffPackageSubName"
			/>
			<!-- <el-table-column
				label="二维码"
				align="center"
				prop="codeUrl"
				width="100"
			>
				<template slot-scope="scope">
					<image-preview
						:src="scope.row.codeUrl"
						:width="50"
						:height="50"
					/>
				</template>
			</el-table-column> -->
			<el-table-column
				label="订单标题"
				align="center"
				prop="title"
			/>
			<el-table-column
				label="购买时长(天)"
				align="center"
				prop="duration"
				width="100"
			/>
			<el-table-column
				label="到期时间"
				align="center"
				prop="productEndTime"
				width="180"
			>
				<template slot-scope="scope">
					<span>{{ scope.row.productEndTime }}</span>
				</template>
			</el-table-column>
			<el-table-column
				label="订单类型"
				align="center"
				prop="type"
			>
				<template slot-scope="scope">
					<dict-tag
						:options="dict.type.order_type"
						:value="scope.row.type"
					/>
				</template>
			</el-table-column>
			<el-table-column
				label="订单金额"
				align="center"
				prop="money"
			/>
			<el-table-column
				label="支付时间"
				align="center"
				prop="updateTime"
				width="180"
			>
				<template slot-scope="scope">
					<span>{{ scope.row.updateTime || '-' }}</span>
				</template>
			</el-table-column>
			<el-table-column
				label="创建时间"
				align="center"
				prop="createTime"
				width="180"
			>
				<template slot-scope="scope">
					<span>{{ scope.row.createTime }}</span>
				</template>
			</el-table-column>
			<el-table-column
				label="订单状态"
				align="center"
				prop="status"
			>
				<template slot-scope="scope">
					<dict-tag
						:options="dict.type.order_status"
						:value="scope.row.status"
					/>
				</template>
			</el-table-column>
			<!-- <el-table-column
				label="是否删除"
				align="center"
				prop="delFlag"
			>
				<template slot-scope="scope">
					<dict-tag
						:options="dict.type.isdel"
						:value="scope.row.delFlag"
					/>
				</template>
			</el-table-column> -->
			<el-table-column
				label="用户"
				align="center"
				prop="userPhone"
			/>

			<!-- <el-table-column
				label="修改时间"
				align="center"
				prop="updateTime"
				width="180"
			>
				<template slot-scope="scope">
					<span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</span>
				</template>
			</el-table-column> -->
			<!-- <el-table-column
				label="修改者"
				align="center"
				prop="updateBy"
			/> -->
			<!-- <el-table-column
				label="操作"
				align="center"
				class-name="small-padding fixed-width"
			>
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['adminorder:adminorder:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
						>修改</el-button
					>
					<el-button
						v-hasPermi="['adminorder:adminorder:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					>
				</template>
			</el-table-column> -->
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改订单对话框 -->
		<el-dialog
			:title="title"
			:visible.sync="open"
			width="500px"
			append-to-body
		>
			<el-form
				ref="form"
				:model="form"
				:rules="rules"
				label-width="80px"
			>
				<el-form-item
					label="订单编号"
					prop="orderNo"
				>
					<el-input
						v-model="form.orderNo"
						placeholder="请输入订单编号"
					/>
				</el-form-item>
				<el-form-item
					label="关联套餐id"
					prop="productParentId"
				>
					<el-input
						v-model="form.productParentId"
						placeholder="请输入关联套餐id"
					/>
				</el-form-item>
				<el-form-item
					label="商品id"
					prop="productId"
				>
					<el-input
						v-model="form.productId"
						placeholder="请输入商品id"
					/>
				</el-form-item>
				<el-form-item
					label="二维码"
					prop="codeUrl"
				>
					<image-upload v-model="form.codeUrl" />
				</el-form-item>
				<el-form-item
					label="订单标题"
					prop="title"
				>
					<el-input
						v-model="form.title"
						placeholder="请输入订单标题"
					/>
				</el-form-item>
				<el-form-item
					label="购买时长"
					prop="duration"
				>
					<el-input
						v-model="form.duration"
						placeholder="请输入购买时长"
					/>
				</el-form-item>
				<el-form-item
					label="商品到期时间"
					prop="productEndTime"
				>
					<el-date-picker
						v-model="form.productEndTime"
						clearable
						type="date"
						value-format="yyyy-MM-dd"
						placeholder="请选择商品到期时间"
					/>
				</el-form-item>
				<el-form-item
					label="订单类型"
					prop="type"
				>
					<el-select
						v-model="form.type"
						placeholder="请选择订单类型"
					>
						<el-option
							v-for="dict in dict.type.order_type"
							:key="dict.value"
							:label="dict.label"
							:value="parseInt(dict.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="订单金额"
					prop="money"
				>
					<el-input
						v-model="form.money"
						placeholder="请输入订单金额"
					/>
				</el-form-item>
				<el-form-item
					label="订单状态"
					prop="status"
				>
					<el-select
						v-model="form.status"
						placeholder="请选择订单状态"
					>
						<el-option
							v-for="dict in dict.type.order_status"
							:key="dict.value"
							:label="dict.label"
							:value="parseInt(dict.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="是否删除"
					prop="delFlag"
				>
					<el-select
						v-model="form.delFlag"
						placeholder="请选择是否删除"
					>
						<el-option
							v-for="dict in dict.type.isdel"
							:key="dict.value"
							:label="dict.label"
							:value="parseInt(dict.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="用户id"
					prop="userId"
				>
					<el-input
						v-model="form.userId"
						placeholder="请输入用户id"
					/>
				</el-form-item>
			</el-form>
			<div
				slot="footer"
				class="dialog-footer"
			>
				<el-button
					type="primary"
					@click="submitForm"
					>确 定</el-button
				>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import {
	listAdminorder,
	getAdminorder,
	delAdminorder,
	addAdminorder,
	updateAdminorder
} from '@/api/adminorder/adminorder'

export default {
	name: 'Adminorder',
	dicts: ['order_status', 'order_type', 'isdel'],
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 订单表格数据
			adminorderList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 用户id时间范围
			daterangeCreateTime: [],
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				orderNo: null,
				productParentId: null,
				productId: null,
				title: null,
				type: null,
				money: null,
				createTime: null,
				status: null,
				delFlag: null,
				userId: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {}
		}
	},
	created() {
		this.getList()
	},
	methods: {
		/** 查询订单列表 */
		getList() {
			this.loading = true
			this.queryParams.params = {}
			if (this.daterangeCreateTime != null && this.daterangeCreateTime != '') {
				this.queryParams.params['beginCreateTime'] = this.daterangeCreateTime[0]
				this.queryParams.params['endCreateTime'] = this.daterangeCreateTime[1]
			}
			listAdminorder(this.queryParams).then((response) => {
				this.adminorderList = response.data
				this.total = response.total
				this.loading = false
			})
		},
		// 取消按钮
		cancel() {
			this.open = false
			this.reset()
		},
		// 表单重置
		reset() {
			this.form = {
				orderId: null,
				orderNo: null,
				productParentId: null,
				productId: null,
				codeUrl: null,
				title: null,
				duration: null,
				productEndTime: null,
				type: null,
				money: null,
				createTime: null,
				status: null,
				delFlag: null,
				userId: null,
				createBy: null,
				updateTime: null,
				updateBy: null
			}
			this.resetForm('form')
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.daterangeCreateTime = []
			this.resetForm('queryForm')
			this.handleQuery()
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.orderId)
			this.single = selection.length !== 1
			this.multiple = !selection.length
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset()
			this.open = true
			this.title = '添加订单'
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const orderId = row.orderId || this.ids
			getAdminorder(orderId).then((response) => {
				this.form = response.data
				this.open = true
				this.title = '修改订单'
			})
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.form.orderId != null) {
						updateAdminorder(this.form).then((response) => {
							this.$modal.msgSuccess('修改成功')
							this.open = false
							this.getList()
						})
					} else {
						addAdminorder(this.form).then((response) => {
							this.$modal.msgSuccess('新增成功')
							this.open = false
							this.getList()
						})
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const orderIds = row.orderId || this.ids
			this.$modal
				.confirm('是否确认删除订单编号为"' + orderIds + '"的数据项？')
				.then(function () {
					return delAdminorder(orderIds)
				})
				.then(() => {
					this.getList()
					this.$modal.msgSuccess('删除成功')
				})
				.catch(() => {})
		},
		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'adminorder/adminorder/export',
				{
					...this.queryParams
				},
				`adminorder_${new Date().getTime()}.xlsx`
			)
		}
	}
}
</script>
