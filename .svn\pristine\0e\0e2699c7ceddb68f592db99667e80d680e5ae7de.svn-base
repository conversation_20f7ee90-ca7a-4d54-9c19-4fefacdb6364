<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.TariffPackageSubMapper">
    
    <resultMap type="TariffPackageSub" id="TariffPackageSubResult">
        <result property="tariffPackageSubId"    column="tariff_package_sub_id"    />
        <result property="tariffPackageSubName"    column="tariff_package_sub_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="score"    column="score"    />
        <result property="subMoney"    column="sub_money"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="cycle"    column="cycle"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTariffPackageSubVo">
        select tariff_package_sub_id, parent_id, score, start_time, end_time, sub_money, cycle, sub_on_sale,
               create_by, create_time, update_by, update_time,tariff_package_sub_name from t_tariff_package_sub
    </sql>

    <select id="selectTariffPackageSubList" parameterType="TariffPackageSub" resultMap="TariffPackageSubResult">
        <include refid="selectTariffPackageSubVo"/>
        <where>  
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="subMoney != null "> and sub_money = #{subMoney}</if>
            <if test="cycle != null "> and cycle = #{cycle}</if>
            <if test="subOnSale != null "> and sub_on_sale = #{subOnSale}</if>
            <if test="tariffPackageSubName != null "> and tariff_package_sub_name = #{tariffPackageSubName}</if>
        </where>
    </select>

    <select id="selectTariffSub" parameterType="TariffPackageSub" resultMap="TariffPackageSubResult">
        select tariff_package_sub_id, parent_id, score, start_time, end_time, cycle, create_by,
               sub_money,sub_on_sale,create_time, update_by, update_time,tariff_package_sub_name from t_tariff_package_sub
    </select>

    <select id="selectListByParentId" parameterType="TariffPackageSub" resultMap="TariffPackageSubResult">
        select tariff_package_sub_id, parent_id, score, start_time, end_time, cycle, create_by,
               sub_money,sub_on_sale,create_time, update_by, update_time,tariff_package_sub_name
        from t_tariff_package_sub
        where parent_id = #{parentId}
    </select>

    <select id="selectTariffPackageSubByTariffPackageSubId" parameterType="String" resultMap="TariffPackageSubResult">
        <include refid="selectTariffPackageSubVo"/>
        where tariff_package_sub_id = #{tariffPackageSubId}
    </select>
        
    <insert id="insertTariffPackageSub" parameterType="TariffPackageSub" useGeneratedKeys="true" keyProperty="tariffPackageSubId">
        insert into t_tariff_package_sub
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="score != null">score,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="money != null">money,</if>
            <if test="cycle != null">cycle,</if>
            <if test="onSale != null">on_sale,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="tariffPackageSubName != null">tariff_package_sub_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="score != null">#{score},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="money != null">#{money},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="onSale != null">#{onSale},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="tariffPackageSubName != null">#{tariffPackageSubName},</if>
         </trim>
    </insert>

    <update id="updateTariffPackageSub" parameterType="TariffPackageSub">
        update t_tariff_package_sub
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="money != null">money = #{money},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="onSale != null">on_sale = #{onSale},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="tariffPackageSubName != null">tariff_package_sub_name = #{tariffPackageSubName},</if>
        </trim>
        where tariff_package_sub_id = #{tariffPackageSubId}
    </update>

    <delete id="deleteTariffPackageSubByTariffPackageSubId" parameterType="String">
        delete from t_tariff_package_sub where tariff_package_sub_id = #{tariffPackageSubId}
    </delete>

    <delete id="deleteTariffPackageSubByTariffPackageSubIds" parameterType="String">
        delete from t_tariff_package_sub where tariff_package_sub_id in 
        <foreach item="tariffPackageSubId" collection="array" open="(" separator="," close=")">
            #{tariffPackageSubId}
        </foreach>
    </delete>

    <select id="selectByIds" parameterType="String" resultMap="TariffPackageSubResult">
        <include refid="selectTariffPackageSubVo"/> where tariff_package_sub_id in
        <foreach item="tariffPackageSubId" collection="array" open="(" separator="," close=")">
            #{tariffPackageSubId}
        </foreach>
    </select>

</mapper>