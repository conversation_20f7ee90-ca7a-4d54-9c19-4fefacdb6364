<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.TBaseinfoDisclaimersMapper">
    
    <resultMap type="TBaseinfoDisclaimers" id="TBaseinfoDisclaimersResult">
        <result property="id"    column="id"    />
        <result property="disclaimersName"    column="disclaimers_name"    />
        <result property="disclaimersContent"    column="disclaimers_content"    />
    </resultMap>

    <sql id="selectTBaseinfoDisclaimersVo">
        select id, disclaimers_name, disclaimers_content from t_baseinfo_disclaimers
    </sql>

    <select id="selectTBaseinfoDisclaimersList" parameterType="TBaseinfoDisclaimers" resultMap="TBaseinfoDisclaimersResult">
        <include refid="selectTBaseinfoDisclaimersVo"/>
        <where>  
            <if test="disclaimersName != null  and disclaimersName != ''"> and disclaimers_name like concat('%', #{disclaimersName}, '%')</if>
        </where>
    </select>
    
    <select id="selectTBaseinfoDisclaimersById" parameterType="Long" resultMap="TBaseinfoDisclaimersResult">
        <include refid="selectTBaseinfoDisclaimersVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTBaseinfoDisclaimers" parameterType="TBaseinfoDisclaimers" useGeneratedKeys="true" keyProperty="id">
        insert into t_baseinfo_disclaimers
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="disclaimersName != null">disclaimers_name,</if>
            <if test="disclaimersContent != null">disclaimers_content,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="disclaimersName != null">#{disclaimersName},</if>
            <if test="disclaimersContent != null">#{disclaimersContent},</if>
         </trim>
    </insert>

    <update id="updateTBaseinfoDisclaimers" parameterType="TBaseinfoDisclaimers">
        update t_baseinfo_disclaimers
        <trim prefix="SET" suffixOverrides=",">
            <if test="disclaimersName != null">disclaimers_name = #{disclaimersName},</if>
            <if test="disclaimersContent != null">disclaimers_content = #{disclaimersContent},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTBaseinfoDisclaimersById" parameterType="Long">
        delete from t_baseinfo_disclaimers where id = #{id}
    </delete>

    <delete id="deleteTBaseinfoDisclaimersByIds" parameterType="String">
        delete from t_baseinfo_disclaimers where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>