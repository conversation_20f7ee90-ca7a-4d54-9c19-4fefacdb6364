package com.dataxai.web.utils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 排队问题，计算每队需要的时间
 */
public class WaitSeqUtils {

    // 主方法：计算每个窗口的总等待时间
    public static List<Integer> calculateWindowWaitingTimes(List<Integer> processTimes, int windowCount) {
        if (processTimes == null || processTimes.isEmpty() || windowCount <= 0) {
            return Collections.emptyList();
        }

        List<Integer> sortedTimes = new ArrayList<>(processTimes);
//        sortedTimes.sort(Comparator.reverseOrder());

        // 2. 初始化窗口最小堆
        PriorityQueue<Window> windows = new PriorityQueue<>(Comparator.comparingInt(w -> w.totalTime));
        for (int i = 0; i < windowCount; i++) {
            windows.add(new Window(i));
        }

        // 3. 分配任务到窗口
        for (int time : sortedTimes) {
            Window window = windows.poll();
            window.totalTime += time;
            windows.offer(window);
        }

        // 4. 按窗口ID排序并返回结果
        List<Window> sortedWindows = new ArrayList<>();
        while (!windows.isEmpty()) {
            sortedWindows.add(windows.poll());
        }
        sortedWindows.sort(Comparator.comparingInt(w -> w.id));

        return sortedWindows.stream()
            .map(w -> w.totalTime)
            .collect(Collectors.toList());
    }

    // 窗口内部类
    static class Window {
        final int id;
        int totalTime;

        public Window(int id) {
            this.id = id;
            this.totalTime = 0;
        }
    }
}
