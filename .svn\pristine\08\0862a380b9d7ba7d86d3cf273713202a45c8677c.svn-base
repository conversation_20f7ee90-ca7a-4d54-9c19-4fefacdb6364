package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.TBaseinfoDisclaimers;

/**
 * 免责声明Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface TBaseinfoDisclaimersMapper 
{
    /**
     * 查询免责声明
     * 
     * @param id 免责声明主键
     * @return 免责声明
     */
    public TBaseinfoDisclaimers selectTBaseinfoDisclaimersById(Long id);

    /**
     * 查询免责声明列表
     * 
     * @param tBaseinfoDisclaimers 免责声明
     * @return 免责声明集合
     */
    public List<TBaseinfoDisclaimers> selectTBaseinfoDisclaimersList(TBaseinfoDisclaimers tBaseinfoDisclaimers);

    /**
     * 新增免责声明
     * 
     * @param tBaseinfoDisclaimers 免责声明
     * @return 结果
     */
    public int insertTBaseinfoDisclaimers(TBaseinfoDisclaimers tBaseinfoDisclaimers);

    /**
     * 修改免责声明
     * 
     * @param tBaseinfoDisclaimers 免责声明
     * @return 结果
     */
    public int updateTBaseinfoDisclaimers(TBaseinfoDisclaimers tBaseinfoDisclaimers);

    /**
     * 删除免责声明
     * 
     * @param id 免责声明主键
     * @return 结果
     */
    public int deleteTBaseinfoDisclaimersById(Long id);

    /**
     * 批量删除免责声明
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTBaseinfoDisclaimersByIds(Long[] ids);
}
