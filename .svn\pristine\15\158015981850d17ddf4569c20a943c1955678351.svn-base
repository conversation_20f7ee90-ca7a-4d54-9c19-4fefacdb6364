package com.dataxai.service.impl;

import java.util.List;
import com.dataxai.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.mapper.ProductInfoMapper;
import com.dataxai.domain.ProductInfo;
import com.dataxai.service.IProductInfoService;

/**
 * 产品信息表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Service
public class ProductInfoServiceImpl implements IProductInfoService 
{
    @Autowired
    private ProductInfoMapper productInfoMapper;

    /**
     * 查询产品信息表
     * 
     * @param id 产品信息表主键
     * @return 产品信息表
     */
    @Override
    public ProductInfo selectProductInfoById(Long id)
    {
        return productInfoMapper.selectProductInfoById(id);
    }

    /**
     * 查询产品信息表列表
     * 
     * @param productInfo 产品信息表
     * @return 产品信息表
     */
    @Override
    public List<ProductInfo> selectProductInfoList(ProductInfo productInfo)
    {
        return productInfoMapper.selectProductInfoList(productInfo);
    }

    /**
     * 新增产品信息表
     * 
     * @param productInfo 产品信息表
     * @return 结果
     */
    @Override
    public int insertProductInfo(ProductInfo productInfo)
    {
        productInfo.setCreateTime(DateUtils.getNowDate());
        return productInfoMapper.insertProductInfo(productInfo);
    }

    /**
     * 修改产品信息表
     * 
     * @param productInfo 产品信息表
     * @return 结果
     */
    @Override
    public int updateProductInfo(ProductInfo productInfo)
    {
        productInfo.setUpdateTime(DateUtils.getNowDate());
        return productInfoMapper.updateProductInfo(productInfo);
    }

    /**
     * 批量删除产品信息表
     * 
     * @param ids 需要删除的产品信息表主键
     * @return 结果
     */
    @Override
    public int deleteProductInfoByIds(Long[] ids)
    {
        return productInfoMapper.deleteProductInfoByIds(ids);
    }

    /**
     * 删除产品信息表信息
     * 
     * @param id 产品信息表主键
     * @return 结果
     */
    @Override
    public int deleteProductInfoById(Long id)
    {
        return productInfoMapper.deleteProductInfoById(id);
    }
}
