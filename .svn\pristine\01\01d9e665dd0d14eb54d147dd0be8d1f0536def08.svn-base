<template>
	<datawrap
		title="新增用户"
		:total="total"
	>
		<echart :option-data="options" />
	</datawrap>
</template>
<script>
import datawrap from '@/components/Datawrap/index.vue'
import echart from '@/components/Echart/index.vue'
import { getTodayNewUserCount } from '@/api/dataOverview'

export default {
	name: 'Taskview',
	components: {
		datawrap,
		echart
	},
	data() {
		return {
			total: 0,
			options: {
				tooltip: {
					trigger: 'axis',
					axisPointer: {
						type: 'shadow'
					}
				},
				grid: {
					top: 20,
					bottom: 30,
					left: 30
				},
				xAxis: {
					type: 'category',
					data: []
				},
				yAxis: {
					type: 'value',
					axisLine: {
						show: true
					},
					axisTick: {
						show: true
					}
				},
				series: [
					{
						data: [],
						type: 'line',
						smooth: true
					}
				]
			}
		}
	},
	mounted() {
		this.initData()
	},
	methods: {
		initData() {
			getTodayNewUserCount().then((res) => {
				if (res.code === 200) {
					this.total = res.data.todayCount
					const _data = res.data.data
					this.options.xAxis.data = _data.map((item) => item.xaxis)
					this.options.series[0].data = _data.map((item, i) => {
						return item.yaxis
					})
				}
			})
		}
	}
}
</script>
<style lang="scss" scoped></style>
