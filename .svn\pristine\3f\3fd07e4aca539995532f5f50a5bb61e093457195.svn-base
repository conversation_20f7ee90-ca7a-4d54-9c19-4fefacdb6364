<template>
	<div class="app-container">
		<el-form
			v-show="showSearch"
			ref="queryForm"
			:model="queryParams"
			size="small"
			:inline="true"
			label-width="68px"
		>
			<el-form-item
				label="套餐名称"
				prop="name"
			>
				<el-input
					v-model="queryParams.name"
					placeholder="请输入套餐名称"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item
				label="套餐金额"
				prop="money"
			>
				<el-input
					v-model="queryParams.money"
					placeholder="请输入套餐金额"
					clearable
					@keyup.enter.native="handleQuery"
				/>
			</el-form-item>
			<el-form-item
				label="是否上架"
				prop="onSale"
			>
				<el-select
					v-model="queryParams.onSale"
					placeholder="请选择是否上架"
					clearable
				>
					<el-option
						v-for="dict in dict.type.on_sale"
						:key="dict.value"
						:label="dict.label"
						:value="dict.value"
					/>
				</el-select>
			</el-form-item>
			<el-form-item label="创建时间">
				<el-date-picker
					v-model="daterangeCreateTime"
					style="width: 240px"
					value-format="yyyy-MM-dd"
					type="daterange"
					range-separator="-"
					start-placeholder="开始日期"
					end-placeholder="结束日期"
				/>
			</el-form-item>

			<el-form-item>
				<el-button
					type="primary"
					icon="el-icon-search"
					size="mini"
					@click="handleQuery"
					>搜索</el-button
				>
				<el-button
					icon="el-icon-refresh"
					size="mini"
					@click="resetQuery"
					>重置</el-button
				>
			</el-form-item>
		</el-form>

		<el-row
			:gutter="10"
			class="mb8"
		>
			<el-col :span="1.5">
				<el-button
					v-hasPermi="['package:adminpackage:add']"
					type="primary"
					plain
					icon="el-icon-plus"
					size="mini"
					@click="handleAdd"
					>新增</el-button
				>
			</el-col>

			<right-toolbar
				:show-search.sync="showSearch"
				@queryTable="getList"
			/>
		</el-row>

		<el-table
			v-loading="loading"
			:data="adminpackageList"
			@selection-change="handleSelectionChange"
		>
			<el-table-column
				type="selection"
				width="55"
				align="center"
			/>
			<el-table-column
				label="套餐id"
				align="center"
				prop="tariffPackageId"
			/>
			<el-table-column
				label="套餐名称"
				align="center"
				prop="name"
			/>
			<el-table-column
				label="套餐金额"
				align="center"
				prop="money"
			/>
			<el-table-column
				label="最大任务数量"
				align="center"
				prop="taskMaxNum"
			/>
			<el-table-column
				label="4k图下载"
				align="center"
			>
				<template slot-scope="scope">
					<span>{{ scope.row.fourKDown ? '包含' : '不包含' }}</span>
				</template>
			</el-table-column>

			<el-table-column
				label="加油包折扣"
				align="center"
				prop="discount"
			/>
			<el-table-column
				label="套餐权益"
				align="center"
			>
				<template slot-scope="scope">
					<el-button
						type="text"
						@click="handleCheckDetail(scope.row, 'equityDescriptionList')"
						>查看</el-button
					>
				</template>
			</el-table-column>
			<el-table-column
				label="购买权益"
				align="center"
			>
				<template slot-scope="scope">
					<el-button
						type="text"
						@click="handleCheckDetail(scope.row, 'tariffPackageSubList')"
						>查看</el-button
					>
				</template>
			</el-table-column>
			<el-table-column
				label="是否默认"
				align="center"
				prop="isDefault"
			>
				<template slot-scope="scope">
					<dict-tag
						:options="dict.type.is_default"
						:value="scope.row.isDefault"
					/>
				</template>
			</el-table-column>
			<el-table-column
				label="是否上架"
				align="center"
				prop="onSale"
			>
				<template slot-scope="scope">
					<dict-tag
						:options="dict.type.on_sale"
						:value="scope.row.onSale"
					/>
				</template>
			</el-table-column>
			<el-table-column
				label="操作"
				align="center"
				class-name="small-padding fixed-width"
			>
				<template slot-scope="scope">
					<el-button
						v-hasPermi="['package:adminpackage:edit']"
						size="mini"
						type="text"
						icon="el-icon-edit"
						@click="handleUpdate(scope.row)"
						>修改</el-button
					>
					<!-- <el-button
						v-hasPermi="['package:adminpackage:remove']"
						size="mini"
						type="text"
						icon="el-icon-delete"
						@click="handleDelete(scope.row)"
						>删除</el-button
					> -->
				</template>
			</el-table-column>
		</el-table>

		<pagination
			v-show="total > 0"
			:total="total"
			:page.sync="queryParams.pageNum"
			:limit.sync="queryParams.pageSize"
			@pagination="getList"
		/>

		<!-- 添加或修改套餐对话框 -->
		<el-dialog
			:title="title"
			:visible.sync="open"
			width="800px"
			append-to-body
		>
			<el-form
				ref="form"
				:model="form"
				:rules="rules"
				label-width="120px"
			>
				<el-form-item
					label="套餐名称"
					prop="name"
				>
					<el-input
						v-model="form.name"
						placeholder="请输入套餐名称"
						:disabled="formDisabled"
					/>
				</el-form-item>
				<el-form-item
					label="套餐描述"
					prop="describe"
				>
					<el-input
						v-model="form.describe"
						placeholder="请输入套餐描述"
						:disabled="formDisabled"
					/>
				</el-form-item>
				<el-form-item
					label="套餐金额"
					prop="money"
				>
					<el-input
						v-model="form.money"
						placeholder="请输入套餐金额"
						:disabled="formDisabled"
						:min="0"
					/>
				</el-form-item>
				<el-form-item
					label="最大任务数量"
					prop="taskMaxNum"
				>
					<el-input
						v-model="form.taskMaxNum"
						placeholder="请输入最大任务数量"
						:disabled="formDisabled"
						:min="0"
					/>
				</el-form-item>
				<el-form-item
					label="4K图下载"
					prop="fourKDown"
				>
					<el-switch
						v-model="form.fourKDown"
						:active-value="1"
						:inactive-value="0"
						:disabled="formDisabled"
					/>
				</el-form-item>
				<el-form-item
					label="加油包折扣"
					prop="discount"
				>
					<el-input
						v-model="form.discount"
						placeholder="请输入加油包折扣"
						:disabled="formDisabled"
						:min="0"
					/>
				</el-form-item>
				<el-form-item
					label="是否默认"
					prop="isDefault"
				>
					<el-select
						v-model="form.isDefault"
						placeholder="请选择是否默认"
					>
						<el-option
							v-for="dict in dict.type.is_default"
							:key="dict.value"
							:label="dict.label"
							:value="Number(dict.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label="是否上架"
					prop="onSale"
				>
					<el-select
						v-model="form.onSale"
						placeholder="请选择是否上架"
					>
						<el-option
							v-for="dict in dict.type.on_sale"
							:key="dict.value"
							:label="dict.label"
							:value="parseInt(dict.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item
					label=""
					label-width="0px"
				>
					<div class="form-item-div">套餐权益（展示，最多5条）</div>

					<el-row
						v-for="(list, i) in form.equityDescriptionList"
						:key="i"
						:gutter="20"
						class="row-view"
					>
						<el-col :span="8">
							<div><span class="col-require">*</span>描述</div>
							<el-form-item
								label=""
								:prop="'equityDescriptionList.' + i + '.content'"
								label-width="0px"
								:rules="[{ required: true, message: '描述不能为空' }]"
							>
								<el-input
									v-model="list.content"
									placeholder="请输入描述"
									:disabled="formDisabled"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<div><span class="col-require">*</span>是否包含</div>
							<el-switch
								v-model="list.contains"
								:active-value="1"
								:inactive-value="0"
								:disabled="formDisabled"
							/>
						</el-col>
						<el-col
							v-if="!formDisabled"
							:span="8"
						>
							<div>操作</div>
							<el-row type="flex">
								<el-button
									v-if="i > 0 || form.equityDescriptionList.length > 1"
									type="danger"
									size="small"
									@click="handleFormDelete(i, 'equityDescriptionList')"
									>删除</el-button
								>
								<el-button
									type="primary"
									size="small"
									@click="handleFormAdd(i, 'equityDescriptionList')"
									>增加</el-button
								>
							</el-row>
						</el-col>
					</el-row>
				</el-form-item>
				<el-form-item
					label=""
					label-width="0px"
				>
					<div class="form-item-div">购买权益（购买）</div>

					<el-row
						v-for="(list, i) in form.tariffPackageSubList"
						:key="i"
						:gutter="20"
						class="row-view"
					>
						<el-col :span="5">
							<div><span class="col-require">*</span>名称</div>
							<el-form-item
								label=""
								:prop="'tariffPackageSubList.' + i + '.tariffPackageSubName'"
								label-width="0px"
								:rules="[{ required: true, message: '名称不能为空' }]"
							>
								<el-input
									v-model="list.tariffPackageSubName"
									placeholder="请输入名称"
									:disabled="formDisabled"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="5">
							<div><span class="col-require">*</span>积分</div>
							<el-form-item
								label=""
								:prop="'tariffPackageSubList.' + i + '.score'"
								label-width="0px"
								:rules="[
									{ required: true, message: '积分不能为空' },
									{ validator: validateNum }
								]"
							>
								<el-input
									v-model="list.score"
									placeholder="请输入积分"
									:disabled="formDisabled"
									:min="0"
								/>
							</el-form-item>
						</el-col>
						<el-col :span="5">
							<div><span class="col-require">*</span>时长</div>
							<el-form-item
								label=""
								:prop="'tariffPackageSubList.' + i + '.cycle'"
								label-width="0px"
								:rules="[
									{ required: true, message: '时长不能为空' },
									{ validator: validateNum }
								]"
							>
								<el-input
									v-model="list.cycle"
									placeholder="请输入时长"
									:disabled="formDisabled"
									:min="0"
								>
									<span slot="append">天</span>
								</el-input>
							</el-form-item>
						</el-col>
						<el-col :span="5">
							<div><span class="col-require">*</span>价格</div>
							<el-form-item
								label=""
								:prop="'tariffPackageSubList.' + i + '.subMoney'"
								label-width="0px"
								:rules="[
									{
										required: true,
										message: '价格不能为空'
									},
									{ validator: validateNum }
								]"
							>
								<el-input
									v-model="list.subMoney"
									placeholder="请输入价格"
									autocomplete="off"
									:disabled="formDisabled"
									:min="0"
								/>
							</el-form-item>
						</el-col>
						<el-col
							v-if="!formDisabled"
							:span="4"
						>
							<div>操作</div>
							<el-row type="flex">
								<el-button
									v-if="i > 0 || form.tariffPackageSubList.length > 1"
									type="danger"
									size="small"
									@click="handleFormDelete(i, 'tariffPackageSubList')"
									>删除</el-button
								>
								<el-button
									type="primary"
									size="small"
									@click="handleFormAdd(i, 'tariffPackageSubList')"
									>增加</el-button
								>
							</el-row>
						</el-col>
					</el-row>
				</el-form-item>
			</el-form>
			<div
				slot="footer"
				class="dialog-footer"
			>
				<el-button
					type="primary"
					@click="submitForm"
					>确 定</el-button
				>
				<el-button @click="cancel">取 消</el-button>
			</div>
		</el-dialog>

		<el-dialog
			:title="detailType === 'tariffPackageSubList' ? '购买权益' : '套餐权益'"
			:visible.sync="popoverVisable"
			width="30%"
			center
			append-to-body
		>
			<el-table
				v-if="detailType === 'tariffPackageSubList'"
				key="1"
				:data="detailData"
				border
			>
				<el-table-column
					property="tariffPackageSubName"
					label="名称"
				/>
				<el-table-column
					property="score"
					label="积分"
				/>
				<el-table-column
					property="cycle"
					label="时长"
				/>
				<el-table-column
					property="subMoney"
					label="价格"
				/>
			</el-table>
			<el-table
				v-else
				key="2"
				:data="detailData"
				border
			>
				<el-table-column
					property="content"
					label="描述"
				/>
				<el-table-column
					property="contains"
					label="是否包含"
				>
					<template slot-scope="scope">
						{{ scope.row.contains == 1 ? '是' : '否' }}
					</template>
				</el-table-column>
			</el-table>
			<span
				slot="footer"
				class="dialog-footer"
			>
				<el-button
					type="primary"
					@click="popoverVisable = false"
					>确 定</el-button
				>
			</span>
		</el-dialog>
	</div>
</template>

<script>
import {
	listAdminpackage,
	getAdminpackage,
	delAdminpackage,
	addAdminpackage,
	updateAdminpackage
} from '@/api/package/adminpackage'
import { validateNumber } from '@/utils/validate'
const equityDescriptionListValue = {
	content: '',
	contains: 1 // 1包含 2 不包含
}

const tariffPackageSubListValue = {
	tariffPackageSubName: '', // 名称
	score: '', // 积分
	subMoney: '', // 金额
	cycle: '' // 时长（天）
}

export default {
	name: 'Adminpackage',
	dicts: ['is_default', 'is_recommend', 'on_sale', 'max_num'],
	data() {
		return {
			// 遮罩层
			loading: true,
			// 选中数组
			ids: [],
			// 非单个禁用
			single: true,
			// 非多个禁用
			multiple: true,
			// 显示搜索条件
			showSearch: true,
			// 总条数
			total: 0,
			// 套餐表格数据
			adminpackageList: [],
			// 弹出层标题
			title: '',
			// 是否显示弹出层
			open: false,
			// 是否推荐时间范围
			daterangeCreateTime: [],
			// 查询参数
			queryParams: {
				pageNum: 1,
				pageSize: 10,
				name: null,
				money: null,
				taskMaxNum: null,
				taskMaxNumExists: null,
				discount: null,
				discountExists: null,
				picMaxSize: null,
				picMaxSizeExists: null,
				vipExists: null,
				onSale: null,
				describe: null,
				createTime: null,
				isDefault: null,
				isRecommend: null
			},
			// 表单参数
			form: {},
			// 表单校验
			rules: {
				name: [{ required: true, message: '套餐名称不能为空' }],
				money: [
					{ required: true, message: '套餐金额不能为空' },
					{ validator: this.validateNum }
				],
				taskMaxNum: [
					{ required: true, message: '最大任务数不能为空' },
					{ validator: this.validateNum }
				],
				discount: [
					{ required: true, message: '加油包折扣不能为空' },
					{ validator: this.validateNum }
				]
			},
			detailData: [],
			detailType: '',
			popoverVisable: false
		}
	},
	computed: {
		formDisabled() {
			return this.title == '修改套餐'
		}
	},
	created() {
		this.getList()
	},
	methods: {
		/** 查询套餐列表 */
		getList() {
			this.loading = true
			this.queryParams.params = {}
			if (this.daterangeCreateTime != null && this.daterangeCreateTime !== '') {
				this.queryParams.params['beginCreateTime'] = this.daterangeCreateTime[0]
				this.queryParams.params['endCreateTime'] = this.daterangeCreateTime[1]
			}
			listAdminpackage(this.queryParams).then((response) => {
				this.adminpackageList = response.data
				this.total = response.total
				this.loading = false
			})
		},
		// 取消按钮
		cancel() {
			this.open = false
			this.reset()
		},
		// 表单重置
		reset() {
			this.form = {
				tariffPackageId: null,
				name: null,
				money: null,
				taskMaxNum: null,
				taskMaxNumExists: null,
				discount: null,
				discountExists: null,
				picMaxSize: null,
				picMaxSizeExists: null,
				vip: null,
				vipExists: null,
				onSale: 0,
				describe: null,
				createBy: null,
				createTime: null,
				updateBy: null,
				updateTime: null,
				isDefault: 2,
				isRecommend: null,
				equityDescriptionList: [{ ...equityDescriptionListValue }],
				tariffPackageSubList: [{ ...tariffPackageSubListValue }]
			}
			this.resetForm('form')
		},
		/** 搜索按钮操作 */
		handleQuery() {
			this.queryParams.pageNum = 1
			this.getList()
		},
		/** 重置按钮操作 */
		resetQuery() {
			this.daterangeCreateTime = []
			this.resetForm('queryForm')
			this.handleQuery()
		},
		// 多选框选中数据
		handleSelectionChange(selection) {
			this.ids = selection.map((item) => item.tariffPackageId)
			this.single = selection.length !== 1
			this.multiple = !selection.length
		},
		/** 新增按钮操作 */
		handleAdd() {
			this.reset()
			this.open = true
			this.title = '添加套餐'
		},
		/** 修改按钮操作 */
		handleUpdate(row) {
			this.reset()
			const tariffPackageId = row.tariffPackageId || this.ids
			getAdminpackage(tariffPackageId).then((response) => {
				this.form = response.data
				this.open = true
				this.title = '修改套餐'
			})
		},
		/** 提交按钮 */
		submitForm() {
			this.$refs['form'].validate((valid) => {
				if (valid) {
					if (this.form.tariffPackageId != null) {
						updateAdminpackage(this.form).then((response) => {
							console.log(response, 'res')
							if (
								response.msg ===
								'当前不存在上架的默认套餐,请务必上架一个默认套餐'
							) {
								this.$confirm(response.msg, '提示', {
									confirmButtonText: '确定',
									showCancelButton: false,
									type: 'warning'
								})
							} else {
								this.$modal.msgSuccess('修改成功')
							}

							this.open = false
							this.getList()
						})
					} else {
						addAdminpackage(this.form).then((response) => {
							this.$modal.msgSuccess('新增成功')
							this.open = false
							this.getList()
						})
					}
				}
			})
		},
		/** 删除按钮操作 */
		handleDelete(row) {
			const tariffPackageIds = row.tariffPackageId || this.ids
			this.$modal
				.confirm('是否确认删除套餐编号为"' + tariffPackageIds + '"的数据项？')
				.then(function () {
					return delAdminpackage(tariffPackageIds)
				})
				.then(() => {
					this.getList()
					this.$modal.msgSuccess('删除成功')
				})
				.catch(() => {})
		},
		/** 导出按钮操作 */
		handleExport() {
			this.download(
				'package/adminpackage/export',
				{
					...this.queryParams
				},
				`adminpackage_${new Date().getTime()}.xlsx`
			)
		},
		/** 新增/编辑  */
		handleFormDelete(index, type) {
			this.form[type].splice(index, 1)
		},
		handleFormAdd(index, type) {
			const len = this.form[type].length

			if (type === 'equityDescriptionList' && len >= 5) {
				this.$modal.msgError('套餐权益最多添加5条')
				return
			}
			if (type === 'tariffPackageSubList' && len >= 4) {
				this.$modal.msgError('购买权益最多添加4条')
				return
			}
			this.form[type].push(
				type === 'equityDescriptionList'
					? { ...equityDescriptionListValue }
					: { ...tariffPackageSubListValue }
			)
		},
		handleCheckDetail(row, type) {
			const tariffPackageId = row.tariffPackageId || this.ids
			this.detailData = []
			getAdminpackage(tariffPackageId).then((response) => {
				this.detailData = response.data[type]
				this.detailType = type
				this.popoverVisable = true
			})
		},
		validateNum(rule, value, callback) {
			if (value === '') {
				callback(new Error('不能为空'))
			} else if (!validateNumber(value)) {
				callback(new Error('请输入数字'))
			} else if (value < 0) {
				callback(new Error('不能小于0'))
			} else {
				callback()
			}
		}
	}
}
</script>
<style lang="scss" scoped>
.form-item-div {
	font-weight: 700;
	border-bottom: 1px solid #e9e9e9;
	margin-bottom: 10px;
}
.row-view {
	padding: 10px;
}
.col-require {
	color: #ff4949;
}
</style>
