<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.OrdinalImgResultMapper">

    <resultMap type="OrdinalImgResult" id="OrdinalImgResultResult">
        <result property="imageId"    column="image_id"    />
        <result property="disassembleImgUrl"    column="disassemble_img_url"    />
        <result property="resWhiteImgUrl"    column="res_white_img_url"    />
        <result property="resImgUrl"    column="res_img_url"    />
        <result property="resSmallImgUrl"    column="res_small_img_url"    />
        <result property="follow"    column="follow"    />
        <result property="zan"    column="zan"    />
        <result property="progress"    column="progress"    />
        <result property="queue"    column="queue"    />
        <result property="taskOrdinalId"    column="task_ordinal_id"    />
        <result property="taskId"    column="task_id"    />
        <result property="userId"    column="user_id"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="originalImgUrl"    column="original_img_url"    />
        <result property="seed"    column="seed"    />
        <result property="ordinal"    column="ordinal"    />
        <result property="type"    column="type"    />
        <result property="extra"    column="extra"    />
        <result property="markImgUrl"    column="mark_img_url"    />
        <result property="hasUploaded"    column="has_uploaded"    />
    </resultMap>

    <sql id="selectOrdinalImgResultVo">
        select image_id, disassemble_img_url,res_white_img_url,res_img_url, res_small_img_url, follow, zan, progress, queue, task_ordinal_id, task_id, user_id, del_flag, create_time, update_time, create_by, update_by, original_img_url,seed,ordinal,type,extra,mark_img_url
        ,has_uploaded
        from t_ordinal_img_result
    </sql>

    <select id="selectOrdinalImgResultList" parameterType="OrdinalImgResult" resultMap="OrdinalImgResultResult">
        <include refid="selectOrdinalImgResultVo"/>
        <where>
            <if test="imageId != null "> and image_id = #{imageId}</if>
            <if test="resWhiteImgUrl != null  and resWhiteImgUrl != ''"> and res_white_img_url = #{resWhiteImgUrl}</if>
            <if test="disassembleImgUrl != null  and disassembleImgUrl != ''"> and disassemble_img_url = #{disassembleImgUrl}</if>
            <if test="resImgUrl != null  and resImgUrl != ''"> and res_img_url = #{resImgUrl}</if>
            <if test="resSmallImgUrl != null  and resSmallImgUrl != ''"> and res_small_img_url = #{resSmallImgUrl}</if>
            <if test="follow != null "> and follow = #{follow}</if>
            <if test="zan != null "> and zan = #{zan}</if>
            <if test="progress != null "> and progress = #{progress}</if>
            <if test="queue != null and queue != ''"> and queue = #{queue}</if>
            <if test="taskOrdinalId != null "> and task_ordinal_id = #{taskOrdinalId}</if>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="originalImgUrl != null  and originalImgUrl != ''"> and original_img_url = #{originalImgUrl}</if>
            <if test="seed != null "> and seed = #{seed}</if>
            <if test="ordinal != null "> and ordinal = #{ordinal}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="extra != null and extra != ''"> and extra = #{extra}</if>
            <if test="markImgUrl != null and markImgUrl != ''"> and mark_img_url = #{markImgUrl}</if>
        </where>
    </select>

    <select id="selectOrdinalImgResultByImageId" parameterType="String" resultMap="OrdinalImgResultResult">
        <include refid="selectOrdinalImgResultVo"/>
        where image_id = #{imageId}
    </select>

    <insert id="insertOrdinalImgResult" parameterType="OrdinalImgResult" keyProperty="imageId">
        insert into t_ordinal_img_result
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageId != null">image_id,</if>
            <if test="resImgUrl != null">res_img_url,</if>
            <if test="disassembleImgUrl != null">disassemble_img_url,</if>
            <if test="resWhiteImgUrl != null">res_white_img_url,</if>
            <if test="resSmallImgUrl != null">res_small_img_url,</if>
            <if test="follow != null">follow,</if>
            <if test="zan != null">zan,</if>
            <if test="progress != null">progress,</if>
            <if test="queue != null">queue,</if>
            <if test="taskOrdinalId != null">task_ordinal_id,</if>
            <if test="taskId != null">task_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="originalImgUrl != null">original_img_url,</if>
            <if test="seed != null">seed,</if>
            <if test="ordinal != null">ordinal,</if>
            <if test="type != null">type,</if>
            <if test="extra != null">extra,</if>
            <if test="markImgUrl != null">mark_img_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageId != null">#{imageId},</if>
            <if test="disassembleImgUrl != null">#{disassembleImgUrl},</if>
            <if test="resImgUrl != null">#{resImgUrl},</if>
            <if test="resWhiteImgUrl != null">#{resWhiteImgUrl},</if>
            <if test="resSmallImgUrl != null">#{resSmallImgUrl},</if>
            <if test="follow != null">#{follow},</if>
            <if test="zan != null">#{zan},</if>
            <if test="progress != null">#{progress},</if>
            <if test="queue != null">#{queue},</if>
            <if test="taskOrdinalId != null">#{taskOrdinalId},</if>
            <if test="taskId != null">#{taskId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="originalImgUrl != null">#{originalImgUrl},</if>
            <if test="seed != null">#{seed},</if>
            <if test="ordinal != null">#{ordinal},</if>
            <if test="type != null">#{type},</if>
            <if test="extra != null">#{extra},</if>
            <if test="markImgUrl != null">#{markImgUrl},</if>
         </trim>
    </insert>

    <update id="updateOrdinalImgResult" parameterType="OrdinalImgResult">
        update t_ordinal_img_result
        <trim prefix="SET" suffixOverrides=",">
            <if test="resImgUrl != null">res_img_url = #{resImgUrl},</if>
            <if test="disassembleImgUrl != null">disassemble_img_url= #{disassembleImgUrl},</if>
            <if test="resWhiteImgUrl != null">res_white_img_url = #{resWhiteImgUrl},</if>
            <if test="resSmallImgUrl != null">res_small_img_url = #{resSmallImgUrl},</if>
            <if test="follow != null">follow = #{follow},</if>
            <if test="zan != null">zan = #{zan},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="queue != null">queue = #{queue},</if>
            <if test="taskOrdinalId != null">task_ordinal_id = #{taskOrdinalId},</if>
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="originalImgUrl != null">original_img_url = #{originalImgUrl},</if>
            <if test="seed != null">seed = #{seed},</if>
            <if test="ordinal != null">ordinal = #{ordinal},</if>
            <if test="type != null">type = #{type},</if>
            <if test="extra != null">extra = #{extra},</if>
            <if test="markImgUrl != null">mark_img_url = #{markImgUrl},</if>
        </trim>
        where image_id = #{imageId}
    </update>

    <delete id="deleteOrdinalImgResultByImageId" parameterType="String">
        delete from t_ordinal_img_result where image_id = #{imageId}
    </delete>

    <delete id="deleteOrdinalImgResultByImageIds" parameterType="String">
        delete from t_ordinal_img_result where image_id in
        <foreach item="imageId" collection="array" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>

    <select id="listByUserIdTypes" resultMap="OrdinalImgResultResult">
        <include refid="selectOrdinalImgResultVo"/>
        <where>
            del_flag = 0
            <if test="ordinalImgResult.imageId != null "> and image_id = #{ordinalImgResult.imageId}</if>
            <if test="ordinalImgResult.resWhiteImgUrl != null  and ordinalImgResult.resWhiteImgUrl != ''"> and res_white_img_url = #{ordinalImgResult.resWhiteImgUrl}</if>
            <if test="ordinalImgResult.disassembleImgUrl != null  and ordinalImgResult.disassembleImgUrl != ''"> and disassemble_img_url = #{ordinalImgResult.disassembleImgUrl}</if>
            <if test="ordinalImgResult.resImgUrl != null  and ordinalImgResult.resImgUrl != ''"> and res_img_url = #{ordinalImgResult.resImgUrl}</if>
            <if test="ordinalImgResult.resSmallImgUrl != null  and ordinalImgResult.resSmallImgUrl != ''"> and res_small_img_url = #{ordinalImgResult.resSmallImgUrl}</if>
            <if test="ordinalImgResult.follow != null "> and follow = #{ordinalImgResult.follow}</if>
            <if test="ordinalImgResult.zan != null "> and zan = #{ordinalImgResult.zan}</if>
            <if test="ordinalImgResult.progress != null "> and progress = #{ordinalImgResult.progress}</if>
            <if test="ordinalImgResult.queue != null and ordinalImgResult.queue != ''"> and queue = #{ordinalImgResult.queue}</if>
            <if test="ordinalImgResult.taskOrdinalId != null "> and task_ordinal_id = #{ordinalImgResult.taskOrdinalId}</if>
            <if test="ordinalImgResult.taskId != null "> and task_id = #{ordinalImgResult.taskId}</if>
            <if test="ordinalImgResult.userId != null "> and user_id = #{ordinalImgResult.userId}</if>
            <if test="ordinalImgResult.originalImgUrl != null  and ordinalImgResult.originalImgUrl != ''"> and original_img_url = #{ordinalImgResult.originalImgUrl}</if>
            <if test="ordinalImgResult.seed != null "> and seed = #{ordinalImgResult.seed}</if>
            <if test="ordinalImgResult.ordinal != null "> and ordinal = #{ordinalImgResult.ordinal}</if>
            <if test="types != null ">
                and type in
                <foreach item="type" collection="types" open="(" separator="," close=")">
                    #{type}
                </foreach>
             </if>
            <if test="ordinalImgResult.extra != null and ordinalImgResult.extra != ''"> and extra = #{ordinalImgResult.extra}</if>
            <if test="ordinalImgResult.markImgUrl != null and ordinalImgResult.markImgUrl != ''"> and mark_img_url = #{ordinalImgResult.markImgUrl}</if>
        </where>
        order by create_time desc
    </select>
    <select id="listByTaskIds" resultMap="OrdinalImgResultResult">
        <include refid="selectOrdinalImgResultVo"/>
        <where>
            task_id in
            <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
                #{taskId}
            </foreach>
        </where>
    </select>
    <select id="selectImageResult" resultMap="OrdinalImgResultResult">
        select tto.task_ordinal_id ,tto.task_param ,tto.short_cut_desc,toir.image_id, CONCAT('https://image-task.xiaoaishop.com/',toir.disassemble_img_url) disassemble_img_url,
               CONCAT('https://image-task.xiaoaishop.com/',toir.res_white_img_url) res_white_img_url,
               CONCAT('https://image-task.xiaoaishop.com/',toir.res_img_url)   res_img_url,
               CONCAT('https://image-task.xiaoaishop.com/',toir.res_small_img_url) res_small_img_url,
               toir.follow, toir.zan, toir.progress, toir.queue, toir.task_ordinal_id, toir.task_id, toir.user_id, toir.del_flag, toir.create_time, toir.update_time, toir.create_by, toir.update_by,
               CONCAT('https://image-task.xiaoaishop.com/',toir.original_img_url) original_img_url,toir.seed,toir.ordinal,toir.type,toir.extra,toir.mark_img_url
        ,toir.has_uploaded

        from
            t_batch tb
                right join t_task tt on
                tb.batch_id = tt.batch_id
                right join t_task_ordinal tto on
                tto.task_id = tt.task_id
                right join t_ordinal_img_result toir on
                toir.task_id = tt.task_id
        where tb.batch_id=#{batchId} and progress=1
    </select>
    <select id="selectTypeByUrl" resultMap="OrdinalImgResultResult">
        <include refid="selectOrdinalImgResultVo"/>
        <where>
            res_img_url =#{url}
            or res_white_img_url =#{url}
        </where>
    </select>
    <select id="listByTaskOrdinalId" resultMap="OrdinalImgResultResult">
        <include refid="selectOrdinalImgResultVo"/>
        <where>
            task_ordinal_id = #{taskOrdinalId}
        </where>
    </select>

    <update id="batchUpdateImageFlags">
        UPDATE t_ordinal_img_result
        SET has_uploaded = #{hasUploaded}
        WHERE res_img_url IN
        <foreach collection="list" item="url" open="(" separator="," close=")">
            #{url.imageUrls}
        </foreach>
    </update>

<!--    <update id="batchUpdateImageFlags">
        UPDATE t_ordinal_img_result
        SET has_uploaded = CASE
        <foreach collection="list" item="item">
            WHEN res_img_url = #{item.imageUrls} OR res_white_img_url = #{item.imageUrls}
            THEN #{hasUploaded}
        </foreach>
        END
        WHERE
        <foreach collection="list" item="item" separator=" OR ">
            res_img_url = #{item.imageUrls} OR res_white_img_url = #{item.imageUrls}
        </foreach>
    </update>-->

    <update id="batchUpdateTaskFlags">
        UPDATE t_task
        SET has_uploaded = #{hasUploaded}
    WHERE   batch_id = #{batchId} and original_url IN
    <foreach collection="list" item="url" open="(" separator="," close=")">
        #{url.imageUrls}
    </foreach>
    </update>
</mapper>
