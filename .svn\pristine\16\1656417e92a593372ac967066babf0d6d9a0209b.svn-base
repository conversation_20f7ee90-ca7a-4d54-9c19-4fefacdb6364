package com.dataxai.web.mapper;

import java.util.List;
import com.dataxai.web.domain.Favorite;

/**
 * 图片收藏Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-13
 */
public interface FavoriteMapper 
{
    /**
     * 查询图片收藏
     * 
     * @param favoriteId 图片收藏主键
     * @return 图片收藏
     */
    public Favorite selectFavoriteByFavoriteId(String favoriteId);

    /**
     * 查询图片收藏
     *
     * @param userId 用户id
     * @return 图片收藏集合
     */
    public List<Favorite> selectFavoriteListByUserId(Long userId);

    /**
     * 查询图片收藏列表
     * 
     * @param favorite 图片收藏
     * @return 图片收藏集合
     */
    public List<Favorite> selectFavoriteList(Favorite favorite);

    /**
     * 新增图片收藏
     * 
     * @param favorite 图片收藏
     * @return 结果
     */
    public int insertFavorite(Favorite favorite);

    /**
     * 修改图片收藏
     * 
     * @param favorite 图片收藏
     * @return 结果
     */
    public int updateFavorite(Favorite favorite);

    /**
     * 删除图片收藏
     * 
     * @param favoriteId 图片收藏主键
     * @return 结果
     */
    public int deleteFavoriteByFavoriteId(Long favoriteId);

    /**
     * 批量删除图片收藏
     * 
     * @param favoriteIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFavoriteByFavoriteIds(Long[] favoriteIds);
}
