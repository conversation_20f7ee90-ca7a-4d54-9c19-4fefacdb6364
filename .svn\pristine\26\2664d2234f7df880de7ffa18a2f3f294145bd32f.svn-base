<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.TaskMapper">

    <resultMap type="Task" id="TaskResult">
        <result property="taskId"    column="task_id"    />
        <result property="batchId"    column="batch_id"    />
        <result property="taskName"    column="task_name"    />
        <result property="type"    column="type"    />
        <result property="userId"    column="user_id"    />
        <result property="referedTaskId"    column="refered_task_id"    />
        <result property="status"    column="status"    />
        <result property="segData"    column="seg_data"    />
        <result property="executeTime"    column="execute_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="originalUrl"    column="original_url"    />
        <result property="thumbnailUrl"    column="thumbnail_url"    />
        <result property="proceing"    column="proceing"    />
        <result property="fourK"    column="four_k"    />
        <result property="descStatus"    column="desc_status"    />
        <result property="description"    column="description"    />
        <result property="frameCoordinates"    column="frame_coordinates"    />
        <result property="markImgUrl"    column="mark_img_url"    />
        <result property="hasUploaded"    column="has_uploaded"    />
    </resultMap>

    <resultMap id="TaskTaskOrdinalResult" type="Task" extends="TaskResult">
        <collection property="taskOrdinalList" notNullColumn="sub_task_ordinal_id" javaType="java.util.List" resultMap="TaskOrdinalResult" />
    </resultMap>

    <resultMap type="TaskOrdinal" id="TaskOrdinalResult">
        <result property="taskOrdinalId"    column="sub_task_ordinal_id"    />
        <result property="name"    column="sub_name"    />
        <result property="taskId"    column="sub_task_id"    />
        <result property="type"    column="sub_type"    />
        <result property="userId"    column="sub_user_id"    />
        <result property="referedTaskId"    column="sub_refered_task_id"    />
        <result property="referedTaskOrdinalId"    column="sub_refered_task_ordinal_id"    />
        <result property="ordinal"    column="sub_ordinal"    />
        <result property="status"    column="sub_status"    />
        <result property="originImgUrl"    column="sub_origin_img_url"    />
        <result property="markImgUrl"    column="sub_mark_img_url"    />
        <result property="desType"    column="sub_des_type"    />
        <result property="shortCutDesc"    column="sub_short_cut_desc"    />
        <result property="modelPrompt"    column="sub_model_prompt"    />
        <result property="modelCharacterId"    column="sub_model_character_id"    />
        <result property="modelSex"    column="sub_model_sex"    />
        <result property="modelAge"    column="sub_model_age"    />
        <result property="modelSkin"    column="sub_model_skin"    />
        <result property="modelExpression"    column="sub_model_expression"    />
        <result property="modelTemperament"    column="sub_model_temperament"    />
        <result property="modelImgId"    column="sub_model_img_id"    />
        <result property="modelUploadUrl"    column="sub_model_upload_url"    />
        <result property="sceneType"    column="sub_scene_type"    />
        <result property="sceneTypeStr"    column="sub_scene_type_str"    />
        <result property="sceneCategoryLarge"    column="sub_scene_category_large"    />
        <result property="sceneCategorySmall"    column="sub_scene_category_small"    />
        <result property="sceneImgId"    column="sub_scene_img_id"    />
        <result property="sceneId"    column="sub_scene_id"    />
        <result property="goodsSceneCategory"    column="sub_goods_scene_category"    />
        <result property="goodsSceneCategorySub"    column="sub_goods_scene_category_sub"    />
        <result property="weight"    column="sub_weight"    />
        <result property="scenePrompt"    column="sub_scene_prompt"    />
        <result property="forward"    column="sub_forward"    />
        <result property="reverse"    column="sub_reverse"    />
        <result property="executeTime"    column="sub_execute_time"    />
        <result property="createTime"    column="sub_create_time"    />
        <result property="updateTime"    column="sub_update_time"    />
        <result property="resultImgUrls"    column="sub_result_img_urls"    />
        <result property="delFlag"    column="sub_del_flag"    />
        <result property="seed"    column="seed"    />
        <result property="realPerson"    column="real_person"    />
        <result property="repair"    column="repair"    />
        <result property="optionalCharacter"    column="optional_character"    />
        <result property="optionalCharacterIds"    column="optional_character_ids"    />
        <result property="frameCoordinates"    column="frame_coordinates"    />
        <result property="taskParam"    column="task_param"    />
    </resultMap>

    <sql id="selectTaskVo">
        select task_id, batch_id ,task_name, type, user_id, refered_task_id, status, seg_data, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url,thumbnail_url from t_task
    </sql>

    <sql id="selectTaskVoNoSegData">
        select task_id, batch_id ,task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url,thumbnail_url
        ,has_uploaded
        from t_task
    </sql>

    <select id="selectTaskListByUserId" parameterType="Long" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
        </where>
        and del_flag = 0
    </select>

    <select id="selectTaskList" parameterType="Task" resultMap="TaskResult">
        <include refid="selectTaskVoNoSegData"/>
        <where>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="batchId != null  and batchId != ''"> and batch_id =#{batchId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="referedTaskId != null "> and refered_task_id = #{referedTaskId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="segData != null  and segData != ''"> and seg_data = #{segData}</if>
            <if test="executeTime != null "> and execute_time = #{executeTime}</if>
            <if test="originalUrl != null  and originalUrl != ''"> and original_url = #{originalUrl}</if>
            <if test="proceing != null "> and proceing = #{proceing}</if>
        </where>
        and del_flag = 0
        and four_k = 0
        order by update_time desc
    </select>

    <select id="selectTaskListAddUrl" parameterType="Task" resultMap="TaskResult">
        select task_id, batch_id ,task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, CONCAT('https://image-task.xiaoaishop.com/', original_url) original_url,
        proceing,four_k,desc_status,description,frame_coordinates,mark_img_url,CONCAT('https://image-task.xiaoaishop.com/',thumbnail_url) thumbnail_url
        ,has_uploaded
        from t_task
        <where>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="batchId != null  and batchId != ''"> and batch_id =#{batchId}</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="referedTaskId != null "> and refered_task_id = #{referedTaskId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="segData != null  and segData != ''"> and seg_data = #{segData}</if>
            <if test="executeTime != null "> and execute_time = #{executeTime}</if>
            <if test="originalUrl != null  and originalUrl != ''"> and original_url = #{originalUrl}</if>
            <if test="proceing != null "> and proceing = #{proceing}</if>
        </where>
        and del_flag = 0
        and four_k = 0
        order by update_time desc
    </select>

<!--    a.seg_data, 字段单独处理一个接口获取 -->
    <select id="selectTaskByUserIdAndTaskId" parameterType="map" resultMap="TaskTaskOrdinalResult">
        select a.task_id, a.task_name, a.type, a.user_id, a.refered_task_id, a.status, a.execute_time, a.create_time, a.update_time, a.del_flag,a.original_url,a.proceing,a.four_k,a.desc_status,a.description,a.frame_coordinates,a.mark_img_url,
        b.task_ordinal_id as sub_task_ordinal_id, b.name as sub_name, b.task_id as sub_task_id, b.type as sub_type, b.user_id as sub_user_id, b.refered_task_id as sub_refered_task_id, b.refered_task_ordinal_id as sub_refered_task_ordinal_id, b.ordinal as sub_ordinal, b.status as sub_status, b.origin_img_url as sub_origin_img_url, b.mark_img_url as sub_mark_img_url, b.des_type as sub_des_type, b.short_cut_desc as sub_short_cut_desc, b.model_prompt as sub_model_prompt, b.model_character_id as sub_model_character_id, b.model_sex as sub_model_sex, b.model_age as sub_model_age, b.model_skin as sub_model_skin, b.model_expression as sub_model_expression, b.model_temperament as sub_model_temperament, b.model_img_id as sub_model_img_id, b.model_upload_url as sub_model_upload_url, b.scene_type as sub_scene_type, b.scene_type_str as sub_scene_type_str, b.scene_category_large as sub_scene_category_large, b.scene_category_small as sub_scene_category_small, b.scene_img_id as sub_scene_img_id, b.scene_id as sub_scene_id, b.goods_scene_category as sub_goods_scene_category, b.goods_scene_category_sub as sub_goods_scene_category_sub, b.weight as sub_weight, b.scene_prompt as sub_scene_prompt, b.forward as sub_forward, b.reverse as sub_reverse, b.execute_time as sub_execute_time, b.create_time as sub_create_time, b.update_time as sub_update_time, b.result_img_urls as sub_result_img_urls, b.del_flag as sub_del_flag,b.seed,b.real_person,
        b.optional_character,b.optional_character_ids,b.repair,
        b.frame_coordinates,b.task_param
        from t_task a
        left join t_task_ordinal b on b.task_id = a.task_id and a.del_flag = b.del_flag and a.user_id = b.user_id
        where a.task_id = #{taskId} and a.user_id = #{userId} and a.del_flag = 0
        order by b.ordinal desc
    </select>

    <!--    a.seg_data, 字段单独处理一个接口获取 -->
    <select id="selectTaskByTaskId" parameterType="String" resultMap="TaskTaskOrdinalResult">
        select a.task_id, a.task_name, a.type, a.user_id, a.refered_task_id, a.status, a.execute_time, a.create_time, a.update_time, a.del_flag,a.original_url,a.proceing,a.four_k,a.desc_status,a.description,a.frame_coordinates,a.mark_img_url,
        b.task_ordinal_id as sub_task_ordinal_id, b.name as sub_name, b.task_id as sub_task_id, b.type as sub_type, b.user_id as sub_user_id, b.refered_task_id as sub_refered_task_id, b.refered_task_ordinal_id as sub_refered_task_ordinal_id, b.ordinal as sub_ordinal, b.status as sub_status, b.origin_img_url as sub_origin_img_url, b.mark_img_url as sub_mark_img_url, b.des_type as sub_des_type, b.short_cut_desc as sub_short_cut_desc, b.model_prompt as sub_model_prompt, b.model_character_id as sub_model_character_id, b.model_sex as sub_model_sex, b.model_age as sub_model_age, b.model_skin as sub_model_skin, b.model_expression as sub_model_expression, b.model_temperament as sub_model_temperament, b.model_img_id as sub_model_img_id, b.model_upload_url as sub_model_upload_url, b.scene_type as sub_scene_type, b.scene_type_str as sub_scene_type_str, b.scene_category_large as sub_scene_category_large, b.scene_category_small as sub_scene_category_small, b.scene_img_id as sub_scene_img_id, b.scene_id as sub_scene_id, b.goods_scene_category as sub_goods_scene_category, b.goods_scene_category_sub as sub_goods_scene_category_sub, b.weight as sub_weight, b.scene_prompt as sub_scene_prompt, b.forward as sub_forward, b.reverse as sub_reverse, b.execute_time as sub_execute_time, b.create_time as sub_create_time, b.update_time as sub_update_time, b.result_img_urls as sub_result_img_urls, b.del_flag as sub_del_flag,b.seed,b.real_person,b.optional_character,b.optional_character_ids,
        b.frame_coordinates,b.task_param
        from t_task a
        left join t_task_ordinal b on b.task_id = a.task_id
        where a.task_id = #{taskId} and a.del_flag = 0
    </select>

    <select id="selectTaskInfoByTaskId" parameterType="String" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        <where>
            <if test="userId != null "> and task_id = #{taskId}</if>
        </where>
        and del_flag = 0
    </select>

    <insert id="insertTask" parameterType="Task" keyProperty="taskId">
        insert into t_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="batchId != null">batch_id,</if>
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="type != null">type,</if>
            <if test="userId != null">user_id,</if>
            <if test="referedTaskId != null">refered_task_id,</if>
            <if test="status != null">status,</if>
            <if test="segData != null and segData != ''">seg_data,</if>
            <if test="executeTime != null">execute_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="originalUrl != null and originalUrl != ''">original_url,</if>
            <if test="thumbnailUrl != null and thumbnailUrl != ''">thumbnail_url,</if>
            <if test="proceing != null">proceing,</if>
            <if test=" fourK != null">four_k,</if>
            <if test="descStatus != null"> desc_status,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="frameCoordinates != null and frameCoordinates != ''">frame_coordinates ,</if>
            <if test="markImgUrl  != null and markImgUrl != ''">mark_img_url ,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="batchId != null">#{batchId},</if>
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="type != null">#{type},</if>
            <if test="userId != null">#{userId},</if>
            <if test="referedTaskId != null">#{referedTaskId},</if>
            <if test="status != null">#{status},</if>
            <if test="segData != null and segData != ''">#{segData},</if>
            <if test="executeTime != null">#{executeTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="originalUrl != null and originalUrl != ''">#{originalUrl},</if>
            <if test="thumbnailUrl != null and thumbnailUrl != ''">#{thumbnailUrl},</if>
            <if test="proceing != null ">#{proceing},</if>
            <if test=" fourK != null">#{fourK},</if>
            <if test=" descStatus != null">#{descStatus},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="frameCoordinates != null and frameCoordinates  != ''">#{frameCoordinates},</if>
            <if test="markImgUrl != null and markImgUrl != ''">#{markImgUrl}</if>
         </trim>
    </insert>


    <insert id="insertBatchTask" parameterType="java.util.List">
        insert into t_task
        (
            task_id,
            batch_id,
            task_name,
            type,
            user_id,
            refered_task_id,
            status,
            seg_data,
            execute_time,
            create_time,
            update_time,
            del_flag,
            original_url,
            thumbnail_url,
            proceing,
            four_k,
            desc_status,
            description,
            frame_coordinates,
            mark_img_url
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.taskId},
            #{item.batchId},
            #{item.taskName},
            #{item.type},
            #{item.userId},
            #{item.referedTaskId},
            #{item.status},
            #{item.segData},
            #{item.executeTime},
            #{item.createTime},
            #{item.updateTime},
            #{item.delFlag},
            #{item.originalUrl},
            #{item.thumbnailUrl},
            #{item.proceing},
            #{item.fourK},
            #{item.descStatus},
            #{item.description},
            #{item.frameCoordinates},
            #{item.markImgUrl}
            )
        </foreach>
    </insert>

    <update id="updateTask" parameterType="Task">
        update t_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="batchId != null and batchId != ''">batch_id = #{batchId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="referedTaskId != null">refered_task_id = #{referedTaskId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="segData != null and segData != ''">seg_data = #{segData},</if>
            <if test="executeTime != null">execute_time = #{executeTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="originalUrl != null and originalUrl != ''">original_url = #{originalUrl},</if>
            <if test="proceing != null ">proceing = #{proceing},</if>
            <if test="descStatus != null ">desc_status = #{descStatus},</if>
            <if test="description != null and description != '' ">description = #{description},</if>
            <if test="frameCoordinates  != null and frameCoordinates  != ''">frame_coordinates =#{frameCoordinates},</if>
            <if test="markImgUrl  != null and markImgUrl  != ''">mark_img_url =#{markImgUrl}</if>
        </trim>
        where task_id = #{taskId} and del_flag = 0
    </update>

    <update id="logicDeleteTaskByTaskIds" parameterType="map">
        update t_task
        set del_flag = 1
        where
        user_id = #{userId}
        and
        task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <delete id="deleteTaskByTaskId" parameterType="String">
        delete from t_task where task_id = #{taskId}
    </delete>

    <delete id="deleteTaskByTaskIds" parameterType="String">
        delete from t_task where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <update id="logicDeleteTaskOrdinalByTaskIds" parameterType="map">
        update t_task_ordinal
        set del_flag = 1
        where
        user_id = #{userId}
        and
        task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <delete id="deleteTaskOrdinalByTaskIds" parameterType="String">
        delete from t_task_ordinal where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <delete id="deleteTaskOrdinalByTaskId" parameterType="String">
        delete from t_task_ordinal where task_id = #{taskId}
    </delete>

    <insert id="batchTaskOrdinal">
        insert into t_task_ordinal( task_ordinal_id, name, task_id, type, user_id, refered_task_id, refered_task_ordinal_id, ordinal, status, origin_img_url, mark_img_url, des_type, short_cut_desc, model_prompt, model_character_id, model_sex, model_age, model_skin, model_expression, model_temperament, model_img_id, model_upload_url, scene_type, scene_type_str, scene_category_large, scene_category_small, scene_img_id, scene_id, goods_scene_category, goods_scene_category_sub, weight, scene_prompt, forward, reverse, execute_time, create_time, update_time, result_img_urls, del_flag,seed) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.taskOrdinalId}, #{item.name}, #{item.taskId}, #{item.type}, #{item.userId}, #{item.referedTaskId}, #{item.referedTaskOrdinalId}, #{item.ordinal}, #{item.status}, #{item.originImgUrl}, #{item.markImgUrl}, #{item.desType}, #{item.shortCutDesc}, #{item.modelPrompt}, #{item.modelCharacterId}, #{item.modelSex}, #{item.modelAge}, #{item.modelSkin}, #{item.modelExpression}, #{item.modelTemperament}, #{item.modelImgId}, #{item.modelUploadUrl}, #{item.sceneType}, #{item.sceneTypeStr}, #{item.sceneCategoryLarge}, #{item.sceneCategorySmall}, #{item.sceneImgId}, #{item.sceneId}, #{item.goodsSceneCategory}, #{item.goodsSceneCategorySub}, #{item.weight}, #{item.scenePrompt}, #{item.forward}, #{item.reverse}, #{item.executeTime}, #{item.createTime}, #{item.updateTime}, #{item.resultImgUrls}, #{item.delFlag},#{item.seed})
        </foreach>
    </insert>
    <insert id="newTaskExecute">
        select count(*) from t_task
        where
            del_flag = 0 and type in(4,6,7,8,9,10,11,12) and
            status =#{status}
    </insert>

    <select id="selectTaskSegData" parameterType="Task" resultMap="TaskResult">
        select a.task_id, a.type,a.original_url,a.seg_data
        from t_task a
        where a.task_id = #{taskId} and a.del_flag = 0
    </select>

    <select id="selectTaskListByTaskStatus" parameterType="map" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        where
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
        and del_flag = 0
    </select>

    <update id="resetTask" parameterType="Task">
        update t_task
        set
        seg_data = #{segData},proceing = #{proceing},original_url = #{originalUrl},status = #{status},update_time = #{updateTime}
        where task_id = #{taskId} and del_flag = 0
    </update>
    <update id="updateOrdinalUrlSub">
        UPDATE t_task
        SET original_url =
                CASE
                    WHEN original_url LIKE CONCAT(#{originImgUrl}, ',%') THEN SUBSTRING(original_url, LENGTH(#{originImgUrl}) + 2)
                    WHEN original_url = #{originImgUrl} THEN ''
                    WHEN original_url LIKE CONCAT('%', #{originImgUrl}, ',%') THEN REPLACE(original_url, CONCAT(#{originImgUrl}, ','), '')
                    WHEN original_url LIKE CONCAT('%', ',', #{originImgUrl}) THEN LEFT(original_url, LENGTH(original_url) - LENGTH(#{originImgUrl}) - 1)
            WHEN RIGHT(original_url, LENGTH(#{originImgUrl})) = #{originImgUrl} THEN LEFT(original_url, LENGTH(original_url) - LENGTH(#{originImgUrl}))
            ELSE original_url
        END
        WHERE task_id = #{taskId};

    </update>
    <update id="logicDeleteImageByTaskIds">
        update t_ordinal_img_result
        set del_flag = 1
        where
        user_id = #{userId}
        and
        task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>

    </update>
    <update id="updateTaskStatus"  parameterType="Task">
        update t_task set status  =#{status} where batch_id =#{batchId}
    </update>

    <select id="selectTaskListByTaskStatusAndUserId" parameterType="map" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        where
        user_id = #{userId} and del_flag = 0 and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <select id="selectTaskListByTaskStatusAndUserIdAndTaskId" parameterType="map" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        where
        user_id = #{userId} and del_flag = 0 and task_id = #{taskId} and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <select id="getTaskListByTaskStatus" parameterType="map" resultMap="TaskResult">
        select task_id,batch_id, task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url from t_task
        where
        four_k = #{fourK} and del_flag = 0 and batch_id = 0 and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
        <if test="typeList != null ">
            and type in
            <foreach item="type" collection="typeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by create_time
--         and type in (6,7,8,9,10,11,12)
        limit #{myPageNum} , #{myPageSize}
    </select>

    <select id="getTaskCountByTaskStatus" parameterType="map" resultType="integer">
        select count(*) from t_task
        where
        four_k = #{fourK} and del_flag = 0 and batch_id = 0 and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
        <if test=" typeList != null ">
        and type in
            <foreach item="type" collection="typeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by create_time
--         and type in (6,7,8,9,10,11,12)
    </select>
    <select id="getTextureTaskCount" resultType="java.lang.Integer">
        select count(*) from t_task
        where
        four_k = #{fourK} and del_flag = 0 and type = 4 and
        status =#{status}

    </select>
    <select id="getTextureTaskList" parameterType="map" resultMap="TaskResult">
        select task_id, task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url from t_task
        where
        four_k = #{fourK} and del_flag = 0 and type = 4 and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
        limit #{myPageNum} , #{myPageSize}
    </select>

    <select id="getTaskSegCount" parameterType="map" resultType="integer">
        select count(*) from t_task
        where
        four_k = #{fourK} and del_flag = 0 and
        type in (0,1,5) and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
    </select>

    <select id="getSegTaskList" parameterType="map" resultMap="TaskResult">
        select task_id, task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url from t_task
        where
        four_k = #{fourK} and del_flag = 0 and
        type in (0,1,5) and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
        limit #{myPageNum} , #{myPageSize}
    </select>
    <select id="selectTextureAddTaskCount" resultType="java.lang.Integer">
        select count(*) from t_task
        where
            four_k = #{fourK} and del_flag = 0 and type = 4 and
            status =#{status} and desc_status = 0
    </select>
    <select id="selectTextureAddTaskList" parameterType="map" resultMap="TaskResult">
        select task_id, task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url from t_task
        where
        four_k = #{fourK} and del_flag = 0 and type = 4 and
        status =#{status} and desc_status = 0
        limit #{myPageNum} , #{myPageSize}
    </select>
<!--    根据用户ID 和类型查询前10条-->
    <select id="selectByUserId" resultMap="TaskTaskOrdinalResult">
        select a.task_id, a.task_name, a.type, a.user_id, a.refered_task_id, a.status, a.execute_time, a.create_time, a.update_time, a.del_flag,a.original_url,a.proceing,
        b.task_ordinal_id as sub_task_ordinal_id, b.name as sub_name, b.task_id as sub_task_id, b.type as sub_type, b.user_id as sub_user_id, b.refered_task_id as sub_refered_task_id, b.refered_task_ordinal_id as sub_refered_task_ordinal_id, b.ordinal as sub_ordinal, b.status as sub_status, b.origin_img_url as sub_origin_img_url, b.mark_img_url as sub_mark_img_url, b.des_type as sub_des_type, b.short_cut_desc as sub_short_cut_desc, b.model_prompt as sub_model_prompt, b.model_character_id as sub_model_character_id, b.model_expression as sub_model_expression, b.model_temperament as sub_model_temperament, b.model_img_id as sub_model_img_id, b.model_upload_url as sub_model_upload_url, b.scene_type as sub_scene_type, b.scene_type_str as sub_scene_type_str, b.scene_category_large as sub_scene_category_large, b.scene_category_small as sub_scene_category_small, b.scene_img_id as sub_scene_img_id, b.scene_prompt as sub_scene_prompt, b.result_img_urls as sub_result_img_urls, b.del_flag as sub_del_flag,
        b.task_param,b.create_time sub_create_time, b.update_time sub_update_time
        from t_task a
        left join t_task_ordinal b on b.task_id = a.task_id and a.del_flag = b.del_flag and a.user_id = b.user_id
        where a.user_id = #{userId} and a.del_flag = 0 and batch_id =0
        <if test="statuses != null ">
          and a.status in
            <foreach item="status" collection="statuses" open="(" separator="," close=")">
                #{status}
          </foreach>
          </if>
        and a.type in
        <foreach item="type" collection="types" open="(" separator="," close=")">
            #{type}
        </foreach>
        order by a.update_time desc, b.ordinal desc
    </select>
    <select id="selectTaskByTaskIds" resultType="com.dataxai.web.domain.Task">
        select a.task_id, a.task_name, a.type, a.user_id, a.refered_task_id, a.status, a.execute_time, a.create_time, a.update_time, a.del_flag,a.original_url,a.proceing,a.four_k,a.desc_status,a.description,a.frame_coordinates,a.mark_img_url
        from t_task a
        where a.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
        and a.user_id =#{userId}
    </select>
    <select id="selectNewTaskExecute" parameterType="map" resultMap="TaskResult">
        select task_id, task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url from t_task
        where
        del_flag = 0 and type in(4,6,7,8,9,10,11,12) and
            status =#{status}
            limit #{myPageNum} , #{myPageSize}
    </select>
    <select id="selectStatus" resultType="java.lang.Integer">
        SELECT t2.status AS status
        FROM t_task t1
                 JOIN t_task t2 ON t1.batch_id = t2.batch_id
        WHERE t1.task_id =#{taskId}
    </select>
    <select id="getTaskBatchCount" parameterType="map" resultType="integer">
        select count(*) from t_task
        where
        four_k = #{fourK} and del_flag = 0 and batch_id != 0 and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
        <if test=" typeList != null ">
            and type in
            <foreach item="type" collection="typeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by create_time

    </select>
    <select id="getBatchTaskList" parameterType="map" resultMap="TaskResult">
        select task_id,batch_id, task_name, type, user_id, refered_task_id, status, execute_time, create_time, update_time, del_flag, original_url,proceing,four_k,desc_status,description,frame_coordinates,mark_img_url from t_task
        where
        four_k = #{fourK} and del_flag = 0 and batch_id != 0 and
        status in
        <foreach item="status" collection="taskStatus" open="(" separator="," close=")">
            #{status}
        </foreach>
        <if test="typeList != null ">
            and type in
            <foreach item="type" collection="typeList" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        order by create_time
        limit #{myPageNum} , #{myPageSize}
    </select>
    <select id="selectTypeByUrl" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        <where>
            original_url=#{url}
            and del_flag = 0
        </where>
    </select>

    <select id="selectTypeByUrlAndBatchId" resultMap="TaskResult">
        <include refid="selectTaskVo"/>
        <where>
            original_url=#{url}
            and batch_id = #{batchId}
            and del_flag = 0
        </where>
    </select>


    <!-- 动态批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_task (
        task_id,
        task_name,
        type,
        user_id,
        refered_task_id,
        status,
        seg_data,
        execute_time,
        create_time,
        update_time,
        del_flag,
        original_url,
        proceing,
        four_k,
        desc_status,
        description,
        frame_coordinates,
        mark_img_url
        ) VALUES
        <foreach collection="list" item="task" separator=",">
            (
            #{task.taskId},
            #{task.taskName},
            #{task.type},
            #{task.userId},
            #{task.referedTaskId},
            #{task.status},
            #{task.segData},
            #{task.executeTime},
            #{task.createTime},
            #{task.updateTime},
            #{task.delFlag},
            #{task.originalUrl},
            #{task.proceing},
            #{task.fourK},
            #{task.descStatus},
            #{task.description},
            #{task.frameCoordinates},
            #{task.markImgUrl}
            )
        </foreach>
    </insert>

    <select id="selectTaskQueueNumByUserIdAndTaskId" parameterType="map" resultType="java.lang.Integer">
        select COUNT(1) from t_task a
        left join t_task_ordinal b on b.task_id = a.task_id and a.del_flag = b.del_flag and a.user_id = b.user_id
        where a.batch_id in('',0) and a.`status` = 4 and a.del_flag = 0
        and a.task_id &lt; #{taskId}
        <choose>
            <when test="flag == 1">
                AND a.type IN (0, 1, 2, 3, 4, 5)
            </when>
            <otherwise>
                AND a.type NOT IN (0, 1, 2, 3, 4, 5)
            </otherwise>
        </choose>
    </select>
    <select id="selectByTaskIds" resultType="com.dataxai.web.domain.Task">
        select *
        from t_task
        where task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
         and status = 4
    </select>
    <select id="getWaitList" parameterType="map" resultType="java.util.Map">
        SELECT
            t.batch_id,
            t.task_id,
            a.task_ordinal_id,
            a.type,
            a.`status`,
            CASE
                WHEN a.task_param IS NULL THEN
                0 ELSE JSON_EXTRACT( a.task_param, '$.imageNumber' )
            END AS imageNumber,
            a.create_time,
            gpu.gpu_count,
            gpu.remark,
            gpu.task_time,
            gpu.load_model_time,
            ((CASE
                WHEN a.task_param IS NULL THEN
                0 ELSE JSON_EXTRACT( a.task_param, '$.imageNumber' )
            END) * gpu.task_time + gpu.load_model_time) as totalTime
        FROM
        `t_task_ordinal` a
        JOIN t_task t ON t.task_id = a.task_id
        <if test="isBatch == 1">
            JOIN t_batch b on b.batch_id = t.batch_id
        </if>
        LEFT JOIN (
            SELECT
                sgg.remark,
                sggt.task_type,
                sggt.task_time,
                sgg.gpu_count,
                sggt.load_model_time
            FROM sys_gpu_group_tasks sggt
            JOIN sys_gpu_group sgg ON sgg.id = sggt.sys_gpu_group_id
            where 1=1
        <choose>
            <when test="isBatch == 1">
                and sgg.remark = 'AI-BATCH-POD'
            </when>
            <otherwise>
                and sgg.remark = 'AI-POD'
            </otherwise>
        </choose>
        ) gpu ON gpu.task_type = a.type
        WHERE a.del_flag = 0
        <choose>
            <when test="isBatch == 1">
                AND t.batch_id not IN ( '0', '' )
                and a.`status` in (4,5)
            </when>
            <otherwise>
                AND t.batch_id IN ( '0', '' )
                and a.`status` = 4
            </otherwise>
        </choose>
        AND a.create_time &lt; #{createTime}
        AND a.type IN (
            SELECT DISTINCT
                sggt.task_type
            FROM
            sys_gpu_group_tasks sggt
            WHERE
            EXISTS (
                SELECT
                1
                FROM
                sys_gpu_group_tasks filter_tasks
                JOIN sys_gpu_group sgg ON filter_tasks.sys_gpu_group_id = sgg.id
                WHERE filter_tasks.sys_gpu_group_id = sggt.sys_gpu_group_id
                AND filter_tasks.task_type = #{type}
                <choose>
                    <when test="isBatch == 1">
                        and sgg.remark = 'AI-BATCH-POD'
                    </when>
                    <otherwise>
                        and sgg.remark = 'AI-POD'
                    </otherwise>
                </choose>
                )
        )
    </select>
    <select id="getGpuCount" parameterType="map" resultType="java.lang.Integer">
        SELECT
        sgg.gpu_count
        FROM sys_gpu_group_tasks sggt
        JOIN sys_gpu_group sgg ON sgg.id = sggt.sys_gpu_group_id
        where sggt.task_type = #{type}
        <choose>
            <when test="isBatch == 1">
                and sgg.remark = 'AI-BATCH-POD'
            </when>
            <otherwise>
                and sgg.remark = 'AI-POD'
            </otherwise>
        </choose>
    </select>

    <select id="getTaskByPush" resultMap="TaskResult">
        select a.task_id,
               a.batch_id ,
               a.task_name,
               a.type,
               a.user_id,
               a.refered_task_id,
               a.status,
               a.seg_data,
               a.execute_time,
               a.create_time,
               a.update_time,
               a.del_flag,
               a.original_url,
               a.proceing,
               a.four_k,
               a.desc_status,
               a.description,
               a.frame_coordinates,
               a.mark_img_url,
               a.thumbnail_url,
               tp.id as pushId
        from t_task a
        join t_task_push tp on tp.task_id = a.task_id
        where tp.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="getTaskByIds" resultMap="TaskResult">
        select a.task_id,
        a.batch_id ,
        a.task_name,
        a.type,
        a.user_id,
        a.refered_task_id,
        a.status,
        a.seg_data,
        a.execute_time,
        a.create_time,
        a.update_time,
        a.del_flag,
        a.original_url,
        a.proceing,
        a.four_k,
        a.desc_status,
        a.description,
        a.frame_coordinates,
        a.mark_img_url,
        a.thumbnail_url
        from t_task a
        where a.task_id in
        <foreach item="taskId" collection="taskIds" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <select id="getTaskPush" resultType="com.dataxai.web.domain.TaskPush">
        select
            id,
            task_id as taskId,
            type
        from t_task_push
    </select>
</mapper>
