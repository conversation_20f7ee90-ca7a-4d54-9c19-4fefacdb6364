package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.TBaseinfoAgreement;

/**
 * 用户服务协议Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
public interface ITBaseinfoAgreementService 
{
    /**
     * 查询用户服务协议
     * 
     * @param id 用户服务协议主键
     * @return 用户服务协议
     */
    public TBaseinfoAgreement selectTBaseinfoAgreementById(Long id);

    /**
     * 查询用户服务协议列表
     * 
     * @param tBaseinfoAgreement 用户服务协议
     * @return 用户服务协议集合
     */
    public List<TBaseinfoAgreement> selectTBaseinfoAgreementList(TBaseinfoAgreement tBaseinfoAgreement);

    /**
     * 新增用户服务协议
     * 
     * @param tBaseinfoAgreement 用户服务协议
     * @return 结果
     */
    public int insertTBaseinfoAgreement(TBaseinfoAgreement tBaseinfoAgreement);

    /**
     * 修改用户服务协议
     * 
     * @param tBaseinfoAgreement 用户服务协议
     * @return 结果
     */
    public int updateTBaseinfoAgreement(TBaseinfoAgreement tBaseinfoAgreement);

    /**
     * 批量删除用户服务协议
     * 
     * @param ids 需要删除的用户服务协议主键集合
     * @return 结果
     */
    public int deleteTBaseinfoAgreementByIds(Long[] ids);

    /**
     * 删除用户服务协议信息
     * 
     * @param id 用户服务协议主键
     * @return 结果
     */
    public int deleteTBaseinfoAgreementById(Long id);
}
