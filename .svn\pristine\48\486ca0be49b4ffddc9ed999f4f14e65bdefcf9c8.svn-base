package com.dataxai.web.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;

/**
 * 权益对象 t_equity_description
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@Data
public class AdminEquityDescription extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /** 套餐主表ID */
    @Excel(name = "套餐主表ID")
    private String tariffId;

    /** 权益描述内容 */
    @Excel(name = "权益描述内容")
    private String content;

    /** 是否包含 1包含，2不包含 */
    @Excel(name = "是否包含 1包含，2不包含")
    private Long contains;


}
