package com.dataxai.web.controller.task;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.alibaba.fastjson2.JSONArray;
import com.dataxai.common.core.domain.R;
import com.dataxai.common.core.page.PageDomain;
import com.dataxai.common.core.page.TableSupport;
import com.dataxai.common.exception.ServiceException;
import com.dataxai.common.utils.SecurityUtils;
import com.dataxai.common.utils.StringUtils;
import com.dataxai.common.utils.sql.SqlUtil;
import com.dataxai.web.Constants.Constants;
import com.dataxai.web.domain.*;
import com.dataxai.web.dto.FollowByTypeDTO;
import com.dataxai.web.dto.OrdinalImageDTO;
import com.dataxai.web.mapper.TaskMapper;
import com.dataxai.web.service.*;
import com.dataxai.web.utils.CommonUtils;
import com.dataxai.web.domain.OrdinalImageVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 任务Controller
 *
 * <AUTHOR>
 * @date 2024-01-08
 */
@RestController
@RequestMapping("/task/photo")
@Api(tags = { "任务管理接口" })
public class TaskController extends BaseController
{
    @Autowired
    private ITaskService taskService;

    @Autowired
    private IFrontUserService userService;

    @Autowired
    private IWebCharacterModelService webCharacterModelService;

    @Autowired
    private IWebScenRealHumanService webScenRealHumanService;

    @Autowired
    private IWebScenGoodsCategorySubService webScenGoodsCategorySubService;

    @Autowired
    private TaskMapper taskMapper;

    /**
     * 查询任务列表
     */
    @ApiOperation(value = "根据用户和任务类型查询任务列表")
    @GetMapping("/listByType/{type}")
    @ApiImplicitParam(name = "type", value = "任务类型(0-真人图，1-人台图，2-商品图，3-配饰图)", required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public AjaxResult listByUserIdAndType(@PathVariable("type") Long type)
    {
//        startPage();
        Task task = new Task();
        task.setType(type);
        task.setUserId(SecurityUtils.getUserId());
        List<Task> list = taskService.selectTaskList(task);
        list.stream().peek(x->{
//            x.setOriginalUrl(Constants.OOS_URL_PREFIX+x.getOriginalUrl());
            x.setOriginalUrl(CommonUtils.addCosPrefix(x.getOriginalUrl()));
        }).collect(Collectors.toList());
//        return getDataTable(list);
        return success(list);
    }

    /**
     * 查询任务列表
     */
    @ApiOperation(value = "根据用户id查询任务列表",hidden = true)
    @GetMapping("/listByUserId/{userId}")
    public TableDataInfo list(@PathVariable("userId") Long userId)
    {
        startPage();
        List<Task> list = taskService.selectTaskListByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 查询任务列表
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:list')")
    @GetMapping("/list/all")
    public TableDataInfo list(Task task)
    {
        startPage();
        List<Task> list = taskService.selectTaskList(task);
        return getDataTable(list);
    }

    /**
     * 导出任务列表
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:export')")
    @Log(title = "任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Task task)
    {
        List<Task> list = taskService.selectTaskList(task);
        ExcelUtil<Task> util = new ExcelUtil<Task>(Task.class);
        util.exportExcel(response, list, "任务数据");
    }

    /**
     * 获取任务切割信息
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:query')")
    @Log(title = "获取任务切割信息", businessType = BusinessType.OTHER)
    @GetMapping(value = "/seg/{taskId}")
    @ApiOperation("获取任务切割信息")
    @ApiImplicitParam(name = "taskId",value = "任务id",required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public AjaxResult getSegDatInfo(@PathVariable("taskId") String taskId) throws IOException {
       // Long userId = SecurityUtils.getUserId();
        Task task = new Task();
        task.setTaskId(taskId);
        Task taskSegDataResult = taskService.selectTaskSegData(task);

        TaskSegData taskSegData = new TaskSegData();
        taskSegData.setTaskId(taskSegDataResult.getTaskId());
        taskSegData.setSegData(taskSegDataResult.getSegData());
        return success(taskSegData);
    }

    /**
     * 前端页面 获取任务详细信息
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:query')")
    @Log(title = "任务", businessType = BusinessType.OTHER)
    @GetMapping(value = "/getInfo/{taskId}")
    @ApiOperation("获取任务详细")
    @ApiImplicitParam(name = "taskId",value = "任务id",required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public AjaxResult getInfo(@PathVariable("taskId") String taskId)
    {
        Long   userId = SecurityUtils.getUserId();
        return success(taskService.selectTaskByUserIdAndTaskId(userId,taskId));
    }

    @Log(title = "任务排队情况", businessType = BusinessType.OTHER)
    @GetMapping(value = "/getTaskQueue/{taskId}")
    @ApiOperation("获取任务排队情况")
    @ApiImplicitParam(name = "taskId",value = "任务id",required = true, dataType = "long", paramType = "path", dataTypeClass = Long.class)
    public AjaxResult getTaskQueue(@PathVariable("taskId") String taskId)
    {
        return success(taskService.selectTaskQueueNumByUserIdAndTaskId(taskId));
    }



    /**
     * 执行任务
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:add')")
    @Log(title = "任务", businessType = BusinessType.OTHER)
    @PostMapping("/execute")
    @ApiOperation("执行任务")
    public AjaxResult executeTask(@RequestBody TaskOrdinalDTO taskOrdinalDTO,HttpServletRequest request) throws ExecutionException, InterruptedException, IOException {
        if (null != taskOrdinalDTO) {
            String desType = taskOrdinalDTO.getDesType();
            if (Constants.DES_TYPE_0.equals(desType)) {
                if (taskOrdinalDTO.getType() == 1) {
                    if (null == taskOrdinalDTO.getRealPerson()) {
                        throw new ServiceException("请选择是否是真人", 500);
                    }
                }
                String prompt = taskOrdinalDTO.getPrompt();
                checkShortCutDesc(prompt);
            } else if (Constants.DES_TYPE_2.equals(desType)) {
                if (taskOrdinalDTO.getType() == 1) {
                    if (null == taskOrdinalDTO.getRealPerson()) {
                        throw new ServiceException("请选择是否是真人", 500);
                    }
                }
                String forward = taskOrdinalDTO.getForward();
                checkForwardDesc(forward);
                String reverse = taskOrdinalDTO.getReverse();
                checkReverseDesc(reverse);
            }
            // 修复
            if (taskOrdinalDTO.getType() != 1) {
                if (taskOrdinalDTO.getRepair() != null && 1 == taskOrdinalDTO.getRepair()) {
                    throw new ServiceException("仅限人台图使用", 500);
                }
            }
        }
        TaskOrdinal taskOrdinal = new TaskOrdinal();
        BeanUtils.copyProperties(taskOrdinalDTO, taskOrdinal);
        List<WebModelCharacterControl> optionalCharacterList = taskOrdinalDTO.getOptionalCharacter();
        if (null != optionalCharacterList) {
            JSONArray optionCharacterJsonArray = JSONArray.from(optionalCharacterList);
            taskOrdinal.setOptionalCharacter(optionCharacterJsonArray.toString());
        }
        taskOrdinal.setShortCutDesc(taskOrdinalDTO.getPrompt());
        taskOrdinal.setUserId(SecurityUtils.getUserId());
        taskOrdinal.setRealPerson(taskOrdinalDTO.getRealPerson());
        WebCharacterModel webCharacterModel = taskOrdinalDTO.getWebCharacterModel();
        if (null != webCharacterModel) {
            List<CharacterModel> params = webCharacterModel.getParams();
            for (CharacterModel param : params) {
                String key = param.getKey();
                String value = param.getValue();
                if (Constants.AGE_STR_ZH.equals(key)) {
                    taskOrdinal.setModelAge(value);
                } else if (Constants.GENDER_STR_ZH.equals(key)) {
                    taskOrdinal.setModelSex(value);
                } else if (Constants.SKIN_STR_ZH.equals(key)) {
                    taskOrdinal.setModelSkin(value);
                }
            }
            WebCharacterModel webCharacterModelDB = webCharacterModelService.selectWebCharacterModelById(webCharacterModel.getId());
            if (null != webCharacterModelDB) {
                taskOrdinal.setModelPrompt(webCharacterModelDB.getPrompt());
            }
            taskOrdinal.setModelCharacterId(webCharacterModel.getId());
        }

        WebScenRealHuman webScenRealHuman = taskOrdinalDTO.getWebScenRealHuman();
        if (null != webScenRealHuman) {
            taskOrdinal.setSceneCategoryLarge(webScenRealHuman.getCategoryLarge());
            taskOrdinal.setSceneCategorySmall(webScenRealHuman.getCategorySmall());
            taskOrdinal.setSceneId(webScenRealHuman.getId());
            WebScenRealHuman webScenRealHumanDB = webScenRealHumanService.selectWebScenRealHumanById(webScenRealHuman.getId());
            if (null != webScenRealHumanDB) {
                taskOrdinal.setScenePrompt(webScenRealHumanDB.getPrompt());
            }
        }
        WebScenGoodsCategorySub webScenGoodsCategorySub = taskOrdinalDTO.getWebScenGoodsCategorySub();
        if (null != webScenGoodsCategorySub) {
            taskOrdinal.setGoodsSceneCategory(webScenGoodsCategorySub.getCategory());
            taskOrdinal.setGoodsSceneCategorySub(webScenGoodsCategorySub.getCategorySub());
            taskOrdinal.setSceneId(webScenGoodsCategorySub.getId());
            WebScenGoodsCategorySub webScenGoodsCategorySubDB = webScenGoodsCategorySubService.selectWebScenGoodsCategorySubById(Integer.valueOf(webScenGoodsCategorySub.getId().intValue()));
            if (null != webScenGoodsCategorySubDB) {
                taskOrdinal.setScenePrompt(webScenGoodsCategorySubDB.getPrompt());
            }
        }

        return toAjax(taskService.executeTask(taskOrdinal));
    }

    private void checkShortCutDesc(String checkedStr){
        if(StringUtils.isNotEmpty(checkedStr)){
            int len = checkedStr.length();
            if(len>2000){
                throw new ServiceException("快捷描述信息长度超过2000",500);
            }
        }
//        else {
//            throw new ServiceException("快捷描述信息为空",500);
//        }
    }

    private void checkForwardDesc(String checkedStr){
        if(StringUtils.isNotEmpty(checkedStr)){
            int len = checkedStr.length();
            if(len>500){
                throw new ServiceException("高级描述我想要信息长度超过500",500);
            }
        }else {
            throw new ServiceException("高级描述我想要信息为空",500);
        }
    }

    private void checkReverseDesc(String checkedStr){
        if(StringUtils.isNotEmpty(checkedStr)){
            int len = checkedStr.length();
            if(len>500){
                throw new ServiceException("高级描述我不想要信息长度超过500",500);
            }
        }
    }

    /**
     * 前端页面 复制创意
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:add')")
    @Log(title = "复制创意", businessType = BusinessType.OTHER)
    @PostMapping("/copyTask")
    @ApiOperation("复制创意")
    public AjaxResult copyTask(@RequestBody TaskDTO taskDTO) throws ExecutionException, InterruptedException {
        String taskId = taskService.copyTask(taskDTO);
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("taskId",taskId);
        return success(resultMap);
    }

    /**
     * 前端页面 再次执行
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:add')")
    @Log(title = "再次执行", businessType = BusinessType.OTHER)
    @PostMapping("/execAgain")
    @ApiOperation("再次执行")
    public AjaxResult executeTaskAgain(@RequestBody TaskDTO taskDTO) throws ExecutionException, InterruptedException, JsonProcessingException {
        return toAjax(taskService.executeTaskAgain(taskDTO));
    }

    /**
     * 前端页面 再次编辑
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:add')")
    @Log(title = "再次编辑", businessType = BusinessType.OTHER)
    @PostMapping("/editAgain")
    @ApiOperation("再次编辑")
    public AjaxResult editTaskAgain(@RequestBody TaskDTO taskDTO) throws ExecutionException, InterruptedException, IOException {
        String taskId = taskService.editTaskAgain(taskDTO);
        Map<String,String> resultMap = new HashMap<>();
        resultMap.put("taskId",taskId);
        return success(resultMap);
    }

    /**
     * 前端页面 生成4k图
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:add')")
    @Log(title = "生成4k图", businessType = BusinessType.OTHER)
    @PostMapping("/exec4k")
    @ApiOperation("生成4k图")
    public AjaxResult exec4k(@RequestBody TaskDTO taskDTO) throws ExecutionException, InterruptedException {
        TaskDTO taskDTOResult = taskService.exec4k(taskDTO);

        return success(taskDTOResult);
    }

    /**
     * 新增任务
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:add')")
    @Log(title = "任务", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation("新建任务")
    public AjaxResult add(@RequestBody Task task) throws ExecutionException, InterruptedException {
        String taskId = taskService.insertTask(task);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("taskId",taskId);
        resultMap.put("type",task.getType());
        return success(resultMap);
    }



    /**
     * 修改任务名称
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:edit')")
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PostMapping("updateTaskName")
    @ApiOperation("修改任务名称")
    public AjaxResult updateTaskName(@RequestBody UpdateTaskNameDTO updateTaskNameDTO)
    {
        Task task = new Task();
        task.setTaskName(updateTaskNameDTO.getTaskName());
        task.setTaskId(updateTaskNameDTO.getTaskId());
        task.setUserId(SecurityUtils.getUserId());
        return toAjax(taskService.updateTaskName(task));
    }

    /**
     * 修改任务
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:edit')")
    @Log(title = "任务", businessType = BusinessType.UPDATE)
    @PostMapping
    @ApiOperation(value = "更新任务")
    public AjaxResult edit(@RequestBody Task task)
    {
//        task.setOriginalUrl(task.getOriginalUrl().replace(Constants.OOS_URL_PREFIX,""));
        task.setOriginalUrl(CommonUtils.subCosPrefix(task.getOriginalUrl()));
        return toAjax(taskService.resetTask(task));
    }

    /**
     * 删除任务
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:remove')")
    @Log(title = "任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{taskIds}")
    @ApiOperation("删除任务")
    @ApiImplicitParam(name = "taskIds", required = true, dataType = "long", paramType = "path", dataTypeClass = String.class)
    public AjaxResult remove(@PathVariable String[] taskIds)
    {
        Long userId = SecurityUtils.getUserId();
        return toAjax(taskService.logicDeleteTaskByTaskIds(userId,taskIds));
    }


    /**
     * 调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:edit')")
    @PostMapping("/imgChangeText")
    @ApiOperation(value = "调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推")
    public AjaxResult imgChangeText(@RequestBody ImgChangeTextDto dto) throws ExecutionException, InterruptedException {
       taskService.imgChangeText(dto);
        return success(null);
    }

    /**
     * 调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推
     */
//    @PreAuthorize("@ss.hasPermi('task:photo:edit')")
    @PostMapping("/test")
    @ApiOperation(value = "调用接口 实现图片反推文本，用于4：元素图提取，将截图进行文本反推")
    public AjaxResult test(@RequestBody ImgChangeTextDto dto) throws ExecutionException, InterruptedException, IOException {
        taskService.test(dto);
        return null;
    }

    @PostMapping("/getTaskResult")
    @ApiOperation(value = "查询用户生成结果")
    public R<Object> getTaskResult(@RequestBody FollowByTypeDTO dto){
        List<Task> list= taskService.getTaskResult(dto.getTypes());
        return R.ok(list);
    }

    @PostMapping("/getHistory")
    @ApiOperation(value = "查询用户的历史记录")
    public R<Object> getHistory(@RequestBody FollowByTypeDTO dto){

        PageDomain pageDomain = TableSupport.buildPageRequest();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        PageHelper.startPage(dto.getPageNumber(), dto.getPageSize(), orderBy).setReasonable(reasonable);
        List<Task> list =  taskService.getHistory(dto.getTypes());
        PageInfo<Task> pageInfo = new PageInfo<>(list);
        long total = pageInfo.getTotal(); // 正确的总记录数
//        // 实现分页逻辑
//        Integer pageNumber = dto.getPageNumber();
//        Integer pageSize = dto.getPageSize();
//        Integer fromIndex = (pageNumber - 1) * pageSize;
//        Integer toIndex = Math.min(fromIndex + pageSize,  list.size());
//        List<Task> paginatedList =  list.subList(fromIndex, toIndex);
        // 返回结果
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("total", total);
        dataMap.put("data", list);
        return R.ok(dataMap);
    }

    @PostMapping("/getHistoryImage")
    @ApiOperation(value = "查询用户传过的图")
    public R<Object> getHistoryImage(@RequestBody FollowByTypeDTO dto){
        List<OrdinalImageVO> list =  taskService.getHistoryImage(dto.getTypes());
        // 实现分页逻辑
        Integer pageNumber = dto.getPageNumber();
        Integer pageSize = dto.getPageSize();
        Integer fromIndex = (pageNumber - 1) * pageSize;
        Integer toIndex = Math.min(fromIndex + pageSize,  list.size());
        List<OrdinalImageVO> paginatedList =  list.subList(fromIndex, toIndex);
        // 返回结果
        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("total",  list.size());
        dataMap.put("data", paginatedList);
        return R.ok(dataMap);
    }

    @PostMapping("/deleteHistoryImage")
    @ApiOperation(value = "删除用户传过的图")
    public R<Object> deleteHistoryImage(@RequestBody OrdinalImageDTO dto ){
        Integer count =  taskService.deleteHistoryImage(dto);
        return R.ok(count);
    }

    @PostMapping("/deleteImage")
    @ApiOperation(value = "删除生成结果/删除历史记录/删除收藏的图")
    public R<Object> deleteImage(@RequestParam("imageId") String imageId){
        Integer result =  taskService.deleteImage(imageId);
        return R.ok(result);
    }

    /**
     * 查询用户收藏的图片列表
     */
//    @PreAuthorize("@ss.hasPermi('favorite:img:list')")
    @PostMapping("/listByType")
    @ApiOperation(value = "查询用户收藏的图片列表")
    public R<HashMap<String,Object>>  listByUserIdTypes(@RequestBody FollowByTypeDTO dto)
    {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        Integer pageNumber = dto.getPageNumber();
        Integer pageSize = dto.getPageSize();
        Long userId = SecurityUtils.getUserId();
        OrdinalImgResult ordinalImgResult = new OrdinalImgResult();
        ordinalImgResult.setUserId(userId);
        ordinalImgResult.setFollow(1L);
        PageHelper.startPage(pageNumber, pageSize, orderBy).setReasonable(reasonable);
        List<OrdinalImgResult> list = taskService.listByUserIdTypes(ordinalImgResult,dto.getTypes());
        list.stream().peek(x->{
            x.setResImgUrl(CommonUtils.addCosPrefix(x.getResImgUrl()));
            x.setOriginalImgUrl(CommonUtils.addCosPrefix(x.getOriginalImgUrl()));
            x.setMarkImgUrl(CommonUtils.addCosPrefix(x.getMarkImgUrl()));
            x.setResSmallImgUrl(CommonUtils.addCosPrefix(x.getResSmallImgUrl()));
            x.setDisassembleImgUrl(CommonUtils.addCosPrefix(x.getDisassembleImgUrl()));
        }).collect(Collectors.toList());
        HashMap<String,Object> dataMap = new HashMap<>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total",total);
        long maxPageSize = total /dto.getPageSize() + (total % dto.getPageSize()== 0 ? 0 : 1);
        if(dto.getPageNumber() > maxPageSize){
            list.clear();
        }
        dataMap.put("data",list);
        return R.ok(dataMap);
    }



}
