package com.dataxai.web.controller.admincontroller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.TTariffPackageSub;
import com.dataxai.web.service.ITTariffPackageSubService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 子套餐Controller
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@RestController
@RequestMapping("/subpackage/adminsub")
public class TTariffPackageSubController extends BaseController
{
    @Autowired
    private ITTariffPackageSubService tTariffPackageSubService;

    /**
     * 查询子套餐列表
     */
    @PreAuthorize("@ss.hasPermi('subpackage:adminsub:list')")
    @GetMapping("/list")
    public TableDataInfo list(TTariffPackageSub tTariffPackageSub)
    {
        startPage();
        List<TTariffPackageSub> list = tTariffPackageSubService.selectTTariffPackageSubList(tTariffPackageSub);
        return getDataTable(list);
    }

    /**
     * 导出子套餐列表
     */
    @PreAuthorize("@ss.hasPermi('subpackage:adminsub:export')")
    @Log(title = "子套餐", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TTariffPackageSub tTariffPackageSub)
    {
        List<TTariffPackageSub> list = tTariffPackageSubService.selectTTariffPackageSubList(tTariffPackageSub);
        ExcelUtil<TTariffPackageSub> util = new ExcelUtil<TTariffPackageSub>(TTariffPackageSub.class);
        util.exportExcel(response, list, "子套餐数据");
    }

    /**
     * 获取子套餐详细信息
     */
    @PreAuthorize("@ss.hasPermi('subpackage:adminsub:query')")
    @GetMapping(value = "/{tariffPackageSubId}")
    public AjaxResult getInfo(@PathVariable("tariffPackageSubId") String tariffPackageSubId)
    {
        return success(tTariffPackageSubService.selectTTariffPackageSubByTariffPackageSubId(tariffPackageSubId));
    }

    /**
     * 新增子套餐
     */
    @PreAuthorize("@ss.hasPermi('subpackage:adminsub:add')")
    @Log(title = "子套餐", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TTariffPackageSub tTariffPackageSub)
    {
        return toAjax(tTariffPackageSubService.insertTTariffPackageSub(tTariffPackageSub));
    }

    /**
     * 修改子套餐
     */
    @PreAuthorize("@ss.hasPermi('subpackage:adminsub:edit')")
    @Log(title = "子套餐", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TTariffPackageSub tTariffPackageSub)
    {
        return toAjax(tTariffPackageSubService.updateTTariffPackageSub(tTariffPackageSub));
    }

    /**
     * 删除子套餐
     */
    @PreAuthorize("@ss.hasPermi('subpackage:adminsub:remove')")
    @Log(title = "子套餐", businessType = BusinessType.DELETE)
	@DeleteMapping("/{tariffPackageSubIds}")
    public AjaxResult remove(@PathVariable String[] tariffPackageSubIds)
    {
        return toAjax(tTariffPackageSubService.deleteTTariffPackageSubByTariffPackageSubIds(tariffPackageSubIds));
    }
}
