# 测试产品图片异步上传功能

## 测试步骤

### 1. 启动应用
确保Redis服务正常运行，启动Spring Boot应用。

### 2. 测试产品创建接口
调用产品新增接口，传入包含图片URL的产品信息：

```json
POST /system/productInfo
{
    "productTitle": "测试产品",
    "productPrice": 99.99,
    "productImageUrl": "https://example.com/test-image.jpg",
    "sourcePlatform": "测试平台"
}
```

### 3. 验证数据库状态
检查产品信息表：
- `original_image_url` 字段应该保存了图片URL
- `status` 字段应该为 "1"（待同步图片）

### 4. 检查队列状态
调用队列状态接口：
```
GET /product/image/queue/status
```

### 5. 观察日志
查看应用日志，应该能看到：
- 事件发布日志
- 队列任务创建日志
- 图片下载和上传日志
- 状态更新日志

### 6. 验证最终结果
等待处理完成后，检查：
- `product_image_url` 字段更新为阿里云OSS地址
- `status` 字段更新为 "2"（已同步图片）

## 预期行为

### 成功场景
1. 产品创建成功，返回产品ID
2. 事件异步处理，队列任务创建
3. 图片下载成功，上传到阿里云OSS
4. 产品状态更新为"2"

### 失败场景
1. 图片URL无效或下载失败
2. 阿里云上传失败
3. 产品状态更新为"3"，记录错误信息

## 测试用例

### 测试用例1：正常图片URL
```json
{
    "productTitle": "正常测试产品",
    "productImageUrl": "https://picsum.photos/800/600",
    "productPrice": 99.99
}
```

### 测试用例2：无效图片URL
```json
{
    "productTitle": "无效URL测试",
    "productImageUrl": "https://invalid-url.com/nonexistent.jpg",
    "productPrice": 99.99
}
```

### 测试用例3：无图片URL
```json
{
    "productTitle": "无图片测试",
    "productPrice": 99.99
}
```

## 监控要点

1. **Redis队列长度**: 正常情况下应该很快处理完，队列长度接近0
2. **线程池状态**: 观察异步线程池的使用情况
3. **错误日志**: 关注下载和上传过程中的异常
4. **数据库状态**: 确认状态字段正确更新

## 性能测试

可以批量创建产品来测试：
- 队列处理能力
- 并发下载上传性能
- 系统稳定性

## 故障排查

### 常见问题
1. **队列任务不处理**: 检查Redis连接和序列化配置
2. **图片下载失败**: 检查网络连接和URL有效性
3. **阿里云上传失败**: 检查OSS配置和权限
4. **状态不更新**: 检查数据库连接和事务配置

### 调试建议
1. 增加详细日志输出
2. 使用Redis客户端查看队列内容
3. 检查线程池和异步配置
4. 验证事件发布和监听机制
