package com.dataxai.web.core.config;

import com.dataxai.web.domain.Task;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class TasksRedisConfig {
    @Bean("customRedisTemplate")
    public RedisTemplate<String, Task> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Task> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 键序列化
        template.setKeySerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());

        // 值序列化
        GenericJackson2JsonRedisSerializer serializer = new GenericJackson2JsonRedisSerializer();
        template.setValueSerializer(serializer);
        template.setHashValueSerializer(serializer);

        template.afterPropertiesSet();
        return template;
    }
}
