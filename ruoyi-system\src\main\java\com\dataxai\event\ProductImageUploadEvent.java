package com.dataxai.event;

import org.springframework.context.ApplicationEvent;

/**
 * 产品图片上传事件
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public class ProductImageUploadEvent extends ApplicationEvent {
    
    private final Long productId;
    private final String originalImageUrl;
    private final Long userId;

    public ProductImageUploadEvent(Object source, Long productId, String originalImageUrl, Long userId) {
        super(source);
        this.productId = productId;
        this.originalImageUrl = originalImageUrl;
        this.userId = userId;
    }

    public Long getProductId() {
        return productId;
    }

    public String getOriginalImageUrl() {
        return originalImageUrl;
    }

    public Long getUserId() {
        return userId;
    }
}
