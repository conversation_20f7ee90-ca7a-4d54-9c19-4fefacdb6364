package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.RefundInfo;

/**
 * 退款记录Service接口
 * 
 * <AUTHOR>
 * @date 2024-02-25
 */
public interface IRefundInfoService 
{
    /**
     * 查询退款记录
     * 
     * @param id 退款记录主键
     * @return 退款记录
     */
    public RefundInfo selectRefundInfoById(String id);

    /**
     * 查询退款记录列表
     * 
     * @param refundInfo 退款记录
     * @return 退款记录集合
     */
    public List<RefundInfo> selectRefundInfoList(RefundInfo refundInfo);

    /**
     * 新增退款记录
     * 
     * @param refundInfo 退款记录
     * @return 结果
     */
    public int insertRefundInfo(RefundInfo refundInfo);

    /**
     * 修改退款记录
     * 
     * @param refundInfo 退款记录
     * @return 结果
     */
    public int updateRefundInfo(RefundInfo refundInfo);

    /**
     * 批量删除退款记录
     * 
     * @param ids 需要删除的退款记录主键集合
     * @return 结果
     */
    public int deleteRefundInfoByIds(String[] ids);

    /**
     * 删除退款记录信息
     * 
     * @param id 退款记录主键
     * @return 结果
     */
    public int deleteRefundInfoById(String id);

    RefundInfo createRefundByOrderNo(String orderNo, String reason);

    void updateRefund(String content);
}
