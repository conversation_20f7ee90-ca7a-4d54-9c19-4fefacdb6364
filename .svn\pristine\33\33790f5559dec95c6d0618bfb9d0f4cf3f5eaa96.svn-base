package com.dataxai.common.core.domain.model;

import com.dataxai.common.core.domain.entity.EquityDescriptionDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 前端用户对象 t_user
 * 
 * <AUTHOR>
 * @date 2024-01-05
 */

@ApiModel(description = "前端用户对象")
@Data
public class User implements Serializable
{
    private static final long serialVersionUID = 1L;


    /** 用户id */
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /** 用户昵称 */
//    @Excel(name = "用户昵称")
    @ApiModelProperty(value = "用户昵称")
    private String nickName;

    /** 手机号码 */
//    @Excel(name = "手机号码")
    @ApiModelProperty(value = "用户手机号码")
    private String phone;

    /** 用户头像 */
//    @Excel(name = "用户头像")
    @ApiModelProperty(value = "用户头像")
    private String avatar;

    /** 微信id */
//    @Excel(name = "微信id")
    @ApiModelProperty(value = "微信openId",hidden = true)
//    @JsonIgnore  //隐藏返回该字段
    private String wxOpenId;

    /** 当前所使用的套餐id */
//    @Excel(name = "当前所使用的套餐id")
    @ApiModelProperty(value = "当前所使用的套餐id")
    private String packageId;

    @ApiModelProperty(value = "当前所使用的套餐名称")
    private String packageName;

    /** 当前所使用的子套餐id */
//    @Excel(name = "当前所使用的套餐id")
    @ApiModelProperty(value = "当前所使用的子套餐id")
    private String packageSubId;

    /** 当前所使用的子套餐id */
//    @Excel(name = "当前所使用的套餐id")
    @ApiModelProperty(value = "当前所使用的加油包id")
    private String refuelingBagId;

    /**
     * 是否默认套餐 1是 2否
     */
    @ApiModelProperty(value = "当前所使用的套餐名称（1 是 2 否）")
    private int isDefaultPackage;

    @ApiModelProperty(value = "剩余积分")
    private Long residueScore;

    @ApiModelProperty(value = "剩余积分价值，单位元")
    private Double residueScoreValue;

    /** 是否删除，0-未删除，1-已删除 */
    @ApiModelProperty(hidden = true)
    @JsonIgnore  //隐藏返回该字段
    private Integer delFlag;

    /** 是否删除，0-未禁用，1-已禁用 */
    @ApiModelProperty(hidden = true)
    @JsonIgnore  //隐藏返回该字段
    private Integer enable;

    /** 性别，0-未知，1-男，2-女 */
    @ApiModelProperty(value = "性别，0-未知，1-男，2-女 ")
    private Integer gender;
    @ApiModelProperty(value = "登录时间",hidden = true)
    @JsonIgnore  //隐藏返回该字段
    private Date loginTime;

    /** 绑定失败原因(0-手机号已存在，1-绑定失败，微信已绑定其他用户！) */
    @ApiModelProperty(value = "绑定失败原因(0-手机号已存在，1-绑定失败，微信已绑定其他用户！) ",hidden = true)
    private Integer bindFail;

    /** 即将过期积分 */
    private Long residualIntegral;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expirationTime;

    /** 过期的积分是 套餐还是加油包；1:套餐 2:加油包 **/
    private Integer packageOrRefueligBag;

    //当前套餐的过期时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tariffEndTime;

    /**
     * (0-不包含4k下载，1-包含4k下载)
     */
    @ApiModelProperty(value = "0-不包含4k下载，1-包含4k下载")
    private Integer fourKDown;

    /**
     * (当前自然月，是否赠送过默认套餐(0-未赠送，1-已赠送))
     */
    @ApiModelProperty(value = "当前自然月，是否赠送过默认套餐(0-未赠送，1-已赠送)")
    private Integer freeGift;

    @ApiModelProperty(value = "赠送默认套餐的时间")
    private Date freeGiftTime;

    @ApiModelProperty(value = "使用的套餐服务信息")
    private List<EquityDescriptionDTO> equityDescriptionList;

    @ApiModelProperty(value = "用户的加油包和积分的信息")
    private List<UserPackageAndBagVO> userPackageAndBagVOs;


    /** 创建者 */
    @JsonIgnore  //隐藏返回该字段
    private String createBy;
    @JsonIgnore  //隐藏返回该字段
    private Date createTime;
    @JsonIgnore  //隐藏返回该字段
    private Date updateTime;

    /** 更新者 */
    @JsonIgnore  //隐藏返回该字段
    private String updateBy;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "是否有密码")
    private Boolean isPassword;

}
