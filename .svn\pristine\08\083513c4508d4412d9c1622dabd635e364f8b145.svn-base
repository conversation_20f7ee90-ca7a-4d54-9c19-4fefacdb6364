package com.dataxai.web.controller.front;

import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.dataxai.common.core.domain.R;
import com.dataxai.web.domain.WebScenRealHumanCategory;
import com.dataxai.web.domain.WebScenRealHumanDTO;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.dataxai.common.annotation.Log;
import com.dataxai.common.core.controller.BaseController;
import com.dataxai.common.core.domain.AjaxResult;
import com.dataxai.common.enums.BusinessType;
import com.dataxai.web.domain.WebScenRealHuman;
import com.dataxai.web.service.IWebScenRealHumanService;
import com.dataxai.common.utils.poi.ExcelUtil;
import com.dataxai.common.core.page.TableDataInfo;

/**
 * 真人-人台场景信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@RestController
@RequestMapping("/scen/realHuman")
@Api(tags = "预设模版-场景")
public class WebScenRealHumanController extends BaseController
{
    @Autowired
    private IWebScenRealHumanService webScenRealHumanService;

    /**
     * 查询真人-人台场景信息列表
     */
    @PreAuthorize("@ss.hasPermi('scen:realHuman:list')")
    @GetMapping("/list")
//    @ApiOperation("查询真人-人台场景信息列表")
    public TableDataInfo list(WebScenRealHuman webScenRealHuman)
    {
        startPage();
        List<WebScenRealHuman> list = webScenRealHumanService.selectWebScenRealHumanList(webScenRealHuman);
        return getDataTable(list);
    }

    /**
     * 导出真人-人台场景信息列表
     */
//    @PreAuthorize("@ss.hasPermi('scen:realHuman:export')")
    @Log(title = "真人-人台场景信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WebScenRealHuman webScenRealHuman)
    {
        List<WebScenRealHuman> list = webScenRealHumanService.selectWebScenRealHumanList(webScenRealHuman);
        ExcelUtil<WebScenRealHuman> util = new ExcelUtil<WebScenRealHuman>(WebScenRealHuman.class);
        util.exportExcel(response, list, "真人-人台场景信息数据");
    }

    /**
     * 获取真人-人台场景信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('scen:realHuman:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(webScenRealHumanService.selectWebScenRealHumanById(id));
    }

    /**
     * 新增真人-人台场景信息
     */
//    @PreAuthorize("@ss.hasPermi('scen:realHuman:add')")
    @Log(title = "真人-人台场景信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WebScenRealHuman webScenRealHuman)
    {
        return toAjax(webScenRealHumanService.insertWebScenRealHuman(webScenRealHuman));
    }

    /**
     * 修改真人-人台场景信息
     */
    @PreAuthorize("@ss.hasPermi('scen:realHuman:edit')")
    @Log(title = "真人-人台场景信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WebScenRealHuman webScenRealHuman)
    {
        return toAjax(webScenRealHumanService.updateWebScenRealHuman(webScenRealHuman));
    }

    /**
     * 删除真人-人台场景信息
     */
    @PreAuthorize("@ss.hasPermi('scen:realHuman:remove')")
    @Log(title = "真人-人台场景信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(webScenRealHumanService.deleteWebScenRealHumanByIds(ids));
    }

    /**
     * 前端页面 预设模版-场景分类信息列表
     */
//    @PreAuthorize("@ss.hasPermi('scen:realHuman:list')")
    @GetMapping("/list/category")
    @ApiOperation(value = "预设模版-场景分类信息列表")
    public AjaxResult listCategory()
    {
        List<String> webScenRealHumanCategoryList = webScenRealHumanService.selectWebScenRealHumanCategory();
        return success(webScenRealHumanCategoryList);
    }

    /**
     * 前端页面 查询真人-人台场景信息列表
     */
//    @PreAuthorize("@ss.hasPermi('scen:realHuman:list')")
    @GetMapping("/listByFresh")
    @ApiOperation(value = "预设模版场景单个场景信息列表(换一批)")
    public R<HashMap<String,Object>> listByFresh(String largeCategory,int pageNum,int pageSize)
    {
        startPage();
        WebScenRealHuman webScenRealHuman = new WebScenRealHuman();
        webScenRealHuman.setCategoryLarge(largeCategory);
        List<WebScenRealHuman> list = webScenRealHumanService.selectWebScenRealHumanList(webScenRealHuman);
        HashMap<String,Object> dataMap  = new HashMap<String,Object>();
        long total = new PageInfo(list).getTotal();
        dataMap.put("total", total);
        long maxPageSize = total/pageSize + (total%pageSize==0?0:1);
        if(pageNum>maxPageSize){
            list.clear();
        }
        dataMap.put("data",list);
        return R.ok(dataMap);
    }

    /**
     * 前端页面，根据场景信息，查询该条数据在第几页
     */
    @PostMapping("/pageNum")
    @ApiOperation(value = "根据真人场景特征信息，查询该条数据在第几页")
    public AjaxResult getPageNumBySubSceneId(@RequestBody WebScenRealHumanDTO webScenRealHumanDTO)
    {
        String categoryLarge = webScenRealHumanDTO.getCategoryLarge();
        Long subSceneId = webScenRealHumanDTO.getId();
        Integer pageSize = webScenRealHumanDTO.getPageSize();
        int pageNum = webScenRealHumanService.selectPageNumBySubSceneId(categoryLarge,subSceneId,pageSize);
        return pageNum>0?success(pageNum):AjaxResult.error("查询的场景不存在");
    }


}
