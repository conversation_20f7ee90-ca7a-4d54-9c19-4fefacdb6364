package com.dataxai.web.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.WebModelDicMapper;
import com.dataxai.web.domain.WebModelDic;
import com.dataxai.web.service.IWebModelDicService;

/**
 * 模特的咒语词典Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-08
 */
@Service
public class WebModelDicServiceImpl implements IWebModelDicService 
{
    @Autowired
    private WebModelDicMapper webModelDicMapper;

    /**
     * 查询模特的咒语词典
     * 
     * @param id 模特的咒语词典主键
     * @return 模特的咒语词典
     */
    @Override
    public WebModelDic selectWebModelDicById(Long id)
    {
        return webModelDicMapper.selectWebModelDicById(id);
    }

    /**
     * 查询模特的咒语词典列表
     * 
     * @param webModelDic 模特的咒语词典
     * @return 模特的咒语词典
     */
    @Override
    public List<WebModelDic> selectWebModelDicList(WebModelDic webModelDic)
    {
        return webModelDicMapper.selectWebModelDicList(webModelDic);
    }

  /**
     * 查询模特的咒语词典分类列表
     *
     * @param webModelDic 模特的咒语词典
     * @return 模特的咒语词典
     */
    @Override
    public List<WebModelDic> selectWebModelCategoryDicList(WebModelDic webModelDic)
    {
        return webModelDicMapper.selectWebModelCategoryDicList(webModelDic);
    }

    /**
     * 新增模特的咒语词典
     * 
     * @param webModelDic 模特的咒语词典
     * @return 结果
     */
    @Override
    public int insertWebModelDic(WebModelDic webModelDic)
    {
        return webModelDicMapper.insertWebModelDic(webModelDic);
    }

    /**
     * 修改模特的咒语词典
     * 
     * @param webModelDic 模特的咒语词典
     * @return 结果
     */
    @Override
    public int updateWebModelDic(WebModelDic webModelDic)
    {
        return webModelDicMapper.updateWebModelDic(webModelDic);
    }

    /**
     * 批量删除模特的咒语词典
     * 
     * @param ids 需要删除的模特的咒语词典主键
     * @return 结果
     */
    @Override
    public int deleteWebModelDicByIds(Long[] ids)
    {
        return webModelDicMapper.deleteWebModelDicByIds(ids);
    }

    /**
     * 删除模特的咒语词典信息
     * 
     * @param id 模特的咒语词典主键
     * @return 结果
     */
    @Override
    public int deleteWebModelDicById(Long id)
    {
        return webModelDicMapper.deleteWebModelDicById(id);
    }
}
