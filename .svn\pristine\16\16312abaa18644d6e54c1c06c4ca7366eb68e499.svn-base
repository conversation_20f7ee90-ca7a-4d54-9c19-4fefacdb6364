package com.dataxai.web.task;

import com.dataxai.web.domain.Task;
import io.lettuce.core.RedisCommandTimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

//任务队列服务类
@Service
@Slf4j
public class TaskQueueService {
    private final RedisTemplate<String, Task> redisTemplate;

    private static final String TASK_QUEUE_PREFIX = "task:queue:";
    private static final String FAILED_TASK_QUEUE = "task:queue:failed";

    public static final List<String> STATUS_LIST5 = new ArrayList<>(Arrays.asList("0","1","2","3","4","5"));;
    public static final List<String> STATUS_LIST10 = new ArrayList<>(Arrays.asList("6","7","8","9","10"));;
    public static final List<String> STATUS_LIST14 = new ArrayList<>(Arrays.asList("11","12","13","14"));;
    private static final int MAX_RETRY = 3;

    @Autowired
        public TaskQueueService(RedisTemplate<String, Task> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    // 添加任务到指定类型队列
    public void enqueueTask(String taskType, Task task) {
        if(STATUS_LIST5.contains(taskType)){
            taskType = "AISnap";
            String queueKey = "task:queue:" + taskType;
            Long result = redisTemplate.opsForList().leftPush(queueKey, task);
            if (result != null && result > 0) {
                System.out.println("AISnap任务添加成功！当前队列长度：" + result);
            }
        }else if(STATUS_LIST10.contains(taskType)){
            taskType = "BatchFiss";
            String queueKey = "task:queue:" + taskType;
            Long result = redisTemplate.opsForList().leftPush(queueKey, task);
            if (result != null && result > 0) {
                System.out.println("BatchFiss任务添加成功！当前队列长度：" + result);
            }
        }else if(STATUS_LIST14.contains(taskType)){
            taskType = "CropEx";
            String queueKey = "task:queue:" + taskType;
            Long result = redisTemplate.opsForList().leftPush(queueKey, task);
            if (result != null && result > 0) {
                System.out.println("CropEx任务添加成功！当前队列长度：" + result);
            }
        }
    }

    // 从指定类型队列获取任务
    public Task dequeueTask(String taskType) {
        String queueKey = TASK_QUEUE_PREFIX + taskType;
        Task o = redisTemplate.opsForList().rightPop(queueKey, 0, TimeUnit.SECONDS);
        return redisTemplate.opsForList().rightPop(queueKey, 0, TimeUnit.SECONDS);
    }

    // 添加失败任务到重试队列
    public void enqueueFailedTask(Task task) {
        redisTemplate.opsForList().leftPush(FAILED_TASK_QUEUE, task);
    }

    // 从失败队列获取任务
    public Task dequeueFailedTask() {
        return  redisTemplate.opsForList().rightPop(FAILED_TASK_QUEUE, 0, TimeUnit.SECONDS);
    }

    // 获取队列长度（监控用）
    public long getQueueSize(String taskType) {
        String queueKey = TASK_QUEUE_PREFIX + taskType;
        Long size = redisTemplate.opsForList().size(queueKey);
        return size != null ? size : 0;
    }

    // 使用 BRPOP 的安全出队
    public Task safeDequeue(String taskType) {
        String queueKey = TASK_QUEUE_PREFIX + taskType;
        int retryCount = 0;

        while (retryCount < MAX_RETRY) {
            try {
                Task execute = redisTemplate.execute(new TaskRedisCallback(queueKey), true);
                return execute;
            } catch (RedisCommandTimeoutException e) {
                retryCount++;
                log.warn("Redis BRPOP timeout (attempt {}/{})", retryCount, MAX_RETRY);
            } catch (Exception e) {
                log.error("Unexpected error during BRPOP", e);
                return null;
            }
        }
        return null;
    }

    // 带指数退避的重试机制
    public Task dequeueWithBackoff(String taskType) {
        int baseDelay = 100; // 初始延迟 100ms
        int maxDelay = 5000; // 最大延迟 5s

        for (int attempt = 1; attempt <= MAX_RETRY; attempt++) {
            Task task = safeDequeue(taskType);
            if (task != null) return task;

            // 指数退避
            int delay = (int) Math.min(maxDelay, baseDelay * Math.pow(2, attempt));
            try {
                Thread.sleep(delay);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return null;
            }
        }
        return null;
    }

    private class TaskRedisCallback implements RedisCallback<Task> {
        private final String queueKey;

        public TaskRedisCallback(String queueKey) {
            this.queueKey = queueKey;
        }

        @Override
        public Task doInRedis(RedisConnection connection) throws DataAccessException {
            // 使用 BRPOP 命令
            List<byte[]> result = null;
            for (int i = 0; i < 6; i++) { // 30秒 = 6次*5秒
                result = connection.bRPop(5, queueKey.getBytes());
                if (result != null) break;
            }
            //List<byte[]> result = connection.bRPop(30, queueKey.getBytes());
            if (result != null && result.size() >= 2) {
                // 反序列化对象
                byte[] valueBytes = result.get(1);
                Task deserialize = (Task) redisTemplate.getValueSerializer().deserialize(valueBytes);
                if(deserialize!=null){
                    log.info("deserialize:------------"+deserialize);
                }
                return (Task) redisTemplate.getValueSerializer().deserialize(valueBytes);
            }
            return null;
        }
    }
}
