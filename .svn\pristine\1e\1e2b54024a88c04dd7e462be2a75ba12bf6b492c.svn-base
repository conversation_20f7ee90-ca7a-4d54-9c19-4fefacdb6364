<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.AdminEquityDescriptionMapper">
    
    <resultMap type="AdminEquityDescription" id="AdminEquityDescriptionResult">
        <result property="id"    column="id"    />
        <result property="tariffId"    column="tariff_id"    />
        <result property="content"    column="content"    />
        <result property="contains"    column="contains"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectAdminEquityDescriptionVo">
        select id, tariff_id, content, contains, create_by, create_time, update_by, update_time from t_equity_description
    </sql>

    <select id="selectAdminEquityDescriptionList" parameterType="AdminEquityDescription" resultMap="AdminEquityDescriptionResult">
        <include refid="selectAdminEquityDescriptionVo"/>
        <where>  
            <if test="tariffId != null  and tariffId != ''"> and tariff_id = #{tariffId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="contains != null "> and contains = #{contains}</if>
        </where>
    </select>
    
    <select id="selectAdminEquityDescriptionById" parameterType="Long" resultMap="AdminEquityDescriptionResult">
        <include refid="selectAdminEquityDescriptionVo"/>
        where id = #{id}
    </select>

    <insert id="insertAdminEquityDescription" parameterType="AdminEquityDescription">
        insert into t_equity_description
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="tariffId != null">tariff_id,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="contains != null">contains,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="tariffId != null">#{tariffId},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="contains != null">#{contains},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateAdminEquityDescription" parameterType="AdminEquityDescription">
        update t_equity_description
        <trim prefix="SET" suffixOverrides=",">
            <if test="tariffId != null">tariff_id = #{tariffId},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="contains != null">`contains` = #{contains},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAdminEquityDescriptionById" parameterType="Long">
        delete from t_equity_description where id = #{id}
    </delete>

    <delete id="deleteAdminEquityDescriptionByTariffId" parameterType="String">
        delete from t_equity_description where tariff_id = #{TariffId}
    </delete>

    <delete id="deleteAdminEquityDescriptionByIds" parameterType="String">
        delete from t_equity_description where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>