package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 真人-人台场景信息对象 包装类
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@ApiModel
public class WebScenRealHumanDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @ApiModelProperty(value = "真人-人台场景id")
    private Long id;

    /** 大类 */
    @Excel(name = "大类")
    @ApiModelProperty(value = "大类")
    private String categoryLarge;

    /** 小类 */
    @Excel(name = "小类")
    @ApiModelProperty(value = "大分类下面的小类",hidden = true)
    private String categorySmall;

    /** 正向关键词 */
    @Excel(name = "正向关键词")
    @ApiModelProperty(value = "正向关键词")
    private String prompt;

    /** 图片练接 */
    @Excel(name = "图片练接")
    @ApiModelProperty(hidden = true)
    private String image;

    /** 是否有效 */
    @Excel(name = "是否有效")
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private Long isVaild;

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    private Integer pageSize;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setCategoryLarge(String categoryLarge)
    {
        this.categoryLarge = categoryLarge;
    }

    public String getCategoryLarge()
    {
        return categoryLarge;
    }
    public void setCategorySmall(String categorySmall)
    {
        this.categorySmall = categorySmall;
    }

    public String getCategorySmall()
    {
        return categorySmall;
    }
    public void setPrompt(String prompt)
    {
        this.prompt = prompt;
    }

    public String getPrompt()
    {
        return prompt;
    }
    public void setImage(String image)
    {
        this.image = image;
    }

    public String getImage()
    {
        return image;
    }
    public void setIsVaild(Long isVaild)
    {
        this.isVaild = isVaild;
    }

    public Long getIsVaild()
    {
        return isVaild;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("categoryLarge", getCategoryLarge())
                .append("categorySmall", getCategorySmall())
                .append("prompt", getPrompt())
                .append("image", getImage())
                .append("isVaild", getIsVaild())
                .toString();
    }
}
