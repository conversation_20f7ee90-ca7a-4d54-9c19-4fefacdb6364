package com.dataxai.web.service.impl;

import java.util.List;
import com.dataxai.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.AdminTaskMapper;
import com.dataxai.web.domain.AdminTask;
import com.dataxai.web.service.IAdminTaskService;

/**
 * 任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-29
 */
@Service
public class AdminTaskServiceImpl implements IAdminTaskService 
{
    @Autowired
    private AdminTaskMapper adminTaskMapper;

    /**
     * 查询任务
     * 
     * @param taskId 任务主键
     * @return 任务
     */
    @Override
    public AdminTask selectAdminTaskByTaskId(String taskId)
    {
        return adminTaskMapper.selectAdminTaskByTaskId(taskId);
    }

    /**
     * 查询任务列表
     * 
     * @param adminTask 任务
     * @return 任务
     */
    @Override
    public List<AdminTask> selectAdminTaskList(AdminTask adminTask)
    {
        return adminTaskMapper.selectAdminTaskList(adminTask);
    }

    /**
     * 新增任务
     * 
     * @param adminTask 任务
     * @return 结果
     */
    @Override
    public int insertAdminTask(AdminTask adminTask)
    {
        adminTask.setCreateTime(DateUtils.getNowDate());
        return adminTaskMapper.insertAdminTask(adminTask);
    }

    /**
     * 修改任务
     * 
     * @param adminTask 任务
     * @return 结果
     */
    @Override
    public int updateAdminTask(AdminTask adminTask)
    {
        adminTask.setUpdateTime(DateUtils.getNowDate());
        return adminTaskMapper.updateAdminTask(adminTask);
    }

    /**
     * 批量删除任务
     * 
     * @param taskIds 需要删除的任务主键
     * @return 结果
     */
    @Override
    public int deleteAdminTaskByTaskIds(String[] taskIds)
    {
        return adminTaskMapper.deleteAdminTaskByTaskIds(taskIds);
    }

    /**
     * 删除任务信息
     * 
     * @param taskId 任务主键
     * @return 结果
     */
    @Override
    public int deleteAdminTaskByTaskId(String taskId)
    {
        return adminTaskMapper.deleteAdminTaskByTaskId(taskId);
    }
}
