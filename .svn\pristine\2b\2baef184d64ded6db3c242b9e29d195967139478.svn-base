package com.dataxai.web.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.dataxai.web.mapper.TBaseinfoDisclaimersMapper;
import com.dataxai.web.domain.TBaseinfoDisclaimers;
import com.dataxai.web.service.ITBaseinfoDisclaimersService;

/**
 * 免责声明Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-02-28
 */
@Service
public class TBaseinfoDisclaimersServiceImpl implements ITBaseinfoDisclaimersService 
{
    @Autowired
    private TBaseinfoDisclaimersMapper tBaseinfoDisclaimersMapper;

    /**
     * 查询免责声明
     * 
     * @param id 免责声明主键
     * @return 免责声明
     */
    @Override
    public TBaseinfoDisclaimers selectTBaseinfoDisclaimersById(Long id)
    {
        return tBaseinfoDisclaimersMapper.selectTBaseinfoDisclaimersById(id);
    }

    /**
     * 查询免责声明列表
     * 
     * @param tBaseinfoDisclaimers 免责声明
     * @return 免责声明
     */
    @Override
    public List<TBaseinfoDisclaimers> selectTBaseinfoDisclaimersList(TBaseinfoDisclaimers tBaseinfoDisclaimers)
    {
        return tBaseinfoDisclaimersMapper.selectTBaseinfoDisclaimersList(tBaseinfoDisclaimers);
    }

    /**
     * 新增免责声明
     * 
     * @param tBaseinfoDisclaimers 免责声明
     * @return 结果
     */
    @Override
    public int insertTBaseinfoDisclaimers(TBaseinfoDisclaimers tBaseinfoDisclaimers)
    {
        return tBaseinfoDisclaimersMapper.insertTBaseinfoDisclaimers(tBaseinfoDisclaimers);
    }

    /**
     * 修改免责声明
     * 
     * @param tBaseinfoDisclaimers 免责声明
     * @return 结果
     */
    @Override
    public int updateTBaseinfoDisclaimers(TBaseinfoDisclaimers tBaseinfoDisclaimers)
    {
        return tBaseinfoDisclaimersMapper.updateTBaseinfoDisclaimers(tBaseinfoDisclaimers);
    }

    /**
     * 批量删除免责声明
     * 
     * @param ids 需要删除的免责声明主键
     * @return 结果
     */
    @Override
    public int deleteTBaseinfoDisclaimersByIds(Long[] ids)
    {
        return tBaseinfoDisclaimersMapper.deleteTBaseinfoDisclaimersByIds(ids);
    }

    /**
     * 删除免责声明信息
     * 
     * @param id 免责声明主键
     * @return 结果
     */
    @Override
    public int deleteTBaseinfoDisclaimersById(Long id)
    {
        return tBaseinfoDisclaimersMapper.deleteTBaseinfoDisclaimersById(id);
    }
}
