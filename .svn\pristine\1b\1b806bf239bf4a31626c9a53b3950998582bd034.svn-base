package com.dataxai.web.domain;

import com.dataxai.common.annotation.Excel;
import com.dataxai.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 标签对象 t_tag
 *
 * <AUTHOR>
 * @date 2024-02-23
 */
@Data
public class TTagDto
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 名称 */
    private String tagName;
}
