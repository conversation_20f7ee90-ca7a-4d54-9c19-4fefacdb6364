package com.dataxai.web.service;

import java.util.List;
import com.dataxai.web.domain.WebModelCharacterControl;

/**
 * 模特特征的可选项Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-11
 */
public interface IWebModelCharacterControlService 
{
    /**
     * 查询模特特征的可选项
     * 
     * @param id 模特特征的可选项主键
     * @return 模特特征的可选项
     */
    public WebModelCharacterControl selectWebModelCharacterControlById(Long id);

    /**
     * 查询模特特征的可选项列表
     * 
     * @param webModelCharacterControl 模特特征的可选项
     * @return 模特特征的可选项集合
     */
    public List<WebModelCharacterControl> selectWebModelCharacterControlList(WebModelCharacterControl webModelCharacterControl);

    /**
     * 新增模特特征的可选项
     * 
     * @param webModelCharacterControl 模特特征的可选项
     * @return 结果
     */
    public int insertWebModelCharacterControl(WebModelCharacterControl webModelCharacterControl);

    /**
     * 修改模特特征的可选项
     * 
     * @param webModelCharacterControl 模特特征的可选项
     * @return 结果
     */
    public int updateWebModelCharacterControl(WebModelCharacterControl webModelCharacterControl);

    /**
     * 批量删除模特特征的可选项
     * 
     * @param ids 需要删除的模特特征的可选项主键集合
     * @return 结果
     */
    public int deleteWebModelCharacterControlByIds(Long[] ids);

    /**
     * 删除模特特征的可选项信息
     * 
     * @param id 模特特征的可选项主键
     * @return 结果
     */
    public int deleteWebModelCharacterControlById(Long id);

    List<WebModelCharacterControl> selectWebModelCharacterControlDistinctList();
}
