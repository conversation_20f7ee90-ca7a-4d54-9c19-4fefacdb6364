<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dataxai.web.mapper.RefuelingBagMapper">
    
    <resultMap type="com.dataxai.web.domain.RefuelingBag" id="RefuelingBagResult">
        <result property="refuelingBagId"    column="refueling_bag_id"    />
        <result property="refuelingBagName"    column="refueling_bag_name"    />
        <result property="tariffPackageId"    column="tariff_package_id"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="userId"    column="user_id"    />
        <result property="score"    column="score"    />
        <result property="money"    column="money"    />
        <result property="cycle"    column="cycle"    />
        <result property="onSale"    column="on_sale"    />
    </resultMap>

    <sql id="selectRefuelingBagVo">
        select refueling_bag_id, tariff_package_id, start_time, end_time, user_id, score, money, cycle,on_sale,refueling_bag_name from t_refueling_bag
    </sql>

    <select id="selectRefuelingBagList" parameterType="com.dataxai.web.domain.RefuelingBag" resultMap="RefuelingBagResult">
        <include refid="selectRefuelingBagVo"/>
        <where>  
            <if test="tariffPackageId != null  and tariffPackageId != ''"> and tariff_package_id = #{tariffPackageId}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="score != null "> and score = #{score}</if>
            <if test="money != null "> and money = #{money}</if>
            <if test="cycle != null "> and cycle = #{cycle}</if>
            <if test="onSale != null "> and on_sale = #{onSale}</if>
            <if test="refuelingBagName != null "> and refueling_bag_name = #{refuelingBagName}</if>
        </where>
        order by score asc
    </select>

    <select id="selectBagByTariffPackageId" parameterType="string" resultMap="RefuelingBagResult">
        select refueling_bag_id, tariff_package_id, start_time, end_time, user_id, score, money, cycle, on_sale,refueling_bag_name from t_refueling_bag
         where tariff_package_id = #{tariffPackageId}
    </select>

    <select id="selectRefuelingBagByRefuelingBagId" parameterType="String" resultMap="RefuelingBagResult">
        <include refid="selectRefuelingBagVo"/>
        where refueling_bag_id = #{refuelingBagId}
    </select>
        
    <insert id="insertRefuelingBag" parameterType="RefuelingBag">
        insert into t_refueling_bag
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refuelingBagId != null">refueling_bag_id,</if>
            <if test="tariffPackageId != null">tariff_package_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="score != null">score,</if>
            <if test="money != null">money,</if>
            <if test="cycle != null">cycle,</if>
            <if test="onSale != null">on_sale,</if>
            <if test="refuelingBagName != null">refueling_bag_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refuelingBagId != null">#{refuelingBagId},</if>
            <if test="tariffPackageId != null">#{tariffPackageId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="score != null">#{score},</if>
            <if test="money != null">#{money},</if>
            <if test="cycle != null">#{cycle},</if>
            <if test="onSale != null">#{onSale},</if>
            <if test="refuelingBagName != null">#{refuelingBagName},</if>
         </trim>
    </insert>

    <update id="updateRefuelingBag" parameterType="RefuelingBag">
        update t_refueling_bag
        <trim prefix="SET" suffixOverrides=",">
            <if test="tariffPackageId != null">tariff_package_id = #{tariffPackageId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="score != null">score = #{score},</if>
            <if test="money != null">money = #{money},</if>
            <if test="cycle != null">cycle = #{cycle},</if>
            <if test="onSale != null">on_sale = #{onSale},</if>
            <if test="refuelingBagName != null">refueling_bag_name = #{refuelingBagName},</if>
        </trim>
        where refueling_bag_id = #{refuelingBagId}
    </update>

    <delete id="deleteRefuelingBagByRefuelingBagId" parameterType="String">
        delete from t_refueling_bag where refueling_bag_id = #{refuelingBagId}
    </delete>

    <delete id="deleteRefuelingBagByRefuelingBagIds" parameterType="String">
        delete from t_refueling_bag where refueling_bag_id in 
        <foreach item="refuelingBagId" collection="array" open="(" separator="," close=")">
            #{refuelingBagId}
        </foreach>
    </delete>
</mapper>